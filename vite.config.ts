import path from "path";
import type { PluginOption } from "vite";
import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import { VitePWA } from "vite-plugin-pwa";
import legacy from "@vitejs/plugin-legacy";
import svgLoader from "vite-svg-loader";
import vueJsx from "@vitejs/plugin-vue-jsx";
import { weimobUploadCdn } from "./scripts/uploadToCDN";
import react from '@vitejs/plugin-react';
import veauryVitePlugins from 'veaury/vite/index.js'

function setupPlugins(env: ImportMetaEnv): PluginOption[] {
	return [

		// vue(),
		// react({
		// 	include: ['**/*.react.tsx']
		// }),
		// vueJsx({
		// 	include: ["*.vue", "!**/*.react.tsx", "**/*.tsx"],
		// }),
		veauryVitePlugins({
      type: 'vue'
    }),
		env.VITE_GLOB_APP_PWA === "true" &&
			VitePWA({
				injectRegister: "auto",
				manifest: {
					name: "aiwork365",
					short_name: "aiwork365",
					icons: [
						{ src: "pwa-192x192.png", sizes: "192x192", type: "image/png" },
						{ src: "pwa-512x512.png", sizes: "512x512", type: "image/png" },
					],
				},
			}),
		svgLoader(),
		weimobUploadCdn(),
	];
}

export default defineConfig((env) => {
	const viteEnv = loadEnv(env.mode, process.cwd()) as unknown as ImportMetaEnv;
	return {
		// base:
		// 	env.mode == "development"
		// 		? ""
		// 		: env.mode === "qa"
		// 		? "https://cdn2.weimob.com/static/aiwork365-web-stc/build-qa"
		// 		: "https://cdn2.weimob.com/static/aiwork365-web-stc/build",
		resolve: {
			alias: {
				"@": path.resolve(process.cwd(), "src"),
			},
		},
		plugins: [
			setupPlugins(viteEnv),
			legacy({
				targets: ["chrome 70", "not IE 11", "> 1%"], // 需要兼容的目标列表,可以设置多个
				additionalLegacyPolyfills: ["regenerator-runtime/runtime"], // 面向IE11时需要此插件
				modernPolyfills: ["es/global-this", "es.object.has-own"],
				polyfills: [
					"es.symbol",
					"es.array.filter",
					"es.promise",
					"es.promise.finally",
					"es.promise.all-settled",
					"es/map",
					"es/set",
					"es.array.for-each",
					"es.object.define-properties",
					"es.object.define-property",
					"es.object.get-own-property-descriptor",
					"es.object.get-own-property-descriptors",
					"es.object.keys",
					"es.object.to-string",
					"web.dom-collections.for-each",
					"es.global-this",
					"esnext.string.match-all",
				],
			}),
		],
		css: {
			preprocessorOptions: {
				// scss: {
				// 	additionalData: `
				//     @import '@/ppt/assets/styles/variable.scss';
				//     @import '@/ppt/assets/styles/mixin.scss';
				//   `,
				// },
			},
		},
		server: {
			host: true,
			port: 1025,
			open: false,
			proxy: {
				'/api3/aiwork365/video': {
					target: "http://aiwrok365-video-biz-adapter.app.qa.internal.weimob.com",
					changeOrigin: true, // 允许跨域
					rewrite: (path) => {
						return path.replace("/api3/", "/");
					},
				},
				"/api3/aiwork365": {
					target:
						"http://aiwrok365-mj-adapter-service.app.qa.internal.weimob.com",
					changeOrigin: true,
					rewrite: (path) => {
						return path.replace("/api3/aiwork365/", "/aiwork365/");
					},
				},
				"/api3/aiwork": {
					target: "http://aiwork365-creation-node.app.qa.internal.weimob.com",
					changeOrigin: true, // 允许跨域
					rewrite: (path) => {
						return path.replace("/api3/aiwork/", "/aiwork/");
					},
				},
				"/api3/paper": {
					target: "http://aiwork365-paper-node.app.qa.internal.weimob.com",
					changeOrigin: true, // 允许跨域
					rewrite: (path) => {
						return path.replace("/api3/paper/", "/paper/");
					},
				},
				"/api3/ppt": {
					target: "http://aiwork365-ppt-node.app.qa.internal.weimob.com",
					changeOrigin: true, // 允许跨域
					rewrite: (path) => {
						return path.replace("/api3/ppt/", "/ppt/");
					},
				},
				"/api3/adtool": {
					target: "http://ad-tool-adapter-service.app.qa.internal.weimob.com",
					changeOrigin: true, // 允许跨域
					rewrite: (path) => {
						return path.replace("/api3/adtool/", "/adtool/");
					},
				},
				"/api3/ad": {
					target: "http://ad-common-component.app.qa.internal.weimob.com",
					changeOrigin: true, // 允许跨域
					rewrite: (path) => {
						return path.replace("/api3/ad/", "/ad/");
					},
				},
			},
		},
		build: {
			reportCompressedSize: false,
			sourcemap: false,
			commonjsOptions: {
				ignoreTryCatch: false,
			},
		},
	};
});
