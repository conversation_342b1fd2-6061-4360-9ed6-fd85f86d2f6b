/** @type {import('tailwindcss').Config} */
module.exports = {
	mode: "jit",
	darkMode: "class",
	content: ["./index.html", "./src/**/*.{vue,js,ts,jsx,tsx}"],
	theme: {
		extend: {
			colors: {
				primary: "#3363FF",
				title: "#3D3D3D",
				secondary: "#666",
				info: '#98A2B5'
			},
			backgroundImage: {
				"gradient-radial": "radial-gradient(var(--tw-gradient-stops))",
				"gradient-conic":
					"conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))",
				bgColor:
					"linear-gradient(180deg, #D4F7EC 0%, rgba(217, 217, 217, 0) 100%)",
				three:
					"linear-gradient(180deg, rgba(231, 255, 248, 0) 16.15%, #E8FFF8 100%)",
				four: "linear-gradient(180deg, #E7FFF8 16.15%, rgba(232, 255, 248, 0) 100%)",
				footerBg:
					"linear-gradient(180deg, rgba(231, 255, 248, 0) 16.15%, #E7FFF8 100%)",
				bottom:
					"linear-gradient(180deg, #E7FFF8 16.15%, rgba(231, 255, 248, 0) 100%) !important",
				btnBg:
					"linear-gradient(90deg,rgba(51,184,159,.8),rgba(51,184,159,.8) 80%,rgba(51,184,159,.8)),rgba(51,184,159,.8)",
				bgImage:
					"linear-gradient(98.75deg,rgba(179,179,252,.3) 2.58%,rgba(199,235,255,.3) 47.57%,rgba(199,255,199,.3) 98.55%)",
				bg2: "linear-gradient(180deg, #04AA7C 0%, #5fd8b3 100%)",
				profile: "linear-gradient(120deg,#fcf7d7,#ffefb4 20%,#f9dea2 90%)",
			},
			animation: {
				blink: "blink 1.2s infinite steps(1, start)",
			},
			keyframes: {
				blink: {
					"0%, 100%": { "background-color": "currentColor" },
					"50%": { "background-color": "transparent" },
				},
			},
		},
		boxShadow: {
			inner: "0px 0px 16px 0px rgba(62, 106, 244, 0.24)",
			inner2: "0px 0px 16px 0px #f8f7fb",
			border: "0 1px 1px 0 rgba(0,0,0,.05)",
			shadow1: "0px 10px 30px 0px rgba(82, 63, 105, .05)",
			inputShow: "false,0 0 0 .25rem #04c8c840",
			aside: "0px 10px 20px 0px rgba(82, 63, 105, .05)",
			floating: "0px 0px 10px 0px rgba(0,0,0,.2)",
			dropdown:
				"0 3px 6px -4px rgba(0, 0, 0, .12), 0 6px 16px 0 rgba(0, 0, 0, .08), 0 9px 28px 8px rgba(0, 0, 0, .05)",
		},
		screens: {
			sm: { max: "767px" },
			// => @media (min-width: 640px) { ... }

			md: { min: "768px" },
			// => @media (min-width: 768px and max-width: 1023px) { ... }

			lg: { min: "1024px" },
			// => @media (min-width: 1024px and max-width: 1279px) { ... }

			xl: { min: "1280px" },
			// => @media (min-width: 1280px and max-width: 1535px) { ... }

			"2xl": { min: "1536px" },
			// => @media (min-width: 1536px) { ... }
			"3xl": { min: "1920px" },
			ipad: { min: "768px", max: "1024px" },
		},
	},
	plugins: [
	],
};
