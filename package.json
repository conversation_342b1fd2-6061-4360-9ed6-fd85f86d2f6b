{"name": "aiwork365-creation-web", "version": "1.0.2", "private": false, "description": "creation Web", "author": "", "keywords": ["creation-web", "creation", "creation", "vue"], "scripts": {"ts": "node scripts/test.js", "dev": "vite", "build": "cross-env NODE_OPTIONS=--max_old_space_size=8096 vite build --mode online", "build:qa": "cross-env NODE_OPTIONS=--max_old_space_size=8096 vite build --mode qa", "build:online": "cross-env NODE_OPTIONS=--max_old_space_size=8096 vite build --mode online", "build1": "run-p type-check build-only", "build2": "cross-env NODE_OPTIONS=--max_old_space_size=8096 vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "lint": "eslint .", "lint:fix": "eslint . --fix", "bootstrap": "pnpm install && pnpm run common:prepare", "common:cleanup": "rimraf node_modules && rimraf pnpm-lock.yaml", "common:prepare": "husky install"}, "dependencies": {"@flaticon/flaticon-uicons": "^3.3.1", "@headlessui/vue": "^1.7.23", "@icon-park/vue-next": "^1.4.2", "@microsoft/fetch-event-source": "^2.0.1", "@tiptap/core": "^3.0.7", "@tiptap/extension-blockquote": "^3.0.7", "@tiptap/extension-bubble-menu": "3.0.7", "@tiptap/extension-collaboration": "3.0.7", "@tiptap/extension-color": "^3.0.7", "@tiptap/extension-drag-handle": "3.0.7", "@tiptap/extension-drag-handle-vue-3": "3.0.7", "@tiptap/extension-font-family": "^3.0.7", "@tiptap/extension-font-size": "3.0.0-next.3", "@tiptap/extension-node-range": "3.0.7", "@tiptap/extension-table-of-contents": "3.0.7", "@tiptap/extension-task-item": "^3.0.7", "@tiptap/extension-task-list": "^3.0.7", "@tiptap/extension-text-align": "^3.0.7", "@tiptap/extension-text-style": "^3.0.7", "@tiptap/extension-underline": "^3.0.7", "@tiptap/extensions": "3.0.7", "@tiptap/pm": "^3.0.7", "@tiptap/starter-kit": "^3.0.7", "@tiptap/suggestion": "^3.0.7", "@tiptap/vue-3": "^3.0.7", "@tiptap/y-tiptap": "3.0.0", "@traptitech/markdown-it-katex": "^3.6.0", "@types/lodash.clonedeep": "^4.5.9", "@vicons/ionicons5": "^0.12.0", "@vueuse/core": "^9.13.0", "aieditor": "1.1.0", "clipboard": "^2.0.11", "dayjs": "^1.11.13", "docx-preview": "^0.3.4", "esbuild": "0.24.0", "file-saver": "^2.0.5", "file-type": "^19.6.0", "highlight.js": "^11.10.0", "html2canvas": "^1.4.1", "install": "^0.13.0", "katex": "^0.16.15", "lax.js": "^2.0.3", "lodash": "^4.17.21", "lodash.clonedeep": "^4.5.0", "mammoth": "^1.8.0", "markdown-it": "^13.0.2", "markdown-to-image": "^0.0.12", "marked": "^14.1.4", "midjourney": "^2.7.79", "naive-ui": "2.40.3", "nanoid": "^5.0.9", "npm": "^11.4.2", "pdfjs-dist": "^4.9.155", "pdfmake": "0.2.4", "pinia": "2.0.33", "prosemirror-commands": "^1.6.2", "prosemirror-dropcursor": "^1.8.1", "prosemirror-gapcursor": "^1.3.2", "prosemirror-history": "^1.4.1", "prosemirror-inputrules": "^1.4.0", "prosemirror-keymap": "^1.2.2", "prosemirror-model": "^1.24.1", "prosemirror-schema-basic": "^1.2.3", "prosemirror-schema-list": "^1.5.0", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.37.0", "qrcode.vue": "^3.6.0", "react": "^18.3.1", "react-dom": "^18.3.1", "remark": "^15.0.1", "remark-docx": "^0.1.6", "remark-frontmatter": "^5.0.0", "remark-gfm": "^4.0.0", "remark-parse": "^11.0.0", "remark-pdf": "^0.0.14", "tippy.js": "^6.3.7", "tiptap-markdown": "^0.8.10", "unified": "^11.0.5", "veaury": "^2.6.1", "vite-svg-loader": "^5.1.0", "vue": "^3.5.13", "vue-clipboard3": "^2.0.0", "vue-countup-v3": "^1.4.2", "vue-hooks-plus": "2.2.3", "vue-i18n": "^9.14.2", "vue-router": "^4.5.0", "vue-waterfall-plugin-next": "^2.6.4", "vue3-carousel": "^0.16.0", "y-protocols": "^1.0.6", "yargs-parser": "^21.1.1", "yjs": "^13.6.27"}, "devDependencies": {"@antfu/eslint-config": "^0.35.3", "@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@iconify/vue": "^4.2.0", "@types/crypto-js": "^4.2.2", "@types/katex": "^0.16.7", "@types/markdown-it": "^12.2.3", "@types/markdown-it-link-attributes": "^3.0.5", "@types/node": "^18.19.67", "@types/react": "^19.0.6", "@types/react-dom": "^19.0.3", "@vicons/fluent": "^0.12.0", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-react": "^4.3.4", "@vitejs/plugin-vue": "^4.6.2", "@vitejs/plugin-vue-jsx": "^4.1.1", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "eslint": "^9.16.0", "husky": "^8.0.3", "less": "^4.2.1", "lint-staged": "^13.3.0", "markdown-it-link-attributes": "^4.0.1", "npm-run-all": "^4.1.5", "postcss": "^8.4.49", "rimraf": "^4.4.1", "semver": "^7.6.3", "simple-git": "^3.27.0", "tailwindcss": "3.4.16", "terser": "^5.37.0", "typescript": "~5.5.4", "vite": "^6.0.3", "vite-plugin-pwa": "^0.14.7", "vue-tsc": "^2.1.10", "weimob-uploadtocdn-core": "^0.1.27"}, "lint-staged": {"*.{ts,tsx,vue}": ["pnpm lint:fix"]}, "packageManager": "pnpm@10.4.1+sha512.c753b6c3ad7afa13af388fa6d808035a008e30ea9993f58c6663e2bc5ff21679aa834db094987129aa4d488b86df57f7b634981b2f827cdcacc698cc0cfb88af", "pnpm": {"ignoredBuiltDependencies": ["core-js", "esbuild", "vue-demi"], "onlyBuiltDependencies": ["core-js"]}}