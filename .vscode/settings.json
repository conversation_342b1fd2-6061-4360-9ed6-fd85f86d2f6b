{"cSpell.words": ["aieditor", "aigc", "aiwork", "<PERSON>r", "Chatbots", "<PERSON><PERSON><PERSON>", "Createbots", "Deepseek", "EVAS", "FINDPASSWORD", "ispay", "pdfjs", "PDFMERGE", "PDFSPLIT", "pinia", "Popconfirm", "qrcode", "scenedetail", "<PERSON><PERSON>", "sugges", "temptype", "uncollect", "vicons", "VITE", "vueuse", "<PERSON><PERSON>", "xmark"], "svg.preview.background": "editor", "Codegeex.RepoIndex": true, "Codegeex.GenerationPreference": "line by line", "gitlens.ai.generateCommitMessage.customInstructions": "# 提交信息 Conventional Commits 指令  请使用 Conventional Commits 格式生成提交信息。  **主题行规则：** - 必须使用祈使句（动词原形开头，如 \"添加功能\" 而不是 \"已添加功能\"）。 - 长度应在 50 个字符以内。 - 不以句号结尾。  **正文规则：** - 主题行和正文之间留一个空行。 - 正文每行应在 72 个字符处换行。 - 解释为什么进行此更改、解决了什么问题以及任何重要的附带影响或后果。 - 不要解释你是如何进行更改的（那是代码的作用）。  **常用类型：** - `feat`: 新增功能 - `fix`: 修复 Bug - `docs`: 仅文档更改 - `style`: 代码格式或风格调整（不影响代码逻辑） - `refactor`: 代码重构（不增加功能也不修复 Bug） - `test`: 添加或更新测试 - `chore`: 构建过程或辅助工具的更改 - `perf`: 性能改进 - `ci`: 持续集成配置更改 - `build`: 构建系统或外部依赖更改 - `revert`: 回滚之前的提交"}