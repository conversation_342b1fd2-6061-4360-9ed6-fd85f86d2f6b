<template>
	<div v-if="
		currentConfig.officeType === OfficeType.PDFSPLIT ||
		currentConfig.officeType === OfficeType.PDFMERGE ||
		currentConfig.officeType === OfficeType.PDF2WORD ||
		currentConfig.officeType === OfficeType.PDF2EXCEL ||
		currentConfig.officeType === OfficeType.PDF2PPT ||
		currentConfig.officeType === OfficeType.WORD2PDF ||
		currentConfig.officeType === OfficeType.EXCEL2PDF ||
		currentConfig.officeType === OfficeType.PPT2PDF ||
		currentConfig.officeType === OfficeType.IMG2WORD ||
		currentConfig.officeType === OfficeType.IMG2EXCEL ||
		currentConfig.officeType === OfficeType.IMG2PPT ||
		currentConfig.officeType === OfficeType.IMG2PDF ||
		currentConfig.officeType === OfficeType.IMGCOMPRESS
	" :class="[
		{
			'office-container': [
				OfficeType.PDFSPLIT,
				OfficeType.PDFMERGE,
				OfficeType.PDF2WORD,
				OfficeType.PDF2EXCEL,
				OfficeType.PDF2PPT,
				OfficeType.WORD2PDF,
				OfficeType.EXCEL2PDF,
				OfficeType.PPT2PDF,
				OfficeType.IMG2WORD,
				OfficeType.IMG2EXCEL,
				OfficeType.IMG2PPT,
				OfficeType.IMG2PDF,
				OfficeType.IMGCOMPRESS
			].includes(currentConfig.officeType),
		}
	]">
		<NSpin :show="uploadFileLoading || fetchViewPdfLoading || checkPointLoading">
			<LeftContent :title="currentConfig.title" :subtitle="currentConfig.subtitle" :icon="currentConfig.icon"
				:footer="currentConfig.footer" :accept="currentConfig.accept" :hide-title="hideTitle">
				<div class="dotted-border flex-1 ">
					<Upload ref="uploadRef" :content-mode="currentConfig.contentMode" :value="fileList"
						:upload-title="currentConfig.uploadTitle" :upload-subtitle="currentConfig.uploadSubtitle"
						:accept="currentConfig.accept" :size="currentConfig.size" :limit="currentConfig.limit"
						@update:value="handleFileList" v-if="!fileList.length" />
					<template v-else>
						<FileList v-if="currentConfig.contentMode === CONTENT_MODE.FILE_LIST" :file-list="fileList"
							:is-border-dashed="false" :show-options="false" :value="fileList" @update:value="handleFileList" />
						<FileSplit v-else-if="currentConfig.contentMode === CONTENT_MODE.FILE_LIST_SPLIT && fileList.length"
							:file-list="fileList" :pages="pages" :split-type="splitType" :page-number="pageNumber"
							:start-page="startPage" :end-page="endPage" @update:checked="handleUpdateChecked"
							:group-pages="groupPages" />
					</template>
				</div>
			</LeftContent>
		</NSpin>
		<RightContent :config="currentConfig" :end-page="endPage" @update:form="handleUpdateForm"></RightContent>
		<!-- 隐藏的文件输入框 -->
		<input type="file" ref="fileInputRef" style="display: none;" :accept="currentConfig.accept"
			@change="handleFileInputChange" :multiple="currentConfig.limit > 1" />
	</div>
	<div v-else-if="currentConfig.officeType === OfficeType.FINDPASSWORD" class="office-col-container">
		<TopContent :title="currentConfig.topTitle" :sub-title="currentConfig.topSubtitle" />
		<div class="flex flex-row gap-x-[12px]">
			<LeftContent :title="currentConfig.title" :subtitle="currentConfig.subtitle" :icon="currentConfig.icon"
				:footer="currentConfig.footer" :accept="currentConfig.accept" :hide-title="hideTitle">
				<div class="dotted-border flex-1">
					<Upload ref="uploadRef" :content-mode="currentConfig.contentMode" :value="fileList"
						:upload-title="currentConfig.uploadTitle" :upload-subtitle="currentConfig.uploadSubtitle"
						:accept="currentConfig.accept" :size="currentConfig.size" :limit="currentConfig.limit"
						@update:value="handleFileList" v-if="!fileList.length" />
					<FileCrack :task-id="taskId" :status="crackStatus" :file-list="fileList" :is-border-dashed="false"
						:show-options="false" :value="fileList" @update:value="handleFileList" v-if="fileList.length"
						:show-modal="showCrackModal" :show-phone-modal="showCrackPhoneModal" @update:showModal="handleShowModal"
						@update:clickCopy="handleClickCopy" @delete="handleFileDelete" @phone-submit="handlePhoneSubmit"
						:file-cracked="fileCracked" />
				</div>
			</LeftContent>
			<RightContent :config="currentConfig" :end-page="endPage" @update:form="handleUpdateForm"></RightContent>
		</div>
	</div>
	<div v-else-if="currentConfig.officeType === OfficeType.DOC2TRANSLATE" class="office-col-container">
		<BottomContent :title="currentConfig.title" :subtitle="currentConfig.subtitle" :icon="currentConfig.icon"
			:footer="currentConfig.footer" :accept="currentConfig.accept" :hide-title="hideTitle" alternateContent="替代内容">
			<template #alternate-header>
				<div class="custom-alternate flex flex-row gap-4">
					<LanguageTransfer v-model:sourceLang="form.sourceLang" v-model:targetLang="form.targetLang"
						:defaultSourceLang="form.sourceLang" :defaultTargetLang="form.targetLang" />
					<TranslateOptions v-model:transType="form.transType" v-model:transOutType="form.transOutType"
						v-model:transLayout="form.transLayout" />
				</div>
			</template>
			<div class="dotted-border flex-1 flex flex-row justify-center items-center">
				<FileTranslate :file-list="fileList" :is-border-dashed="false" :show-options="false"
					@update:value="handleFileList" @delete="handleFileDelete" />
				<div class="h-[calc(100%-40px)] w-[4px] dotted-border-left my-[20px]"></div>
				<Upload ref="uploadRef" :content-mode="currentConfig.contentMode" :value="fileList"
					:upload-title="currentConfig.uploadTitle" :upload-subtitle="currentConfig.uploadSubtitle"
					:accept="currentConfig.accept" :size="currentConfig.size" :show-after-upload="true"
					:limit="currentConfig.limit" @update:value="handleFileList" />
			</div>
		</BottomContent>
	</div>
</template>
<script setup lang="ts">
import { computed, ref, watch, onMounted, onBeforeUnmount } from 'vue';
import { useRoute } from 'vue-router';
import { icons } from '@/plugins/icons';
import { CheckedPage, CONTENT_MODE, ImageFitOption, imageFitOptionList, fileTransformOption, OfficeType, Page, SplitType, TRANSFORM_TYPE } from './types';
import { ls } from '@/utils/storage/local';
import PDF2WordIcon from '@/assets/office/icon/pdf2wordicon.png';
import PDF2ExcelIcon from '@/assets/office/icon/pdf2excelicon.png';
import PDF2PPTIcon from '@/assets/office/icon/pdf2ppticon.png';
import PDFMergeIcon from '@/assets/office/icon/pdfmergeicon.png';
import PDFSplitIcon from '@/assets/office/icon/pdfspliticon.png';
import Img2WordIcon from '@/assets/office/icon/image2wordicon.png';
import Img2ExcelIcon from '@/assets/office/icon/image2excelicon.png'
import Img2PPTIcon from '@/assets/office/icon/ppt2pdficon.png'; // Temporary icon for Image2PPT
import Img2PDFIcon from '@/assets/office/icon/image2pdficon.png'
import ImgCompressIcon from '@/assets/office/icon/imagecompressicon.png'
import Word2PdfIcon from '@/assets/office/icon/word2pdficon.png';
import Excel2PdfIcon from '@/assets/office/icon/excel2pdficon.png';
import Ppt2PdfIcon from '@/assets/office/icon/ppt2pdficon.png';
import FindPasswordIcon from '@/assets/office/icon/findpasswordicon.png';
import TranslateIcon from '@/assets/office/icon/translate-icon.png';

import LeftContent from './components/left-content.vue';
import RightContent from './components/right-content.vue';

import TopContent from './components/top-content.vue';
import BottomContent from './components/bottom-content.vue';

import Upload from './components/upload.vue';
import { UploadFileInfo, NSpin, useMessage } from 'naive-ui';
import FileList from './components/file-list.vue';
import FileSplit from './components/file-split.vue';
import FileCrack from './components/file-crack.vue';
import FileTranslate from './components/file-translate.vue';
import TranslateOptions from './components/translate-options.vue';
import LanguageTransfer from './components/language-transfer.vue';
import { useUploadFile, useAITools } from './hooks';
import { useRequest } from 'vue-hooks-plus';
import { checkBindPhone, fetchViewPdf, getPassword, getPasswordTask, getPdfUsePoint, getOfficeUsePoint, getImageUsePoint, getDocTranslateUsePoint } from './apis';
import { router } from '@/router';
import { copyPassword } from '@/utils/clipboard';
const message = useMessage();
const { IconHistory, IconImport } = icons;
// 获取路由参数 /office/pdf2word 用于判断是哪个功能
const route = useRoute();

// 积分相关变量
const pointValue = ref<number>(0);
const checkPointLoading = ref(false);

const form = ref<Record<string, any>>({
	transType: 'trans_text',
	transOutType: 'only',
	transLayout: 'new',
	sourceLang: '中文',
	targetLang: '英语',
	quality: 60
});
const fileList = computed(() => uploadFileData.value || []);
const pages = ref<CheckedPage[]>([]);
const uploaded = computed(() => uploadFileData.value?.length > 0);
const progress = ref<number>(0);
const splitType = ref<number>(3);
const pageNumber = ref<number>(2); // 设置默认值为 2
const startPage = ref<number>(1);
const endPage = ref<number>(1);

const pageMin = ref<number>(1);
const pageMax = ref<number>(1);

const crackLoading = ref<boolean>(false);
const crackStatus = ref<null | 'pending' | 'success' | 'fail'>(null);
// 单次失败后弹出加强的密码破解付费
const showCrackModal = ref<boolean>(false);
// 单次成功后弹出绑定phone 的框 绑定后再收费
const showCrackPhoneModal = ref<boolean>(false);
const taskId = ref<number | null>(null);
const fileCracked = ref<boolean>(false);

watch(() => pages.value, (newPages) => {
	if (newPages?.length) {
		endPage.value = newPages.length;
	}
}, { immediate: true });

const hideTitle = computed(() => {
	return fileList.value.length > 0;
})

const uploadRef = ref<InstanceType<typeof Upload> | null>(null);

const triggerUpload = () => {
	// 触发隐藏的文件输入框
	if (fileInputRef.value) {
		fileInputRef.value.click();
	}
};

const handleShowModal = ($event) => {
	showCrackModal.value = $event
	fileCracked.value = false
}

// 根据上传文件的扩展名确定可选择的文件类型
const availableFileTypes = computed(() => {
	if (!uploadFileData.value || uploadFileData.value.length === 0) {
		return fileTransformOption; // 如果没有上传文件，返回所有选项
	}

	const fileName = uploadFileData.value[0].name;
	const fileExtension = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();

	// 根据文件扩展名确定文件类型
	if (fileExtension === '.doc' || fileExtension === '.docx') {
		return fileTransformOption.filter(item => item.value === 'word');
	} else if (fileExtension === '.xls' || fileExtension === '.xlsx') {
		return fileTransformOption.filter(item => item.value === 'excel');
	} else if (fileExtension === '.ppt' || fileExtension === '.pptx') {
		return fileTransformOption.filter(item => item.value === 'ppt');
	} else if (fileExtension === '.pdf') {
		return fileTransformOption.filter(item => item.value === 'pdf');
	}

	return fileTransformOption; // 默认返回所有选项
});

// 首先定义一些计算属性
const splitTypeText = computed(() => {
	if (!pages.value?.length) return '';

	switch (splitType.value) {
		case 1: {
			const start = startPage.value || 1;
			const end = endPage.value || pages.value.length;
			return `<span>将从第 <span class="text-[#0E69FF]">${start}</span> 页到第 <span class="text-[#0E69FF]">${end}</span> 页提取为新的PDF文件</span>`;
		}
		case 2: {
			// 确保 pageNumber 有值且大于 0
			const pn = pageNumber?.value || 2;
			const totalPages = pages.value.length;
			const totalFiles = Math.ceil(totalPages / pn);
			return `<span>该PDF文件将会被拆分为 <span class="text-[#0E69FF]">${pn}页</span> 的多个文件。将创建 <span class="text-[#0E69FF]">${totalFiles}份</span> PDF文件</span>`;
		}
		case 3:
			return `<span>该PDF文件将被拆分为 <span class="text-[#0E69FF]">${pages.value.length}</span> 个单页文件</span>`;
		default:
			return '';
	}
});

// 计算按钮显示文本的computed属性 - 支持所有操作按钮类型（排除翻译和找回密码）
const buttonDisplayText = computed(() => {
	return (originalText: string) => {
		if (pointValue.value > 0) {
			return `${originalText}  (${pointValue.value}积分)`;
		}
		return originalText;
	};
});

// 为向后兼容保留原有的计算属性
const convertButtonText = computed(() => buttonDisplayText.value('立即转换'));
const mergeButtonText = computed(() => buttonDisplayText.value('立即合并'));
const splitButtonText = computed(() => buttonDisplayText.value('立即拆分'));
const translateButtonText = computed(() => buttonDisplayText.value('翻译'));

const TRANSLATEConfig = ref({
	title: '文档翻译',
	subtitle: '为您提供专业的文档翻译服务，支持格式.docx/.xlsx/.pptx/.pdf/.txt/.csv/.md，文件≤10MB',
	icon: TranslateIcon,
	officeType: OfficeType.DOC2TRANSLATE,
	accept: '.docx,.xlsx,.ppt,.pptx,.pdf,.txt,.csv,.md',
	limit: 1,
	size: 10,
	contentMode: CONTENT_MODE.FILE_LIST,
	uploadTitle: '拖拽文件到这里，或者选择导入文件',
	uploadSubtitle: '导入本地文件',
	footer: [
		{
			type: 'button',
			icon: IconHistory,
			label: '创作记录',
			className: 'w-[126px] h-[62px] leading-[62px] text-center bg-white border border-[#333333] rounded-[10px] text-[14px] text-[#333333] box-border flex-shrink-0',
			showAfterImport: false,
			onClick: commonHistory
		},
		{
			type: 'button',
			label: computed(() => translateButtonText.value),
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-[linear-gradient(90deg,#009DFF_0%,#7E4AFF_100%)] rounded-[10px] text-[14px] text-white box-border flex-shrink-0 ml-[auto]',
			showAfterImport: false,
			disabled: computed(() => fileList.value.length === 0),
			onClick: commonTransform
		}
	]
})

const FINDPASSWORDConfig = ref({
	title: '密码找回',
	subtitle: '支持找回文件类型：Excel、Word、PPT，文件≤100MB',
	topTitle: '免责声明 （必读条款）',
	topSubtitle: '委托本站提供的相关服务须要确保您不违反中华人民共和国或您所在国家相关的著作权、知识产权、密码法等相关的法律法规。此外，您须要保证你是该文件的合法占有人，知识产权及著作权人的授权使用人。不得侵犯他人隐私和商业秘密！不得利用该服务从事违法行为！上传文件视为同意“免责声明”条款。违反法律法规造成的法律后果由您自行承担。',
	officeType: OfficeType.FINDPASSWORD,
	icon: FindPasswordIcon,
	accept: '.doc,.docx,.xls,.xlsx,.ppt,.pptx',
	uploadTitle: '拖拽文件到这里，或者选择导入文件',
	uploadSubtitle: '导入本地文件',
	limit: 1,
	size: 100,
	contentMode: CONTENT_MODE.FILE_LIST,
	footer: [
		{
			type: 'button',
			icon: IconHistory,
			label: '创作记录',
			className: 'w-[126px] h-[62px] leading-[62px] text-center bg-white border border-[#333333] rounded-[10px] text-[14px] text-[#333333] box-border flex-shrink-0',
			showAfterImport: false,
			onClick: commonHistory
		},
		{
			type: 'button',
			label: '找回密码',
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-[linear-gradient(90deg,#009DFF_0%,#7E4AFF_100%)] rounded-[10px] text-[14px] text-white box-border ml-[auto]',
			showAfterImport: false,
			disabled: computed(() => fileList.value.length === 0 || crackStatus.value === 'pending'),
			onClick: crackFile
		}
	],
	config: [
		{
			type: 'header',
			label: '破解格式',
			options: [
				{
					type: 'buttons',
					path: 'fileType',
					label: availableFileTypes,
				}
			]
		}
	]
})

const PDF2WORDConfig = ref({
	title: 'PDF转Word',
	subtitle: '轻松将PDF文件转换为可编辑的doc和docx文件',
	officeType: OfficeType.PDF2WORD,
	icon: PDF2WordIcon,
	accept: '.pdf,.PDF',
	uploadTitle: '拖拽PDF文件到这里，或者选择导入PDF文件',
	uploadSubtitle: '导入本地PDF文件',
	limit: 1,
	size: 10,
	contentMode: CONTENT_MODE.FILE_LIST,
	footer: [
		{
			type: 'button',
			icon: IconHistory,
			label: '创作记录',
			className: 'w-[126px] h-[62px] leading-[62px] text-center bg-white border border-[#333333] rounded-[10px] text-[14px] text-[#333333] box-border flex-shrink-0',
			showAfterImport: false,
			onClick: commonHistory
		},
		{
			type: 'button',
			icon: IconImport,
			label: '导入本地文件',
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-white border border-[#0060FF] rounded-[10px] text-[14px] text-[#0060FF] box-border flex-shrink-0',
			showAfterImport: true,
			onClick: triggerUpload
		},
		{
			type: 'button',
			label: computed(() => convertButtonText.value),
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-[linear-gradient(90deg,#009DFF_0%,#7E4AFF_100%)] rounded-[10px] text-[14px] text-white box-border flex-shrink-0 ml-[auto]',
			showAfterImport: false,
			disabled: computed(() => fileList.value.length === 0),
			onClick: commonTransform
		}
	],
	config: [
		{
			type: 'header',
			label: '转换格式',
			options: [
				{
					type: 'buttons',
					name: 'transformType',
					path: 'transformType',
					label: [TRANSFORM_TYPE.WORD],
				}
			]
		}
	]
})
const PDF2EXCELConfig = ref({
	title: 'PDF转Excel',
	subtitle: '将PDF文件转换为Excel文档',
	officeType: OfficeType.PDF2EXCEL,
	icon: PDF2ExcelIcon,
	accept: '.pdf,.PDF',
	uploadTitle: '拖拽PDF文件到这里，或者选择导入PDF文件',
	uploadSubtitle: '导入本地PDF文件',
	limit: 1,
	size: 10,
	contentMode: CONTENT_MODE.FILE_LIST,
	footer: [
		{
			type: 'button',
			icon: IconHistory,
			label: '创作记录',
			className: 'w-[126px] h-[62px] leading-[62px] text-center bg-white border border-[#333333] rounded-[10px] text-[14px] text-[#333333] box-border flex-shrink-0',
			showAfterImport: false,
			onClick: commonHistory
		},
		{
			type: 'button',
			icon: IconImport,
			label: '导入本地文件',
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-white border border-[#0060FF] rounded-[10px] text-[14px] text-[#0060FF] box-border flex-shrink-0',
			showAfterImport: true,
			onClick: triggerUpload
		},
		{
			type: 'button',
			label: computed(() => convertButtonText.value),
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-[linear-gradient(90deg,#009DFF_0%,#7E4AFF_100%)] rounded-[10px] text-[14px] text-white box-border flex-shrink-0 ml-[auto]',
			showAfterImport: false,
			disabled: computed(() => fileList.value.length === 0),
			onClick: commonTransform
		}
	],
	config: [
		{
			type: 'header',
			label: '转换格式',
			options: [
				{
					type: 'buttons',
					name: 'transformType',
					path: 'transformType',
					label: [TRANSFORM_TYPE.EXCEL],
				}
			]
		}
	]
})
const PDF2PPTConfig = ref({
	title: 'PDF转PPT',
	subtitle: '将PDF文件转换为PPT文档',
	officeType: OfficeType.PDF2PPT,
	icon: PDF2PPTIcon,
	accept: '.pdf,.PDF',
	uploadTitle: '拖拽PDF文件到这里，或者选择导入PDF文件',
	uploadSubtitle: '导入本地PDF文件',
	limit: 1,
	size: 10,
	contentMode: CONTENT_MODE.FILE_LIST,
	footer: [
		{
			type: 'button',
			icon: IconHistory,
			label: '创作记录',
			className: 'w-[126px] h-[62px] leading-[62px] text-center bg-white border border-[#333333] rounded-[10px] text-[14px] text-[#333333] box-border flex-shrink-0',
			showAfterImport: false,
			onClick: commonHistory
		},
		{
			type: 'button',
			icon: IconImport,
			label: '导入本地文件',
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-white border border-[#0060FF] rounded-[10px] text-[14px] text-[#0060FF] box-border flex-shrink-0',
			showAfterImport: true,
			onClick: triggerUpload
		},
		{
			type: 'button',
			label: computed(() => convertButtonText.value),
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-[linear-gradient(90deg,#009DFF_0%,#7E4AFF_100%)] rounded-[10px] text-[14px] text-white box-border flex-shrink-0 ml-[auto]',
			showAfterImport: false,
			disabled: computed(() => fileList.value.length === 0),
			onClick: commonTransform
		}
	],
	config: [
		{
			type: 'header',
			label: '转换格式',
			options: [
				{
					type: 'buttons',
					name: 'transformType',
					path: 'transformType',
					label: [TRANSFORM_TYPE.PPT],
				}
			]
		}
	]
})
const PDFMERGEConfig = ref({
	title: 'PDF合并',
	subtitle: '可拖动文件调整文件之间的顺序',
	officeType: OfficeType.PDFMERGE,
	icon: PDFMergeIcon,
	accept: '.pdf,.PDF',
	uploadTitle: '拖拽PDF文件到这里，或者选择导入PDF文件',
	uploadSubtitle: '导入本地PDF文件',
	limit: 10,
	size: 10,
	contentMode: CONTENT_MODE.FILE_LIST,
	footer: [
		{
			type: 'button',
			icon: IconHistory,
			label: '创作记录',
			className: 'w-[126px] h-[62px] leading-[62px] text-center bg-white border border-[#333333] rounded-[10px] text-[14px] text-[#333333] box-border flex-shrink-0',
			showAfterImport: false,
			onClick: commonHistory
		},
		{
			type: 'button',
			icon: IconImport,
			label: '导入本地文件',
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-white border border-[#0060FF] rounded-[10px] text-[14px] text-[#0060FF] box-border flex-shrink-0',
			showAfterImport: computed(() => fileList.value.length <= 0),
			onClick: triggerUpload
		},
		{
			type: 'button',
			label: computed(() => mergeButtonText.value),
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-[linear-gradient(90deg,#009DFF_0%,#7E4AFF_100%)] rounded-[10px] text-[14px] text-white box-border flex-shrink-0 ml-[auto]',
			showAfterImport: false,
			disabled: computed(() => fileList.value.length === 0),
			onClick: commonTransform
		}
	],
	config: [
		{
			type: 'header',
			label: '转换格式',
			options: [
				{
					type: 'buttons',
					name: 'transformType',
					path: 'transformType',
					label: [TRANSFORM_TYPE.PDF],
				}
			]
		}
	]
})
const PDFSPLITConfig = ref({
	title: 'PDF拆分',
	subtitle: '您可自由拆分PDF文件',
	officeType: OfficeType.PDFSPLIT,
	icon: PDFSplitIcon,
	accept: '.pdf',
	uploadTitle: '拖拽PDF文件到这里，或者选择导入PDF文件',
	uploadSubtitle: '导入本地PDF文件',
	limit: 1,
	size: 10,
	contentMode: CONTENT_MODE.FILE_LIST_SPLIT,
	footer: [
		{
			type: 'button',
			icon: IconHistory,
			label: '创作记录',
			className: 'w-[126px] h-[62px] leading-[62px] text-center bg-white border border-[#333333] rounded-[10px] text-[14px] text-[#333333] box-border flex-shrink-0',
			showAfterImport: false,
			onClick: commonHistory
		},
		{
			type: 'button',
			icon: IconImport,
			label: '导入本地文件',
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-white border border-[#0060FF] rounded-[10px] text-[14px] text-[#0060FF] box-border flex-shrink-0',
			showAfterImport: true,
			onClick: triggerUpload
		},
		{
			type: 'button',
			label: computed(() => splitButtonText.value),
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-[linear-gradient(90deg,#009DFF_0%,#7E4AFF_100%)] rounded-[10px] text-[14px] text-white box-border flex-shrink-0 ml-[auto]',
			showAfterImport: false,
			disabled: computed(() => fileList.value.length === 0),
			onClick: commonTransform
		}
	],
	config: [
		{
			type: 'header',
			label: '导出格式',
			options: [
				{
					type: 'buttons',
					name: 'transformType',
					path: 'transformType',
					label: [TRANSFORM_TYPE.PDF],
				}
			]
		},
		{
			type: 'header',
			label: '高级设置',
			options: [
				{
					type: 'checkboxGroup',
					path: 'splitType',
					options: [
						{
							value: 1,
							label: '按页码范围拆分',
							children: [
								{
									type: 'inputGroup',
									items: [
										{ type: 'text', content: '从页数：' },
										{
											type: 'inputNumber',
											path: 'startPage',
											placeholder: '请输入起始页',
											defaultValue: 1,
											pageMin: computed(() => pageMin.value),
											pageMax: computed(() => pages.value.length || 1),
										},
										{ type: 'text', content: '至' },
										{
											type: 'inputNumber',
											path: 'endPage',
											placeholder: '请输入结束页',
											defaultValue: computed(() => pages.value?.length || 1),
											pageMin: computed(() => pageMin.value),
											pageMax: computed(() => pages.value.length || 1),
										}
									]
								},
								{
									type: 'html',
									show: computed(() => pages.value?.length > 0 && splitType.value === 1),
									content: computed(() => splitTypeText.value)
								}
							]
						},
						{
							value: 2,
							label: '按间距拆分',
							children: [
								{
									type: 'inputGroup',
									items: [
										{ type: 'text', content: '页面间距：' },
										{
											type: 'inputNumber',
											path: 'pageNumber',
											placeholder: '请输入页码间距',
											defaultValue: 2,
											pageMin: 2,
											pageMax: computed(() => pages.value?.length || 1),
										}
									]
								},
								{
									type: 'html',
									show: computed(() => pages.value?.length > 0 && splitType.value === 2),
									content: computed(() => splitTypeText.value)
								}
							]
						},
						{
							value: 3,
							label: '一键拆分所有页面',
							children: [
								{
									type: 'html',
									show: computed(() => pages.value?.length > 0 && splitType.value === 3),
									content: computed(() => splitTypeText.value)
								}
							]
						}
					]
				}
			]
		}
	]
});

const WORD2PDFConfig = ref({
	title: 'Word转PDF',
	subtitle: '轻松将Word文件转换为不易篡改的PDF',
	officeType: OfficeType.WORD2PDF,
	icon: Word2PdfIcon,
	accept: '.doc,.docx',
	uploadTitle: '拖拽Word文件到这里，或者选择导入Word文件',
	uploadSubtitle: '导入本地Word文件',
	limit: 1,
	size: 10,
	contentMode: CONTENT_MODE.FILE_LIST,
	footer: [
		{
			type: 'button',
			icon: IconHistory,
			label: '创作记录',
			className: 'w-[126px] h-[62px] leading-[62px] text-center bg-white border border-[#333333] rounded-[10px] text-[14px] text-[#333333] box-border flex-shrink-0',
			showAfterImport: false,
			onClick: commonHistory
		},
		{
			type: 'button',
			icon: IconImport,
			label: '导入本地文件',
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-white border border-[#0060FF] rounded-[10px] text-[14px] text-[#0060FF] box-border flex-shrink-0',
			showAfterImport: true,
			onClick: triggerUpload
		},
		{
			type: 'button',
			label: computed(() => convertButtonText.value),
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-[linear-gradient(90deg,#009DFF_0%,#7E4AFF_100%)] rounded-[10px] text-[14px] text-white box-border flex-shrink-0 ml-[auto]',
			showAfterImport: false,
			disabled: computed(() => fileList.value.length === 0),
			onClick: commonTransform
		}
	],
	config: [
		{
			type: 'header',
			label: '转换格式',
			options: [
				{
					type: 'buttons',
					name: 'transformType',
					path: 'transformType',
					label: [TRANSFORM_TYPE.PDF],
				}
			]
		}
	]
})

const EXCEL2PDFConfig = ref({
	title: 'Excel转PDF',
	subtitle: '轻松将Excel文件转换为不易篡改的PDF',
	officeType: OfficeType.EXCEL2PDF,
	accept: '.xlsx,.xls',
	icon: Excel2PdfIcon,
	uploadTitle: '拖拽Excel文件到这里，或者选择导入Excel文件',
	uploadSubtitle: '导入本地Excel文件',
	limit: 1,
	size: 10,
	contentMode: CONTENT_MODE.FILE_LIST,
	footer: [
		{
			type: 'button',
			icon: IconHistory,
			label: '创作记录',
			className: 'w-[126px] h-[62px] leading-[62px] text-center bg-white border border-[#333333] rounded-[10px] text-[14px] text-[#333333] box-border flex-shrink-0',
			showAfterImport: false,
			onClick: commonHistory
		},
		{
			type: 'button',
			icon: IconImport,
			label: '导入本地文件',
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-white border border-[#0060FF] rounded-[10px] text-[14px] text-[#0060FF] box-border flex-shrink-0',
			showAfterImport: true,
			onClick: triggerUpload
		},
		{
			type: 'button',
			label: computed(() => convertButtonText.value),
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-[linear-gradient(90deg,#009DFF_0%,#7E4AFF_100%)] rounded-[10px] text-[14px] text-white box-border flex-shrink-0 ml-[auto]',
			showAfterImport: false,
			disabled: computed(() => fileList.value.length === 0),
			onClick: commonTransform
		}
	],
	config: [
		{
			type: 'header',
			label: '转换格式',
			options: [
				{
					type: 'buttons',
					name: 'transformType',
					path: 'transformType',
					label: [TRANSFORM_TYPE.PDF],
				}
			]
		}
	]
})

const PPT2PDFConfig = ref({
	title: 'PPT转PDF',
	subtitle: '轻松将PPT文件转换为不易篡改的PDF',
	officeType: OfficeType.PPT2PDF,
	icon: Ppt2PdfIcon,
	accept: '.ppt,.pptx',
	uploadTitle: '拖拽PPT文件到这里，或者选择导入PPT文件',
	uploadSubtitle: '导入本地PPT文件',
	limit: 1,
	size: 50,
	contentMode: CONTENT_MODE.FILE_LIST,
	footer: [
		{
			type: 'button',
			icon: IconHistory,
			label: '创作记录',
			className: 'w-[126px] h-[62px] leading-[62px] text-center bg-white border border-[#333333] rounded-[10px] text-[14px] text-[#333333] box-border flex-shrink-0',
			showAfterImport: false,
			onClick: commonHistory
		},
		{
			type: 'button',
			icon: IconImport,
			label: '导入本地文件',
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-white border border-[#0060FF] rounded-[10px] text-[14px] text-[#0060FF] box-border flex-shrink-0',
			showAfterImport: true,
			onClick: triggerUpload
		},
		{
			type: 'button',
			label: computed(() => convertButtonText.value),
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-[linear-gradient(90deg,#009DFF_0%,#7E4AFF_100%)] rounded-[10px] text-[14px] text-white box-border flex-shrink-0 ml-[auto]',
			showAfterImport: false,
			disabled: computed(() => !uploaded.value),
			onClick: commonTransform
		}
	],
	config: [
		{
			type: 'header',
			label: '转换格式',
			options: [
				{
					type: 'buttons',
					name: 'transformType',
					path: 'transformType',
					label: [TRANSFORM_TYPE.PDF],
				}
			]
		}
	]
})

const IMG2WORDConfig = ref({
	title: '图片转Word',
	subtitle: '在线提取图片中的文字，还可转换成Word文档',
	officeType: OfficeType.IMG2WORD,
	accept: 'image/*',
	icon: Img2WordIcon,
	uploadTitle: '拖拽文件到这里，或者选择导入文件',
	uploadSubtitle: '导入本地文件',
	limit: 10,
	size: 10,
	contentMode: CONTENT_MODE.FILE_LIST,
	footer: [
		{
			type: 'button',
			icon: IconHistory,
			label: '创作记录',
			className: 'w-[126px] h-[62px] leading-[62px] text-center bg-white border border-[#333333] rounded-[10px] text-[14px] text-[#333333] box-border flex-shrink-0',
			showAfterImport: false,
			onClick: commonHistory
		},
		{
			type: 'button',
			icon: IconImport,
			label: '添加本地文件',
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-white border border-[#0060FF] rounded-[10px] text-[14px] text-[#0060FF] box-border flex-shrink-0',
			showAfterImport: computed(() => fileList.value.length <= 0),
			onClick: triggerUpload
		},
		{
			type: 'button',
			label: computed(() => convertButtonText.value),
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-[linear-gradient(90deg,#009DFF_0%,#7E4AFF_100%)] rounded-[10px] text-[14px] text-white box-border flex-shrink-0 ml-[auto]',
			showAfterImport: false,
			disabled: computed(() => fileList.value.length === 0),
			onClick: commonTransform
		}
	],
	config: [
		{
			type: 'header',
			label: '转换格式',
			options: [
				{
					type: 'buttons',
					name: 'transformType',
					path: 'transformType',
					label: [TRANSFORM_TYPE.WORD],
				}
			]
		},
		{
			type: 'header',
			label: '图片适应',
			options: [
				{
					type: 'buttons',
					path: 'fitOption',
					label: imageFitOptionList,
				}
			]
		}
	]
})

const IMG2EXCELConfig = ref({
	title: '图片转Excel',
	subtitle: '在线提取图片中的文字，还可转换成Word文档',
	officeType: OfficeType.IMG2EXCEL,
	accept: 'image/*',
	icon: Img2ExcelIcon,
	uploadTitle: '拖拽文件到这里，或者选择导入文件',
	uploadSubtitle: '导入本地文件',
	limit: 10,
	size: 10,
	contentMode: CONTENT_MODE.FILE_LIST,
	footer: [
		{
			type: 'button',
			icon: IconHistory,
			label: '创作记录',
			className: 'w-[126px] h-[62px] leading-[62px] text-center bg-white border border-[#333333] rounded-[10px] text-[14px] text-[#333333] box-border flex-shrink-0',
			showAfterImport: false,
			onClick: commonHistory
		},
		{
			type: 'button',
			icon: IconImport,
			label: '导入本地文件',
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-white border border-[#0060FF] rounded-[10px] text-[14px] text-[#0060FF] box-border flex-shrink-0',
			showAfterImport: computed(() => fileList.value.length <= 0),
			onClick: triggerUpload
		},
		{
			type: 'button',
			label: computed(() => convertButtonText.value),
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-[linear-gradient(90deg,#009DFF_0%,#7E4AFF_100%)] rounded-[10px] text-[14px] text-white box-border flex-shrink-0 ml-[auto]',
			showAfterImport: false,
			disabled: computed(() => fileList.value.length === 0),
			onClick: commonTransform
		}
	],
	config: [
		{
			type: 'header',
			label: '转换格式',
			options: [
				{
					type: 'buttons',
					path: 'transformType',
					name: 'transformType',
					label: [TRANSFORM_TYPE.EXCEL],
				}
			]
		},
		{
			type: 'header',
			label: '图片适应',
			options: [
				{
					type: 'buttons',
					path: 'fitOption',
					label: imageFitOptionList,
				}
			]
		}
	]
})

const IMG2PPTConfig = ref({
	title: '图片转PPT',
	subtitle: '在线提取图片中的文字，还可转换成PPT文档',
	officeType: OfficeType.IMG2PPT,
	accept: 'image/*',
	icon: Img2PPTIcon,
	uploadTitle: '拖拽文件到这里，或者选择导入文件',
	uploadSubtitle: '导入本地文件',
	limit: 10,
	size: 10,
	contentMode: CONTENT_MODE.FILE_LIST,
	footer: [
		{
			type: 'button',
			icon: IconHistory,
			label: '创作记录',
			className: 'w-[126px] h-[62px] leading-[62px] text-center bg-white border border-[#333333] rounded-[10px] text-[14px] text-[#333333] box-border flex-shrink-0',
			showAfterImport: false,
			onClick: commonHistory
		},
		{
			type: 'button',
			icon: IconImport,
			label: '导入本地文件',
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-white border border-[#0060FF] rounded-[10px] text-[14px] text-[#0060FF] box-border flex-shrink-0',
			showAfterImport: computed(() => fileList.value.length <= 0),
			onClick: triggerUpload
		},
		{
			type: 'button',
			label: computed(() => convertButtonText.value),
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-[linear-gradient(90deg,#009DFF_0%,#7E4AFF_100%)] rounded-[10px] text-[14px] text-white box-border flex-shrink-0 ml-[auto]',
			showAfterImport: false,
			disabled: computed(() => fileList.value.length === 0),
			onClick: commonTransform
		}
	],
	config: [
		{
			type: 'header',
			label: '转换格式',
			options: [
				{
					type: 'buttons',
					path: 'transformType',
					name: 'transformType',
					label: [TRANSFORM_TYPE.PPT],
				}
			]
		},
		{
			type: 'header',
			label: '图片适应',
			options: [
				{
					type: 'buttons',
					path: 'fitOption',
					label: imageFitOptionList,
				}
			]
		}
	]
})

const IMG2PDFConfig = ref({
	title: '图片转PDF',
	subtitle: '在线提取图片中的文字，还可转换成PDF文档',
	officeType: OfficeType.IMG2PDF,
	accept: 'image/*',
	icon: Img2PDFIcon,
	uploadTitle: '拖拽文件到这里，或者选择导入文件',
	uploadSubtitle: '导入本地文件',
	limit: 10,
	size: 10,
	contentMode: CONTENT_MODE.FILE_LIST,
	footer: [
		{
			type: 'button',
			icon: IconHistory,
			label: '创作记录',
			className: 'w-[126px] h-[62px] leading-[62px] text-center bg-white border border-[#333333] rounded-[10px] text-[14px] text-[#333333] box-border flex-shrink-0',
			showAfterImport: false,
			onClick: commonHistory
		},
		{
			type: 'button',
			icon: IconImport,
			label: '导入本地文件',
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-white border border-[#0060FF] rounded-[10px] text-[14px] text-[#0060FF] box-border flex-shrink-0',
			showAfterImport: computed(() => fileList.value.length <= 0),
			onClick: triggerUpload
		},
		{
			type: 'button',
			label: computed(() => convertButtonText.value),
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-[linear-gradient(90deg,#009DFF_0%,#7E4AFF_100%)] rounded-[10px] text-[14px] text-white box-border flex-shrink-0 ml-[auto]',
			showAfterImport: false,
			disabled: computed(() => fileList.value.length === 0),
			onClick: commonTransform
		}
	],
	config: [
		{
			type: 'header',
			label: '转换格式',
			options: [
				{
					type: 'buttons',
					path: 'transformType',
					name: 'transformType',
					label: [TRANSFORM_TYPE.PDF],
				}
			]
		},
		{
			type: 'header',
			label: '图片适应',
			options: [
				{
					type: 'buttons',
					path: 'fitOption',
					label: imageFitOptionList,
				}
			]
		}
	]
})

const IMGCOMPRESSConfig = ref({
	title: '图片压缩',
	subtitle: '智能压缩图片大小',
	accept: '.jpg,.jpeg,.png,.gif',
	officeType: OfficeType.IMGCOMPRESS,
	icon: ImgCompressIcon,
	uploadTitle: '拖拽文件到这里，或者选择导入文件',
	uploadSubtitle: '导入本地文件',
	limit: 1,
	size: 10,
	contentMode: CONTENT_MODE.FILE_LIST,
	footer: [
		{
			type: 'button',
			icon: IconHistory,
			label: '创作记录',
			className: 'w-[126px] h-[62px] leading-[62px] text-center bg-white border border-[#333333] rounded-[10px] text-[14px] text-[#333333] box-border flex-shrink-0',
			showAfterImport: false,
			onClick: commonHistory
		},
		{
			type: 'button',
			icon: IconImport,
			label: '导入本地文件',
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-white border border-[#0060FF] rounded-[10px] text-[14px] text-[#0060FF] box-border flex-shrink-0',
			showAfterImport: true,
			onClick: triggerUpload
		},
		{
			type: 'button',
			label: computed(() => convertButtonText.value),
			className: 'w-[230px] h-[62px] leading-[62px] text-center bg-[linear-gradient(90deg,#009DFF_0%,#7E4AFF_100%)] rounded-[10px] text-[14px] text-white box-border flex-shrink-0 ml-[auto]',
			showAfterImport: false,
			disabled: computed(() => fileList.value.length === 0),
			onClick: commonTransform
		}
	],
	config: [
		{
			type: 'header',
			label: '高级设置',
			options: [
				{
					type: 'text',
					label: '一键压缩至',
					show: true
				},
				{
					type: 'inputGroup',
					items: [
						{
							type: 'inputNumber',
							path: 'size',
							placeholder: '请输入压缩大小',
							defaultValue: null,
							pageMin: 1,
							pageMax: 1000,
							showButton: false,
							suffix: 'KB',
							width: '100%'
						},
					]
				},
				{
					type: 'text',
					label: '压缩等级',
					show: true
				},
				{
					type: 'slide',
					path: 'quality',
					step: 1,
					marks: {
						1: '1%',
						25: '',
						50: '50%',
						75: '',
						100: '100%'
					},
					start: 1,
					defaultValue: 60,
					end: 100
				}
			]
		}
	]
})
const configMap = {
	[OfficeType.PDF2WORD]: PDF2WORDConfig.value,
	[OfficeType.PDF2EXCEL]: PDF2EXCELConfig.value,
	[OfficeType.PDF2PPT]: PDF2PPTConfig.value,
	[OfficeType.PDFMERGE]: PDFMERGEConfig.value,
	[OfficeType.PDFSPLIT]: PDFSPLITConfig.value,
	// office系列
	[OfficeType.WORD2PDF]: WORD2PDFConfig.value,
	[OfficeType.EXCEL2PDF]: EXCEL2PDFConfig.value,
	[OfficeType.PPT2PDF]: PPT2PDFConfig.value,
	// 图片系列
	[OfficeType.IMG2EXCEL]: IMG2EXCELConfig.value,
	[OfficeType.IMG2PPT]: IMG2PPTConfig.value,
	[OfficeType.IMG2WORD]: IMG2WORDConfig.value,
	[OfficeType.IMG2PDF]: IMG2PDFConfig.value,
	[OfficeType.IMGCOMPRESS]: IMGCOMPRESSConfig.value,
	// 密码找回
	[OfficeType.FINDPASSWORD]: FINDPASSWORDConfig.value,
	// 文档翻译
	[OfficeType.DOC2TRANSLATE]: TRANSLATEConfig.value
}

const groupPages = computed(() => {
	const groups: CheckedPage[][] = [];
	if (splitType.value === 2) {
		// Create groups where each group contains first and last page of each pageNumber interval
		const pageSize = pageNumber?.value!;
		for (let i = 0; i < pages?.value?.length; i += pageSize) {
			// Get first and last page of the current interval
			const firstPage = pages[i];
			const lastPage = pages[Math.min(i + pageSize - 1, pages?.value?.length - 1)];
			// Only add group if both pages exist and are different
			if (firstPage && lastPage && firstPage !== lastPage) {
				groups.push([firstPage, lastPage]);
			}
		}
	}
	return groups;
});


const handleUpdateChecked = (page: CheckedPage) => {
	pages.value = pages.value.map(item => {
		if (item.mediaId === page.mediaId) {
			return {
				...item,
				checked: !item.checked
			}
		}
		return item;
	})
}
const handleUpdateForm = (f: any) => {
	const { splitType: newSplitType, startPage: sp, endPage: ep, pageNumber: pn, quality, size } = f;
	if (newSplitType?.length) {
		form.value.splitType = splitType.value = Number(newSplitType[0]);
		if (Number(newSplitType[0]) === 1) {
			form.value.startPage = startPage.value = sp || 1;
			form.value.endPage = endPage.value = ep || pages.value?.length || 1;
		}
	}
	if (splitType.value === 1) {
		form.value.startPage = startPage.value = sp || startPage.value;
		form.value.endPage = endPage.value = ep || endPage.value;
	} else if (splitType.value === 2) {
		form.value.pageNumber = pageNumber.value = pn || pageNumber.value;
	}
	if (quality) {
		form.value.quality = quality;
	}
	if (size) {
		form.value.size = size;
	}
};

const currentConfig = computed(() => {
	return configMap[route.name as OfficeType];
})

const { run: runCheckBindPhone } = useRequest(checkBindPhone, {
	manual: true,
	onSuccess(data: { bindedPhone: boolean }) {
		if (!data?.bindedPhone) {
			showCrackPhoneModal.value = true;
		} else {
			commonHistory();
		}
	}
})

const { run: runGetPassword, loading: getPasswordLoading } = useRequest(getPassword, {
	manual: true,
	onSuccess(data: { password: string }) {
		fileCracked.value = true;
		copyPassword(data.password, message);
		message.success('复制成功');
	}
})

const { run: runGetPasswordTask, cancel: cancelGetPasswordTask } = useRequest(getPasswordTask, {
	manual: true,
	onSuccess(data: {
		taskId?: number;
		complete?: boolean;
		progress?: number;
		cracked?: number;
		pay?: boolean;
	}, params) {
		if (data.complete) {
			taskId.value = data.taskId || null;
			if (data.cracked === 0) {
				// 直接弹多会员破解
				crackStatus.value = 'fail';
				showCrackPhoneModal.value = true;
			} else {
				if (data?.pay) {
					runGetPassword({
						taskId: params?.[0]?.taskId
					})
				} else {
					// 单会员单次破解
					crackStatus.value = 'success';
					showCrackModal.value = true;
				}
			}
			crackLoading.value = false;
			cancelGetPasswordTask();
		}
	},
	onError(error, params) {
		if (error.errcode === '50010') {
			const taskId = params?.[0]?.taskId
			window.$aiwork.openRecharge({
				type: 'office',
				categoryId: 6,
				memberType: 'DOC_DECODE_MEMBER_2',
				taskId: taskId ?? undefined,
			}).then(() => {
				// showCrackPhoneModal.value = true;
				runCheckBindPhone();
			})
		}
	},
})

const { run: runAITools, refresh } = useAITools({
	type: currentConfig.value.type,
	onSuccess(data) {
		if (currentConfig.value.officeType === OfficeType.FINDPASSWORD) {
			// crackStatus.value = 'pending';
			// crackLoading.value = true;
			taskId.value = data.taskId;
			runGetPasswordTask({
				taskId: data.taskId,
				withProgressing: false
			})
		}
		else if (currentConfig.value.officeType === OfficeType.DOC2TRANSLATE) {
			router.push({
				name: 'OfficeHistory'
			})
		} else {
			// 跳转到创作记录页面
			message.success('请求成功');
			router.push({
				name: 'OfficeHistory'
			})
		}
	},
	onError(error, params) {
		if (error.errcode === '50001') {
			if (currentConfig.value.officeType === OfficeType.DOC2TRANSLATE) {
				window.$aiwork.openRecharge({
					type: 'translate',
					categoryId: 6,
					memberType: 'DOC_TRANS_MEMBER',
					taskId: params?.[0]?.taskId
				}).then(() => {
					message.success('支付成功');
					refresh()
				})
			} else {
				window.$aiwork.openRecharge({
					type: 'office',
					categoryId: 6,
					memberType: 'DOC_MEMBER',
				}).then(() => {
					refresh()
				})
			}
		}
	}
})

const { run: runFetchViewPdf, loading: fetchViewPdfLoading } = useRequest<{
	pages: Page[]
}, [params: { inputUrl: string; inputName: string }]>(fetchViewPdf, {
	manual: true,
	onSuccess(data) {
		pages.value = data?.pages?.map(page => ({
			...page,
			checked: page.checked || true
		}));
		pageMax.value = pages.value.length;
	}
})

const { run, loading: uploadFileLoading, data: uploadFileData } = useUploadFile(currentConfig.value.limit, {
	onSuccess: async (data) => {
		// Move checkPointValue declaration above this block
		await checkPointValue(data);
		// PDF分割的情况下才需要请求runFetchViewPdf获取预览页面
		if (currentConfig.value.contentMode === CONTENT_MODE.FILE_LIST_SPLIT) {
			const file = data?.[0];
			runFetchViewPdf({
				inputUrl: file.url,
				inputName: file.name,
			});
		}
	},
	onError(e) {
		console.log('update error', e);
	}
})

const handlePhoneSubmit = () => {
	// // 弹支付弹窗
	// window.$aiwork.openRecharge({
	// 	type: 'findpasswordOnce',
	// 	categoryId: 6,
	// 	memberType: 'DOC_DECODE_MEMBER_2',
	// 	taskId: taskId.value ?? undefined,
	// 	// 可能还要传taskId什么的 方便后端判断密码强度
	// }).then(() => {
	// 	// 支付完成后, 通过taskId获取密码 然后直接复制到剪切板
	// 	commonHistory();
	// })
	commonHistory();
}
const handleClickCopy = () => {
	if (fileCracked.value) {
		runGetPassword({
			taskId: taskId.value!
		})
		return;
	}
	window.$aiwork.openRecharge({
		type: 'findpasswordOnce',
		categoryId: 6,
		memberType: 'DOC_DECODE_MEMBER',
		taskId: taskId.value ?? undefined,
		// 可能还要传taskId什么的 方便后端判断密码强度
	}).then(() => {
		// 支付完成后, 通过taskId获取密码 然后直接复制到剪切板
		message.success('支付成功');
		runGetPassword({
			taskId: taskId.value!
		})
	})
}

function commonReplace() {
	uploadFileData.value = [];
}
function commonHistory() {
	router.push({
		name: 'OfficeHistory',
	})
}
function crackFile() {
	const { url: inputUrl, name: inputName } = uploadFileData?.value?.[0]
	runAITools({
		type: OfficeType.FINDPASSWORD,
		inputUrl,
		inputName
	})
}

function commonTransform() {
	const { name: inputName, url: inputUrl } = uploadFileData?.value?.[0]

	if (currentConfig.value.officeType === OfficeType.PDF2WORD) {
		runAITools({
			type: OfficeType.PDF2WORD,
			inputName,
			inputUrl,
			outputFormat: "docx",
		})
	} else if (currentConfig.value.officeType === OfficeType.PDF2EXCEL) {
		runAITools({
			type: OfficeType.PDF2EXCEL,
			inputName,
			inputUrl,
		})
	} else if (currentConfig.value.officeType === OfficeType.PDF2PPT) {
		runAITools({
			type: OfficeType.PDF2PPT,
			inputName,
			inputUrl,
			outputFormat: "pptx",
		})
	} else if (currentConfig.value.officeType === OfficeType.PDFMERGE) {
		runAITools({
			type: OfficeType.PDFMERGE,
			inputArray: uploadFileData?.value?.map(item => ({
				inputUrl: item.url,
				inputName: item.name,
				inputFormat: "pdf",
			}))
		})
	} else if (currentConfig.value.officeType === OfficeType.PDFSPLIT) {
		if (splitType.value === SplitType.ALL) {
			runAITools({
				inputUrl,
				inputName,
				type: OfficeType.PDFSPLIT,
				splitType: SplitType.ALL,
				pageNumbers: (() => {
					const result: string[] = [];
					pages?.value?.forEach((item, index) => {
						if (item.checked) {
							result.push(index + 1);
						}
					});
					return result;
				})().join(",")
			})
		} else if (splitType.value === SplitType.INTERVAL) {
			runAITools({
				inputUrl,
				inputName,
				type: OfficeType.PDFSPLIT,
				splitType: SplitType.INTERVAL,
				pageNumber: pageNumber?.value
			})
		} else {

			runAITools({
				inputUrl,
				inputName,
				type: OfficeType.PDFSPLIT,
				splitType: SplitType.RANGE,
				startPage: startPage?.value,
				endPage: endPage?.value,
			})
		}
	} else if (currentConfig.value.officeType === OfficeType.WORD2PDF) {
		runAITools({
			type: OfficeType.WORD2PDF,
			inputName,
			inputUrl,
		})
	} else if (currentConfig.value.officeType === OfficeType.EXCEL2PDF) {
		runAITools({
			type: OfficeType.EXCEL2PDF,
			inputName,
			inputUrl,
		})
	} else if (currentConfig.value.officeType === OfficeType.PPT2PDF) {
		runAITools({
			type: OfficeType.PPT2PDF,
			inputName,
			inputUrl,
		})
	} else if (currentConfig.value.officeType === OfficeType.DOC2TRANSLATE) {
		const { transType, transOutType, transLayout, targetLang } = form.value

		runAITools({
			type: OfficeType.DOC2TRANSLATE,
			fileName: inputName,
			fileUrl: inputUrl,
			transType, transOutType, transLayout, targetLang
		})
	} else if (currentConfig.value.officeType === OfficeType.IMG2WORD) {
		const inputArray = uploadFileData?.value?.map(item => ({
			inputUrl: item.url,
			inputName: item.name,
		}))
		runAITools({
			type: OfficeType.IMG2WORD,
			inputArray,
			outputFormat: 'docx'
		})
	} else if (currentConfig.value.officeType === OfficeType.IMG2EXCEL) {
		const inputArray = uploadFileData?.value?.map(item => ({
			inputUrl: item.url,
			inputName: item.name,
		}))
		runAITools({
			type: OfficeType.IMG2EXCEL,
			inputArray,
			outputFormat: 'excel'
		})
	} else if (currentConfig.value.officeType === OfficeType.IMG2PPT) {
		const inputArray = uploadFileData?.value?.map(item => ({
			inputUrl: item.url,
			inputName: item.name,
		}))
		runAITools({
			type: OfficeType.IMG2PPT,
			inputArray,
			outputFormat: 'pptx'
		})
	} else if (currentConfig.value.officeType === OfficeType.IMG2PDF) {
		const inputArray = uploadFileData?.value?.map(item => ({
			inputUrl: item.url,
			inputName: item.name,
		}))
		runAITools({
			type: OfficeType.IMG2PDF,
			inputArray,
			// fitOption: form.value.fitOption || ImageFitOption.FILL_PAGE,
			// colorType: form.value.colorType || 'color',
			// autoRotate: form.value.autoRotate !== undefined ? form.value.autoRotate : true
		})
	} else if (currentConfig.value.officeType === OfficeType.IMGCOMPRESS) {
		const inputArray = uploadFileData?.value?.map(item => ({
			inputUrl: item.url,
			inputName: item.name,
		}))
		runAITools({
			type: OfficeType.IMGCOMPRESS,
			inputArray,
			quality: form?.value?.quality / 100,
			size: form?.value?.size
		})
	}
}

const handleFileList = (files: UploadFileInfo[]) => {
	if (!files.length) {
		uploadFileData.value = []
		pointValue.value = 0
		return
	}
	// uploadFileData.value = files;
	// fileList.value = files;
	// 需要立即上传
	run(files)

	// 根据文件类型调用对应积分接口
	// checkPointValue(files)
}
// Move this declaration above its usage
const checkPointValue = async (files: UploadFileInfo[]) => {
  try {
    checkPointLoading.value = true;
    const file = files[0];
    if (!file?.url) {
      checkPointLoading.value = false;
      return;
    }

		const officeType = currentConfig.value.officeType
		// 排除找回密码功能，不查询积分
		if ([OfficeType.FINDPASSWORD].includes(officeType)) {
			pointValue.value = 0
			return
		}

		// PDF相关功能
		if ([OfficeType.PDF2WORD, OfficeType.PDF2EXCEL, OfficeType.PDF2PPT, OfficeType.PDFSPLIT, OfficeType.PDFMERGE].includes(officeType)) {
			let convertType = ''
			switch (officeType) {
				case OfficeType.PDF2WORD:
					convertType = 'PDF_WORD'
					break
				case OfficeType.PDF2EXCEL:
					convertType = 'PDF_EXCEL'
					break
				case OfficeType.PDF2PPT:
					convertType = 'PDF_PPT'
					break
				case OfficeType.PDFSPLIT:
					convertType = 'SPLIT_PDF'
					break
				case OfficeType.PDFMERGE:
					convertType = 'MERGE_PDF'
					break
			}
			if (convertType) {
				const result = await getPdfUsePoint({ inputUrl: file.url, convertType })
				pointValue.value = result.usePoint || 0
			}
		}
		// Office相关功能
		else if ([OfficeType.WORD2PDF, OfficeType.EXCEL2PDF, OfficeType.PPT2PDF].includes(officeType)) {
			let convertType = ''
			switch (officeType) {
				case OfficeType.WORD2PDF:
					convertType = 'WORD_PDF'
					break
				case OfficeType.EXCEL2PDF:
					convertType = 'EXCEL_PDF'
					break
				case OfficeType.PPT2PDF:
					convertType = 'PPT_PDF'
					break
			}
			if (convertType) {
				const result = await getOfficeUsePoint({ inputUrl: file.url, convertType })
				pointValue.value = result.usePoint || 0
			}
		}
		// 图片相关功能
		else if ([OfficeType.IMG2WORD, OfficeType.IMG2EXCEL, OfficeType.IMG2PPT, OfficeType.IMG2PDF, OfficeType.IMGCOMPRESS].includes(officeType)) {
			let convertType = ''
			switch (officeType) {
				case OfficeType.IMG2WORD:
					convertType = 'IMG_WORD'
					break
				case OfficeType.IMG2EXCEL:
					convertType = 'IMG_EXCEL'
					break
				case OfficeType.IMG2PPT:
					convertType = 'IMG_PPT'
					break
				case OfficeType.IMG2PDF:
					convertType = 'IMG_PDF'
					break
				case OfficeType.IMGCOMPRESS:
					convertType = 'IMG_COMPRESS'
					break
			}
			if (convertType) {
				const inputArray = files.filter(f => f.url).map(f => ({ inputUrl: f.url!, inputName: f.name }))
				if (inputArray.length > 0) {
					const result = await getImageUsePoint({ inputArray, convertType })
					pointValue.value = result.usePoint || 0
				}
			}
		}
		else if ([OfficeType.DOC2TRANSLATE].includes(officeType)) {
			const result = await getDocTranslateUsePoint({ fileUrl: file.url })
			pointValue.value = result.charCount || 0
		}
	} catch (error) {
	  console.error('获取积分信息失败:', error)
	  pointValue.value = 0
	} finally {
	  checkPointLoading.value = false
	}
}
const handleFileDelete = (file: UploadFileInfo) => {
	uploadFileData.value = uploadFileData.value.filter(item => item.name !== file.name)
	// fileList.value = fileList.value.filter(item => item.name !== file.name)
}

const fileInputRef = ref(null)

const handleFileInputChange = (e: any) => {
	const file = e.target.files[0];
	if (!file) return;

	// 创建符合 NUpload 规范的文件对象
	const uploadFile: UploadFileInfo = {
		id: Date.now().toString(),
		name: file.name,
		status: 'finished',
		percentage: 100,
		file: file,
		url: URL.createObjectURL(file),
		type: file.type
	};

	run([uploadFile]);

	// 重置文件输入框，以便再次选择同一文件时也能触发 change 事件
	e.target.value = '';
}

onMounted(() => {
	// 从localStorage中获取临时文件
	const tempFile = ls.get('office_temp_file');
	if (tempFile) {
		// 判断当前功能是否为 PDF 分割
		const isPdfSplit = currentConfig.value.officeType === OfficeType.PDFSPLIT;
		if (isPdfSplit) {
			uploadFileData.value = tempFile
			runFetchViewPdf({
				inputUrl: tempFile?.[0]?.url,
				inputName: tempFile?.[0]?.name
			})
		} else {
			uploadFileData.value = tempFile
		}
		// 清除临时文件
		ls.remove('office_temp_file');
	}
});

onBeforeUnmount(() => {
	// 清空localStorage中的临时文件
	ls.remove('office_temp_file');
});
</script>
<style lang="less" scoped>
:deep(.n-spin-container) {
	// width: 100% !important;
	height: 100% !important;
}

:deep(.n-spin-content) {
	width: 100% !important;
	height: 100% !important;
}

.office-container {
	width: 100%;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: center;
	column-gap: 12px;
	background: linear-gradient(to bottom, #f5f7ff, rgba(236, 241, 255, 0.8858), #e5f1ff);
	margin: 0 auto;
	padding: 50px 0;
	// background: url("@/assets/images/paper-bg.png");
	background-size: cover;
	background-position: center;
	background-repeat: repeat;
}

.office-col-container {
	width: 100%;
	display: flex;
	background: linear-gradient(to bottom, #f5f7ff, rgba(236, 241, 255, 0.8858), #e5f1ff);
	flex-direction: column;
	row-gap: 14px;
	align-items: center;
	justify-content: center;
	padding: 50px 0;
}

.dotted-border {
	border-radius: 10px;
	border: 2px dashed transparent;
	background: linear-gradient(white, white) padding-box, repeating-linear-gradient(-45deg, #DCE9FF 0, #DCE9FF .3em, white 0, white .8em);
}

.dotted-border-left {
	border-left: 2px dashed transparent;
	background: linear-gradient(white, white) padding-box, repeating-linear-gradient(-45deg, #DCE9FF 0, #DCE9FF .3em, white 0, white .8em);
}
</style>
