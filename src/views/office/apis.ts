import { post } from "@/utils/request";
import { InputArray, InputFileInfo, SplitType } from "./types";

export function fetchViewPdf<T, R>(params: R) {
	return post<T>({
		url: "/api3/adtool/pdf/general/viewPdf",
		data: params,
	});
}

export function uploadOfficeFile<T>(params: {
	file: File;
	fileName: string;
	fileType: number;
	onUploadProgress: any;
}) {
	const { onUploadProgress, ...rest } = params || {};
	return post<T>({
		url: "/api3/adtool/file/upload",
		headers: { "Content-Type": "multipart/form-data" },
		data: rest,
		onUploadProgress,
	});
}
// 查询任务列表
export function fetchOfficeFile<T>(params: any) {
	return post<T>({
		url: "/api3/adtool/file/task/queryFileTaskList",
		data: params,
	});
}

// 批量删除任务
export function batchDeleteFileTask<T>(params: {
	taskIdList: string[]; // 必须，任务ID列表
}) {
	return post<T>({
		url: "/api3/adtool/file/task/batchDeleteFileTask",
		data: params,
	});
}

/********** PDF转换 ********** */

// PDF转换CSV
export function pdfToCsv<T>(params: {
	inputUrl: string; // 必须，文件URL
	inputName: string; // 必须，文件名称
}) {
	return post<T>({
		url: "/api3/adtool/pdf/convert/pdf-csv",
		data: params,
	});
}

// PDF转换图片
export function pdfToImage<T>(params: {
	inputUrl: string;
	inputName: string;
	// 图片格式(png、jpg、jpeg)
	imageFormat?: "png" | "jpg" | "jpeg";
}) {
	return post<T>({
		url: "/api3/adtool/pdf/convert/pdf-img",
		data: params,
	});
}

// PDF拆分
export function splitPdf<T>(params: {
	inputUrl: string; // 必须，文件URL
	inputName: string; // 必须，文件名称
	splitType: SplitType; // 必须，拆分方式（1按页码范围拆分，2按间距拆分，3一键拆分所有页面）
	startPage?: number; // 非必须，起始页（按范围拆分必传）
	endPage?: number; // 非必须，结束页（按范围拆分必传）
	pageNumber?: number; // 非必须，页码间距（按间距拆分必传）
}) {
	return post<T>({
		url: "/api3/adtool/pdf/general/split-pdf",
		data: params,
	});
}

// PDF合并
export function mergePdf<T>(params: { inputArray: InputArray }) {
	return post<T>({
		url: "/api3/adtool/pdf/general/merge-pdf",
		data: params,
	});
}

// PDF转换PPT
export function pdfToPpt<T>(params: {
	inputUrl: string; // 必须，文件URL
	inputName: string; // 必须，文件名称
	outputFormat: "ppt" | "pptx"; // 必须，输出格式（ppt、pptx）
}) {
	return post<T>({
		url: "/api3/adtool/pdf/convert/pdf-ppt",
		data: params,
	});
}

// PDF转换Word
export function pdfToWord<T>(params: {
	inputUrl: string; // 必须，文件URL
	inputName: string; // 必须，文件名称
	outputFormat: "doc" | "docx"; // 必须，输出格式（doc、docx）
}) {
	return post<T>({
		url: "/api3/adtool/pdf/convert/pdf-word",
		data: params,
	});
}

/********** 图片转换 ********** */

// 图片转Word
export function img2Word<T>(params: { inputArray: InputFileInfo[] }) {
	return post<T>({
		url: "/api3/adtool/img/convert/img-word",
		data: params,
	});
}

// 图片转PDF
export function imageToPdf<T>(params: { inputArray: InputFileInfo[] }) {
	return post<T>({
		url: "/api3/adtool/img/convert/img-pdf",
		data: params,
	});
}

// 图片压缩
export function imgCompress<T>(params: {
	inputArray: InputFileInfo[];
	size: number;
	quality: number;
}) {
	return post<T>({
		url: "/api3/adtool/img/convert/compress-img",
		data: params,
	});
}



/********** office转换 */

// Word转PDF
export function wordToPdf<T>(params: {
	inputUrl: string; // 必须，文件URL
	inputName: string; // 必须，文件名称
}) {
	return post<T>({
		url: "/api3/adtool/office/convert/word-pdf",
		data: params,
	});
}

// Excel转PDF
export function excelToPdf<T>(params: {
	inputUrl: string; // 必须，文件URL
	inputName: string; // 必须，文件名称
}) {
	return post<T>({
		url: "/api3/adtool/office/convert/excel-pdf",
		data: params,
	});
}

// PPT转PDF
export function pptToPdf<T>(params: {
	inputUrl: string; // 必须，文件URL
	inputName: string; // 必须，文件名称
}) {
	return post<T>({
		url: "/api3/adtool/office/convert/ppt-pdf",
		data: params,
	});
}

// 文档翻译
export function doc2Translate<T>(params: {
	fileUrl: string; // 必须，文件URL
	fileName: string; // 必须，文件名称
	sourceLang?: string; // 源语言
	targetLang?: string; // 目标语言
	transType?: string; // 翻译类型
	transOutType?: string; // 翻译输出类型
	transLayout?: string; // 翻译布局
}) {
	return post<T>(
		{
			url: "/api3/adtool/ai/aiDocTranslate",
			data: params,
		},
		{
			payParams: {
				type: "doc2translate",
			},
		}
	);
}

/********** 密码查找 ********** */
// 密码查找
export function searchPassword<T>(params: {
	inputUrl: string; // 必须，文件URL
	inputName: string; // 必须，文件名称
}) {
	return post<T>({
		url: "/api3/adtool/pwd/submitTask",
		data: params,
	});
}

// 获取密码查找任务结果
export function getPasswordTask<T>(params: {
	taskId: number; // 必须，任务ID
	withProgressing?: boolean; // 非必须，是否包含进度信息
}) {
	return post<T>({
		url: "/api3/adtool/pwd/getTask",
		data: params,
	});
}

// 获取密码
export function getPassword<T>(params: {
	taskId: number; // 必须，任务ID
}) {
	return post<T>({
		url: "/api3/adtool/pwd/getPwd",
		data: params,
	});
}

// 发送短信验证码
export function sendSmsCode<T>(params: {
	taskId: number;
	phone: string; // 必须，手机号码
}) {
	return post<T>({
		url: "/api3/adtool/pwd/sendSmsCode",
		data: params,
	});
}


// 验证短信验证码
export function verifySmsCode<T>(params: {
	phone: string; // 必须，手机号码
	verifyCode: string; // 必须，短信验证码
	taskId: number
}) {
	return post<T>({
		url: "/api3/adtool/pwd/verifySmsCode",
		data: params,
	});
}
export function checkBindPhone<T>() {
	return post<T>({
		url: "/api3/adtool/pwd/checkBindPhone",
	});
}

// 获取PDF使用积分信息
export function getPdfUsePoint<T>(params: {
	inputUrl?: string; // 非必须，文件URL(类型为PDF_WORD、PDF_PPT、PDF_EXCEL、PDF_IMG时必传)
	convertType: string; // 必须，转换类型(PDF_WORD、PDF_PPT、PDF_EXCEL、PDF_IMG、MERGE_PDF、SPLIT_PDF、SPLIT_PDF_BY_COUNT)
}): Promise<{
	pageCount?: number; // 非必须，页数
	usePoint?: number; // 非必须，使用积分
}> {
	return post<{
		pageCount?: number;
		usePoint?: number;
	}>({
		url: "/api3/adtool/pdf/general/getPdfUsePoint",
		data: params,
	});
}
// 获取office使用积分信息（
export function getOfficeUsePoint<T>(params: {
	inputUrl?: string; // 非必须，文件URL
	convertType: string; // 必须，转换类型(WORD_PDF、PPT_PDF、EXCEL_PDF)
}): Promise<{
	pageCount?: number; // 非必须，页数
	usePoint?: number; // 非必须，使用积分
}> {
	return post<{
		pageCount?: number;
		usePoint?: number;
	}>({
		url: "/api3/adtool/office/general/getOfficeUsePoint",
		data: params,
	});
}
// 获取图片使用积分信息
export function getImageUsePoint<T>(params: {
	inputArray: InputFileInfo[]; // 必须，输入文件列表
	convertType: string; // 必须，转换类型
}): Promise<{
	pageCount?: number; // 非必须，页数
	usePoint?: number; // 非必须，使用积分
}> {
	return post<{
		pageCount?: number;
		usePoint?: number;
	}>({
		url: "/api3/adtool/img/general/getImageUsePoint",
		data: params,
	});
}

// 获取文档翻译使用积分信息
export function getDocTranslateUsePoint<T>(params: {
	fileUrl: string; // 必须，文件URL
}): Promise<{
	charCount?: number; // 非必须，字符数
	usePoint?: number; // 非必须，使用积分
}> {
	return post<{
		charCount?: number;
		usePoint?: number;
	}>({
		url: "/api3/adtool/ai/getDocTranslateUsePoint",
		data: params,
	});
}


// 图片转Excel
export function imgToExcel<T>(params: {
	inputArray: InputFileInfo[]; // 必须，输入文件列表
	fitOption?: "fillPage" | "fitDocumentToImage" | "maintainAspectRatio"; // 非必须，图片适应选项
	autoRotate?: boolean; // 非必须，是否自动旋转图片
}) {
	return post<T>({
		url: "/api3/adtool/img/convert/img-excel",
		data: params,
	});
}

// 图片转PPT
export function imgToPpt<T>(params: {
	inputArray: InputFileInfo[]; // 必须，输入文件列表
	fitOption?: "fillPage" | "fitDocumentToImage" | "maintainAspectRatio"; // 非必须，图片适应选项
	autoRotate?: boolean; // 非必须，是否自动旋转图片
}) {
	return post<T>({
		url: "/api3/adtool/img/convert/img-ppt",
		data: params,
	});
}

// PDF转换Excel
export function pdfToExcel<T>(params: {
	inputUrl: string; // 必须，文件URL
	inputName: string; // 必须，文件名称
}): Promise<{
	taskId?: number; // 非必须，任务ID
	thirdTaskId?: string; // 非必须，第三方任务ID
}> {
	return post<{
		taskId?: number;
		thirdTaskId?: string;
	}>({
		url: "/api3/adtool/pdf/convert/pdf-excel",
		data: params,
	});
}
