<script setup lang="ts">
import { ref } from 'vue';
import { NDivider, NSpin, useMessage } from 'naive-ui';
import { useRequest } from 'vue-hooks-plus';
import { fetchPersonalDetail2 } from './api';
import { router } from '@/router';

// 导入所有组件
import UserInfoCard from './components/UserInfoCard.vue';
import BaseInfoBinding from './components/BaseInfoBinding.vue';
import MembershipTabs from './components/MembershipTabs.vue';
import MembershipCard from './components/MembershipCard.vue';
import UsageDetail from './components/UsageDetail.vue';
import BindAccount from '@/views/components/BindAccount.vue';
import BindWechat from '@/views/components/BindWechat.vue';

const message = useMessage();

// 使用原始的数据获取方式
const { data: memberData, loading } = useRequest(fetchPersonalDetail2);

// 本地状态
const activeMode = ref<'member' | 'freeMember' | 'paper'>('freeMember');
const bindPhone = ref(false);
const bindWechat = ref(false);

// 事件处理
const handleUpgrade = () => {
  message.info('即将跳转会员开通页面');
};

const handleBindPhone = () => {
  bindPhone.value = true;
};

const handleBindWechat = () => {
  bindWechat.value = true;
};

const handleBindSuccess = () => {
  bindPhone.value = false;
  bindWechat.value = false;
  window.location.reload();
};

const handleOldProfile = () => {
  router.push({
    name: 'Profile',
  });
};
</script>

<template>
  <main class="w-full min-h-screen bg-[#F6F8F9] dark:bg-[#1A1A1A] p-4 md:p-6">
    <n-spin :show="loading" description="加载中...">
      <div class="max-w-[1200px] mx-auto">
        <div @click="handleOldProfile" class="mb-[26px]">
          <a class="text-[#0E69FF]">切换至旧版个人中心</a>
          >
        </div>

        <!-- 用户信息卡片 -->
        <UserInfoCard :memberData="memberData" @upgrade="handleUpgrade" />

        <!-- 基础信息区域 -->
        <section class="bg-white dark:bg-[#242424] rounded-xl shadow-sm p-6 mb-6">
          <BaseInfoBinding
            @bind-phone="handleBindPhone"
            @bind-wechat="handleBindWechat"
            @bind-success="handleBindSuccess"
          />

          <NDivider class="!my-[24px]" />

          <MembershipTabs v-model:activeMode="activeMode" :memberData="memberData" />
          <MembershipCard :activeMode="activeMode" :memberData="memberData" @upgrade="handleUpgrade" />
        </section>

        <!-- 权益使用明细 -->
        <UsageDetail />
      </div>
    </n-spin>

    <!-- 弹窗 -->
    <BindAccount v-model:visible="bindPhone" @bind-success="handleBindSuccess" />
    <BindWechat v-model:visible="bindWechat" @bind-success="handleBindSuccess" />
  </main>
</template>

<style scoped>
/* 样式移到各个子组件中 */
</style>
