<template>
  <div class="vip-levels">
    <div class="vip-levels__header">
      <div class="vip-levels__logo">
        <div class="logo-group">
          <!-- Logo品牌球 -->
          <NImage width="170" src="https://cdn2.weimob.com/saas/saas-fe-sirius-orion-node/production/zh-CN/503/iocnlogo.png" />
        </div>
      </div>

      <!-- 标题文案 -->
      <div class="vip-levels__title">
        <h1>AIWork365让创作更简单 办公更轻松</h1>
        <p>功能更多 · 体验更好 · 随心更高 · 效率更高 · 随想随出</p>
      </div>
    </div>

    <!-- 会员类型tabs -->
    <div class="vip-levels__tabs" style="width: 699px; margin: 0 auto 53px">
      <NTabs v-model:value="activeTab" type="segment" size="large" animated style="--n-color-segment: #e9d6fc; height: 79px">
        <NTabPane name="ultra" tab="Ultra版会员" />
        <NTabPane name="pro" tab="Pro版会员" />
        <NTabPane name="package" tab="积分加油包" />
      </NTabs>
    </div>

    <!-- 会员类型卡片列表 -->
    <n-spin :spinning="loading">
      <Transition name="fade" mode="out-in">
        <VipCard :service-header="privilegeData?.serviceHeader" :goods="privilegeData?.goods" />
      </Transition>
    </n-spin>

    <!-- FAQ部分 -->
    <div class="vip-levels__faq">
      <h2>常见问题</h2>
      <DoubleColumnFaq :items="faqItems" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { NTabs, NTabPane, NImage, NSpin } from 'naive-ui'
import VipCard from './components/VipCard.vue'
import DoubleColumnFaq from './components/DoubleColumnFaq.vue'
import { Privilege } from './types'
import { useRequest } from 'vue-hooks-plus'
import { fetchGoodsServices } from './api'

interface FAQItem {
  question: string
  answer: string
}

const faqItems: FAQItem[] = [
	{
		question: '会员方案中除赠送权益次数外，绘画和视频等权益是可以同时享有的吗？',
		answer: '可以。但会员方案中介绍的是总积分可以生成单项应用的最大值，绘画、视频等应用各自有独立的积分扣费规则，具体生成次数，请以各功能的实际扣费规则为准。建议您在使用前详细查看具体的扣费规则，以合理规划您的积分使用。如果您需要同时使用多种创作功能，建议您购买更多的积分或选择更高阶的会员方案。',
	},
	{
		question: '积分使用优先级?',
		answer: '如果您同时购买了多个会员方案，积分及会员有效期将累计计算，赠送权益也会叠加。系统会优先扣除即将到期的积分及权益，确保您能高效利用资源。',
	},
	{
		question: '积分的获取方式有哪些?',
		answer: '目前，获取积分仅支持付费购买一种方式。',
	},
	{
		question: '任务在生成中，需要注意什么?',
		answer: '任务暂停后，且在20分钟内没有发送新对话，系统会自动终止该任务，已消耗积分不退。执行生成任务时，若遇到积分不足的情况，需继续生成任务请在10分钟内充值以保持积分余额充足。',
	},
	{
		question: '如果任务暂停，我该怎么充值积分？',
		answer: '若您的任务意外暂停，需充值积分以继续，具体操作如下：1、点击【成为会员】按钮或进入个人中心页面，选择“购买积分”会员套餐，按页面提示完成支付；2、充值成功后，系统将自动恢复任务，您可继续使用平台功能。若需了解更多积分使用说明，请查看相关规则或联系在线客服。',
	},
	{
		question: 'AIWork365积分消耗规则是什么？',
		answer: '本平台实行积分消耗制度。积分消耗与任务的复杂度及时长相关，具体扣费规则因功能而异。计算资源（如处理能力、内存及服务器时间）消耗的越多，所需积分也越多。使用后，若出现积分不足的情况，可选择充值购买积分。',
	},
	{
		question: '如何查看会员权益？',
		answer: '您好，您可以通过访问右上角的个人头像，点击下拉菜单并选择“个人中心”，便捷地查看您所购买的全部会员权益。',
	},
	{
		question: '免费版和VIP会员的区别？',
		answer: '免费版支持基础的产品使用功能，赠送定量的产品功能试用体验，但无法使用全部产品功能及高级模型。VIP会员则畅享全部高阶功能，专属会员加速通道生成速度更快、支持提交多组任务并行、新功能优先体验等权益，同时拥有海量的PPT模板和海报模板，高效办公一步到位。',
	},
	{
		question: '开通会员后是否可申请退款？',
		answer: 'AIWork365作为数字化商品，本产品已为您提供免费体验版本以供试用。请您在购买前务必充分了解并体验产品功能。一旦完成购买，将不支持退款。如有任何疑问，欢迎咨询在线客服获取帮助。',
	},
]

const activeTab = ref<'ultra' | 'pro' | 'package'>('ultra')
const privilegeData = ref<Partial<Privilege>>({})
const { loading } = useRequest(() => fetchGoodsServices<Privilege>({ goodsType: activeTab.value }), {
  refreshDeps: [activeTab],
  onSuccess: (data: Privilege) => {
    privilegeData.value = data
  },
})
</script>

<style lang="less" scoped>
.vip-levels {
  width: 100%;
  position: relative;
  overflow: hidden;
	background: url(/src/assets/images/vip-bg.png) no-repeat 0 0;
	background-size: contain;
}

.vip-levels__header {
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 100px;
  margin-bottom: 56px;
}

.vip-levels__title {
  text-align: center;

  h1 {
    font-size: 60px;
    color: #151515;
  }

  p {
    font-size: 22px;
    color: #3d3d3d;
  }
}

:deep(.n-tabs-tab) {
  font-family: 'Microsoft YaHei';
  font-size: 14px;
  color: #222222;
  border-radius: 17px;
  margin: 0;
  height: 79px;
  line-height: 79px;

  &:deep(.n-tabs-tab--active) {
    background-color: #ffffff;
    color: #222222;
  }
}

.vip-levels__faq {
  margin-top: 120px;
  text-align: center;

  h2 {
    font-size: 60px;
    color: #151515;
    margin-bottom: 50px;
  }
}
</style>
