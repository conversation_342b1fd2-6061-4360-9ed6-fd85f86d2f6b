<template>
  <!-- 基础信息区域 -->
  <section class="bg-white dark:bg-[#242424] rounded-xl shadow-sm p-6 mb-6">
    <h3 class="text-lg font-semibold mb-4 dark:text-white">基础信息</h3>
    <div class="space-y-6">
      <!-- 手机账号 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <component :is="icons.IconPhone" />
          <div class="flex items-center gap-2">
            <span class="text-base font-medium dark:text-gray-200">手机账号</span>
            <span v-if="phoneBind" class="text-sm text-gray-900 dark:text-gray-200 font-medium">{{ user?.phone }}</span>
            <span v-else class="text-sm text-gray-500 dark:text-gray-400">未绑定</span>
          </div>
        </div>
        <button
          v-if="!phoneBind"
          @click="emit('bind-phone')"
          class="bg-gray-900 hover:bg-gray-800 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
        >
          绑定手机号
        </button>
        <button
          v-else
          class="border-2 border-gray-200 dark:border-gray-600 text-gray-600 dark:text-gray-400 px-4 py-2 rounded-lg text-sm font-medium cursor-default"
        >
          已绑定
        </button>
      </div>

      <!-- 微信账号 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <component :is="icons.IconWechat" />
          <div class="flex items-center gap-2">
            <span class="text-base font-medium dark:text-gray-200">微信账号</span>
            <span v-if="wechatBind" class="text-sm text-gray-900 dark:text-gray-200 font-medium">已绑定</span>
            <span v-else class="text-sm text-gray-500 dark:text-gray-400">未绑定</span>
          </div>
        </div>
        <button
          v-if="!wechatBind"
          @click="emit('bind-wechat')"
          class="bg-gray-900 hover:bg-gray-800 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200"
        >
          绑定微信
        </button>
        <button
          v-else
          class="border-2 border-gray-200 dark:border-gray-600 text-gray-600 dark:text-gray-400 px-4 py-2 rounded-lg text-sm font-medium cursor-default"
        >
          已绑定
        </button>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useProfileStore } from '@/store/modules/profile'
import { icons } from '@/plugins/icons'

// Store
const profileStore = useProfileStore()

// Computed properties
const user = computed(() => profileStore.user)
const phoneBind = computed(() => profileStore.phoneBind)
const wechatBind = computed(() => profileStore.wechatBind)

// Events
const emit = defineEmits(['bind-phone', 'bind-wechat'])
</script>
