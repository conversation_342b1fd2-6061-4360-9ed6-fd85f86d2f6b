<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRequest } from 'vue-hooks-plus'
import { NEmpty, NImage, NPagination } from 'naive-ui'
import { fetchConsumeList, type ConsumeListResponse } from '../api'
import Empty from '@/assets/aiwork/images/profile-empty.png'
// 标签页状态
const activeTab = ref('消耗')

// 时间筛选状态
const timeFilter = ref('近30天')
const timeOptions = ['近7天', '近30天', '近3个月', '近1年']

// 分页状态
const page = ref(1)
const pageSize = 10
const records = ref<ConsumeListResponse['rows']>([])
const total = ref(0)

// 获取时间范围
const getDateRange = (filter: string) => {
  const now = new Date()
  const endDate = now.toISOString().split('T')[0]
  let startDate = ''

  switch (filter) {
    case '近7天':
      const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
      startDate = sevenDaysAgo.toISOString().split('T')[0]
      break
    case '近30天':
      const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000)
      startDate = thirtyDaysAgo.toISOString().split('T')[0]
      break
    case '近3个月':
      const threeMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 3, now.getDate())
      startDate = threeMonthsAgo.toISOString().split('T')[0]
      break
    case '近1年':
      const oneYearAgo = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
      startDate = oneYearAgo.toISOString().split('T')[0]
      break
  }

  return { startDate, endDate }
}

// 获取请求参数
const getRequestParams = (pageNum: number = 1) => {
  const { startDate, endDate } = getDateRange(timeFilter.value)
  return {
    page: pageNum,
    pageSize,
    startDate,
    endDate,
    type: activeTab.value === '消耗' ? 2 : (1 as 1 | 2),
  }
}

// 请求数据
const {
  data,
  loading,
  run: fetchData,
} = useRequest(() => fetchConsumeList(getRequestParams(page.value)), {
  manual: true,
})

// 翻页
const onPageChange = (currentPage: number) => {
  page.value = currentPage
  fetchData()
}

// 重置列表并重新加载
const resetAndReload = () => {
  page.value = 1
  records.value = []
  fetchData()
}

// 格式化日期
const formatDate = (dateStr: string) => {
  try {
    const date = new Date(dateStr)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch {
    return dateStr
  }
}

// 格式化消耗量显示
const formatConsumption = (record: ConsumeListResponse['rows'][0]) => {
  if (activeTab.value === '消耗') {
    return record.desc || record.action || '-'
  } else {
    return record.desc || '+'
  }
}

// 获取消耗量的颜色样式
const getConsumptionColor = (record: ConsumeListResponse['rows'][0]) => {
  if (activeTab.value === '消耗') {
    return 'text-red-500 dark:text-red-400'
  } else {
    return 'text-green-500 dark:text-green-400'
  }
}

// 监听数据变化，更新records
watch(
  data,
  (newData) => {
    if (newData) {
      records.value = newData.rows || []
      total.value = newData.count
    }
  },
  { immediate: true },
)

// 监听标签页和时间筛选变化
watch([activeTab, timeFilter], () => {
  resetAndReload()
})

// 组件挂载时初始化
onMounted(() => {
  resetAndReload()
})
</script>

<template>
  <section class="bg-white dark:bg-[#242424] rounded-xl shadow-sm dark:shadow-none p-6">
    <!-- 标题和筛选器 -->
    <div class="flex justify-between items-center mb-6">
      <h3 class="text-lg font-semibold dark:text-white">权益使用明细</h3>
    </div>

    <!-- 标签页 -->
    <div class="flex justify-between w-full">
      <div class="flex gap-2 mb-4">
        <button
          v-for="tab in ['消耗', '购买']"
          :key="tab"
          class="px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          :class="{
            'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400': activeTab === tab,
            'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700/50': activeTab !== tab,
          }"
          @click="activeTab = tab"
        >
          {{ tab }}
        </button>
      </div>
      <div class="relative">
        <select v-model="timeFilter" class="appearance-none bg-white dark:bg-[#2A2A2A] border border-gray-200 dark:border-gray-600 rounded-lg px-4 py-2 pr-8 text-sm text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
          <option v-for="option in timeOptions" :key="option" :value="option">
            {{ option }}
          </option>
        </select>
        <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none h-[38px]">
          <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </div>
      </div>
    </div>

    <!-- 记录列表 -->
    <div v-if="records.length > 0" class="space-y-3">
      <div v-for="(record, index) in records" :key="`${record.createdAt}-${index}`" class="flex justify-between items-center p-4 border border-gray-100 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-[#333333] transition-colors">
        <div>
          <div class="font-medium text-gray-900 dark:text-gray-100">
            {{ record.name }}
          </div>
          <div class="text-gray-400 dark:text-gray-500 text-sm mt-1">
            {{ formatDate(record.createdAt) }}
          </div>
        </div>
        <div :class="getConsumptionColor(record)" class="text-sm font-medium">
          {{ formatConsumption(record) }}
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!loading">
      <n-empty description="暂无记录" class="py-8" style="--n-icon-size: 82px" size="large">
        <template #icon>
          <n-image :src="Empty" preview-disabled />
        </template>
      </n-empty>
    </div>

    <!-- 首次加载状态 -->
    <div v-if="loading && records.length === 0" class="text-center py-8">
      <span class="text-gray-400 text-sm">加载中...</span>
    </div>
    <!-- 分页 -->
    <div v-if="total > pageSize" class="flex justify-end mt-6">
      <n-pagination
        :item-count="total"
        :page="page"
        :page-size="pageSize"
        @update:page="onPageChange"
      />
    </div>
  </section>
</template>

<style scoped>
/* 可以根据实际设计调整样式 */
</style>
