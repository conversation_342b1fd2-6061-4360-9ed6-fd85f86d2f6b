<template>
  <div class="flex gap-3 mb-6 mt-6">
    <button
      v-if="memberData?.freeMembers"
      @click="emit('update:activeMode', 'freeMember')"
      :class="{
        'bg-blue-500 text-white border-blue-500': activeMode === 'freeMember',
        'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200': activeMode !== 'freeMember'
      }"
      class="px-4 py-2 rounded-lg border transition-colors duration-200 font-medium"
    >
      注册赠送会员权益
    </button>
    <button
      v-if="memberData?.member"
      @click="emit('update:activeMode', 'member')"
      :class="{
        'bg-blue-500 text-white border-blue-500': activeMode === 'member',
        'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200': activeMode !== 'member'
      }"
      class="px-4 py-2 rounded-lg border transition-colors duration-200 font-medium"
    >
      充值赠送会员权益
    </button>
    <button
      v-if="memberData?.paperMember"
      @click="emit('update:activeMode', 'paper')"
      :class="{
        'bg-blue-500 text-white border-blue-500': activeMode === 'paper',
        'bg-gray-100 text-gray-600 border-gray-300 hover:bg-gray-200': activeMode !== 'paper'
      }"
      class="px-4 py-2 rounded-lg border transition-colors duration-200 font-medium"
    >
      论文算力包
    </button>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  activeMode: string;
  memberData: any;
}>();

const emit = defineEmits<{
  'update:activeMode': [mode: string]
}>();
</script>
