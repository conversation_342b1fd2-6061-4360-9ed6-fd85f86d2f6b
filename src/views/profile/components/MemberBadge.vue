<template>
  <div class="flex items-center justify-between w-full gap-4">
    <div class="flex items-center gap-1">
      <!-- 付费会员 -->
      <span v-if="memberTypes.paid && showUpgrade" class="bg-gradient-to-l from-[#F1ECFF] to-[#ECDDFF] text-purple-800 text-xs px-2 rounded-full inline-flex items-center gap-1 py-[5px]">
        <img :src="PaidImg" class=" h-[16px]" />
        付费会员
      </span>

      <!-- 算力包会员 -->
      <span v-if="memberTypes.paper" class="bg-gradient-to-l from-[#B2F8FF] to-[#D3FBFF] text-[#2AAFBC] text-xs px-2 rounded-full inline-flex items-center gap-1 py-[5px]">
        <img :src="CountImg" class=" h-[16px]" />
        论文算力包
      </span>
    </div>

    <!-- 升级会员按钮 - 只对免费会员显示，固定在最右边 -->
    <button
      v-if="memberTypes.free && showUpgrade"
      @click="handleClick"
      class="flex items-center justify-center text-white text-sm font-medium transition-all hover:shadow-lg hover:-translate-y-0.5 active:scale-95"
      style="width: 128px; height: 44px; border-radius: 91px; background: linear-gradient(90deg, #6A58FF 0%, #B778FF 100%);"
    >
      升级会员
    </button>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import CountImg from '@/assets/aiwork/images/member-1.png'
import PaidImg from '@/assets/aiwork/images/member-2.png'
import { isBoolean } from 'lodash'

interface Props {
  member: {
    isFree: boolean
    list?: Array<{
      name: string
      desc: string
    }>
  }
  paperMember: {
    count: number
    use: string
  }
	showUpgrade?: boolean
}

const props = withDefaults(defineProps<Props>(), {
	showUpgrade: true
})

defineEmits<{
  upgrade: []
}>()
const handleClick = () => {
	window.$aiwork.openRecharge({
		type: 'ai'
	})
}

// 计算会员类型 - 支持同时存在多种类型
const memberTypes = computed(() => {
  return {
    paper: props.paperMember?.count > 0, // 有算力包
    paid: isBoolean(props.member?.isFree) && props.member?.isFree? true: false, // 付费会员
    free: props.member?.isFree // 纯免费会员（没有算力包且是免费）
  }
})
</script>

<style scoped>
/* 额外样式可以在这里添加 */
</style>
