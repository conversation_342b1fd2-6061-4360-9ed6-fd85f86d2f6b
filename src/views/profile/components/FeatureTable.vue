<template>
  <div class="feature-table">
    <table>
      <thead>
        <tr>
          <th>会员特权</th>
          <th v-for="level in levels" :key="level.name">{{ level.name }}</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="feature in features" :key="feature.name">
          <td>{{ feature.name }}</td>
          <td v-for="level in levels" :key="level.name">
            <span v-if="feature.levels.includes(level.id)" class="checkmark">✓</span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
defineProps({
  levels: {
    type: Array,
    required: true,
    default: () => []
  },
  features: {
    type: Array,
    required: true,
    default: () => []
  }
})
</script>

<style lang="less" scoped>
.feature-table {
  overflow-x: auto;
  margin: 20px 0;

  table {
    width: 100%;
    border-collapse: collapse;
    min-width: 600px;

    th, td {
      padding: 12px 16px;
      text-align: center;
      border: 1px solid #e0e0e0;
    }

    th {
      background-color: #f5f7fa;
      font-weight: 600;
    }

    tr:nth-child(even) {
      background-color: #f9f9f9;
    }

    .checkmark {
      color: #4CAF50;
      font-weight: bold;
    }
  }
}
</style>
