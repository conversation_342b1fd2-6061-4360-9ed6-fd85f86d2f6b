<template>
  <!-- 用户信息区域 -->
  <section class="relative rounded-xl p-6 mb-6 transition-all member-card-gradient">
    <div class="flex items-center gap-4">
      <n-avatar round :size="92" :src="user?.avatar || defaultAvatar" />
      <div class="flex-1">
        <div class="flex items-center gap-2" v-if="memberData">
          <n-ellipsis class="text-xl font-normal w-[250px]">{{ user?.nickname || '用户' }}</n-ellipsis>
          <MemberBadge :member="memberData?.member" :paperMember="memberData?.paperMember" @upgrade="handleUpgrade" />
        </div>
        <div class="text-[#878787] dark:text-gray-500 text-base leading-6 mt-1">UID: {{ user?.uid || '未知' }}</div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { NAvatar, NEllipsis } from 'naive-ui'
import MemberBadge from './MemberBadge.vue'
import defaultAvatar from '@/assets/avatar.jpg'
import { getUser } from '@/store/modules/auth/helper'

const props = defineProps<{
  memberData?: any
}>()

const emit = defineEmits(['upgrade'])

const user = getUser()

const handleUpgrade = () => {
  emit('upgrade')
}
</script>

<style scoped>
.member-card-gradient {
  background: linear-gradient(85deg, #ffffff 68%, #f6e8ff 95%);
  box-shadow: 0px 4px 14px 0px rgba(223, 223, 223, 0.26);
  transition: all 0.3s ease;
}

.member-card-gradient:hover {
  box-shadow: 0px 6px 18px 0px rgba(223, 223, 223, 0.36);
}

.dark .member-card-gradient {
  background: linear-gradient(85deg, #242424 68%, #2d1a36 95%);
  box-shadow: 0px 4px 14px 0px rgba(0, 0, 0, 0.26);
}

.dark .member-card-gradient:hover {
  box-shadow: 0px 6px 18px 0px rgba(0, 0, 0, 0.36);
}
</style>
