<template>
  <!-- 绑定订单号 -->
  <div class="flex items-center gap-4">
    <component :is="icons.IconOrder" />
    <div class="flex-1">
      <div class="flex items-center gap-4">
        <span class="text-base font-medium dark:text-gray-200">绑定订单号</span>
        <div class="flex-1 flex gap-3 items-center justify-between">
          <input
            v-model="orderNumber"
            type="text"
            placeholder="订单编号"
            class="w-[270px] max-w-full border-2 border-gray-300 dark:border-gray-600 rounded-lg px-4 py-2 text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:border-blue-500 focus:outline-none transition-colors"
          />
          <button
            @click="handleBindOrder"
            :disabled="loading"
            class="bg-gray-900 hover:bg-gray-800 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg text-sm font-medium transition-colors duration-200 min-w-[60px]"
          >
            {{ loading ? '绑定中...' : '绑定' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useProfileStore } from '@/store/modules/profile';
import { icons } from '@/plugins/icons';

const profileStore = useProfileStore();
const orderNumber = ref('');
const loading = ref(false);

const emit = defineEmits<{
  'bind-success': []
}>();

const handleBindOrder = async () => {
  if (!orderNumber.value.trim()) {
    return;
  }

  loading.value = true;
  try {
    const success = await profileStore.bindPayOrder(orderNumber.value.trim());
    if (success) {
      orderNumber.value = '';
      emit('bind-success');
    }
  } finally {
    loading.value = false;
  }
};
</script>
