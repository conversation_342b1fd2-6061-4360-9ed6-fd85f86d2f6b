<template>
  <!-- 权益统计区域 -->
  <!-- 付费会员 -->
  <div v-if="activeMode === 'member'" class="flex flex-col gap-y-[15px] p-[21px] rounded-lg bg-cover bg-center bg-no-repeat" :style="{ backgroundImage: `url(${paidBg})` }">
    <div class="flex justify-between">
      <span class="text-[#212121] text-lg">{{ memberPoints }}积分</span>
      <span v-if="memberData?.member?.endDate && !memberData?.member?.isPermanent" class="text-[#7F4DFF]">{{ dayjs(memberData?.member?.endDate).format('YYYY.MM.DD HH:mm') }}到期</span>
      <span v-else-if="memberData?.member?.isPermanent" class="text-[#7F4DFF]">终身会员</span>
    </div>
    <div class="flex flex-row gap-1">
      <div v-for="stat in paidMemberStats" :key="stat.name" class="flex-1 p-3 bg-[rgba(255,255,255,0.57)] box-border border border-gray-100 dark:border-gray-700 rounded-lg text-center hover:shadow-sm transition-all">
        <div class="text-lg font-bold text-gray-800 dark:text-gray-200 mb-1"
          :class="{'text-left': !stat.name}">
          {{ stat.value }}
        </div>
        <div class="text-gray-500 dark:text-gray-400 text-xs">
          {{ stat.name }}
        </div>
      </div>
    </div>
  </div>

  <!-- 免费会员 -->
  <div v-if="activeMode === 'freeMember'" class="flex flex-col gap-y-[15px] p-[21px] rounded-lg bg-cover bg-center bg-no-repeat" :style="{ backgroundImage: `url(${freeBg})` }">
    <div v-if="memberData" class="flex flex-row gap-1">
      <div v-for="stat in freeMemberStats" :key="stat.name" class="flex-1 p-3 bg-[rgba(255,255,255,0.57)] box-border border border-gray-100 dark:border-gray-700 rounded-lg text-center hover:shadow-sm transition-all">
        <div class="text-lg font-bold text-gray-800 dark:text-gray-200 mb-1">
          {{ stat.value }}
        </div>
        <div class="text-gray-500 dark:text-gray-400 text-xs">
          {{ stat.name }}
        </div>
      </div>
    </div>
  </div>

  <!-- 论文算力包 -->
  <div v-if="activeMode === 'paper' && memberData" class="max-w-[434px] flex flex-col gap-y-[15px] p-[21px] rounded-lg bg-cover bg-center bg-no-repeat" :style="{ backgroundImage: `url(${creditBg})` }">
    <div class="flex flex-col gap-y-3">
      <MemberBadge :member="memberData?.member" :paperMember="memberData?.paperMember" @upgrade="handleUpgrade" :show-upgrade="false" />
      <div class="flex justify-between text-sm text-[#333333]">
        <span>算力消耗</span>
        <span>{{ memberData?.paperMember?.use }} / {{ memberData?.paperMember?.count }}算力</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import MemberBadge from './MemberBadge.vue';
import freeBg from '@/assets/aiwork/images/free-bg.png';
import paidBg from '@/assets/aiwork/images/paid-bg.png';
import creditBg from '@/assets/aiwork/images/credit-bg.png';
import dayjs from 'dayjs';

const props = defineProps<{
  activeMode: string;
  memberData?: any;
}>();

const emit = defineEmits(['upgrade']);

// 免费会员权益数据
const freeMemberStats = computed(() => {
  const freeList = props.memberData?.freeMembers;
  if (freeList && freeList.length > 0) {
    return freeList.map((item) => ({
      name: item.name,
      value: item.desc,
    }));
  }
  return [];
});

// 付费会员权益数据
const paidMemberStats = computed(() => {
  const memberList = props.memberData?.member?.list;
  if (memberList && memberList.length > 0) {
    return memberList.map((item) => ({
      name: item.name,
      value: item.desc,
    }));
  }
  // 如果有member数据但list是空数组，显示默认权益说明
  if (props.memberData?.member) {
    return [{
      name: '',
      value: '全站尊享AI聊天、智能创作、AI PPT、AI视频、AI绘图、AI海报与文件转换等权益'
    }];
  }
  return [];
});

const memberPoints = computed(() => {
  if (props.memberData?.member) {
    const count = parseInt(props.memberData.member.count, 10) || 0;
    const use = parseInt(props.memberData.member.use, 10) || 0;
    return count - use;
  }
  return '-';
});

const handleUpgrade = () => {
  emit('upgrade');
};
</script>
