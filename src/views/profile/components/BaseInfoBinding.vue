<template>
  <div>
    <h3 class="text-lg font-semibold mb-4 dark:text-white">基础信息</h3>
    <div class="space-y-6 mb-8">
      <!-- 手机账号 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <component :is="icons.IconPhone" />
          <div class="flex items-center gap-2">
            <span class="text-base font-medium dark:text-gray-200">手机账号</span>
            <span v-if="phoneBind" class="text-sm text-gray-900 dark:text-gray-200 font-medium">{{ model.phone }}</span>
            <span v-else class="text-sm text-gray-500 dark:text-gray-400">未绑定</span>
          </div>
        </div>
        <button v-if="!phoneBind" @click="handleBindPhone" class="bg-gray-900 dark:bg-gray-700 text-white px-6 py-2.5 rounded-full text-sm font-medium hover:bg-gray-800 dark:hover:bg-gray-600 transition-colors">绑定手机号</button>
        <button v-else class="border-2 border-gray-200 dark:border-gray-600 text-gray-600 dark:text-gray-300 px-6 py-2.5 rounded-full text-sm font-medium bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">已绑定</button>
      </div>

      <!-- 微信账号 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-4">
          <component :is="icons.IconWechat" />
          <div class="flex items-center gap-2">
            <span class="text-base font-medium dark:text-gray-200">微信账号</span>
            <span v-if="wechatBind" class="text-sm text-gray-900 dark:text-gray-200 font-medium">已绑定</span>
            <span v-else class="text-sm text-gray-500 dark:text-gray-400">未绑定</span>
          </div>
        </div>
        <button v-if="wechatBind" class="border-2 border-gray-200 dark:border-gray-600 text-gray-600 dark:text-gray-300 px-6 py-2.5 rounded-full text-sm font-medium bg-gray-50 dark:bg-gray-700/50 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors">已绑定</button>
        <button v-else @click="handleBindWechat" class="bg-gray-900 dark:bg-gray-700 text-white px-6 py-2.5 rounded-full text-sm font-medium hover:bg-gray-800 dark:hover:bg-gray-600 transition-colors">绑定微信</button>
      </div>

      <!-- 绑定订单号 -->
      <div class="flex items-center gap-4 mb-3">
        <component :is="icons.IconOrder" />
        <div class="flex-1">
          <div class="flex items-center gap-4">
            <span class="text-base font-medium dark:text-gray-200">绑定订单号</span>
            <div class="flex-1 flex gap-3 items-center justify-between">
              <input
                v-model="orderNumber"
                type="text"
                placeholder="订单编号"
                class="w-[270px] max-w-full border-2 border-gray-200 dark:border-gray-600 rounded-xl px-4 py-3 text-sm bg-white dark:bg-gray-800 dark:text-gray-200 focus:outline-none focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
              />
              <button @click="handleBindOrder" class="bg-gray-900 dark:bg-gray-700 text-white px-6 py-3 rounded-xl text-sm font-medium hover:bg-gray-800 dark:hover:bg-gray-600 transition-colors whitespace-nowrap">绑定</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useMessage } from 'naive-ui'
import { icons } from '@/plugins/icons'
import { getUser } from '@/store/modules/auth/helper'
import { fetchBindPay } from '../api'

const emit = defineEmits(['bind-phone', 'bind-wechat', 'bind-success'])

const message = useMessage()
const user = getUser()

const model = ref({
  nickname: user?.nickname || '',
  phone: user?.phone || '',
  openId: user?.openId || '',
  uid: user?.uid || '',
})

const wechatBind = computed(() => !!model.value.openId)
const phoneBind = computed(() => !!model.value.phone)

// 绑定订单号输入框的值
const orderNumber = ref('')

const handleBindPhone = () => {
  emit('bind-phone')
}

const handleBindWechat = () => {
  emit('bind-wechat')
}

const handleBindOrder = () => {
  if (!orderNumber.value.trim()) {
    message.warning('请输入订单编号')
    return
  }

  fetchBindPay({ payOrderNo: orderNumber.value })
    .then((res) => {
      message.success('绑定成功')
      orderNumber.value = ''
      emit('bind-success')
    })
    .catch((err) => {
      message.error('绑定失败，请检查订单号是否正确')
    })
}
</script>
