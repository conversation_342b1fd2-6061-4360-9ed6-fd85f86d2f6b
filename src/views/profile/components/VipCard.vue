<template>
  <div class="vip-card">
    <!-- 左侧服务分类 -->
    <div class="service-categories">
      <!-- Base section -->
      <div class="category-group">
        <div class="category-item block block-large">
          <img v-if="serviceHeader?.base?.url" :src="serviceHeader.base.url" alt="base icon" class="service-icon" />
          <span>{{ serviceHeader?.base?.value }}</span>
        </div>
        <div class="category-list">
          <div v-for="(item, itemIndex) in serviceHeader?.base?.list" :key="itemIndex" class="list-item block block-small">
            {{ item }}
          </div>
        </div>
      </div>

      <!-- Services section -->
      <div v-for="(service, index) in serviceHeader?.services" :key="service.type" class="category-group">
        <div class="category-item block block-large">
          <img v-if="service.url" :src="service.url" alt="service icon" class="service-icon" />
          <span>{{ service.value }}</span>
        </div>
        <div class="category-list">
          <div v-for="(item, itemIndex) in service.list" :key="itemIndex" class="list-item block block-small">
            {{ item }}
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧会员卡列表 -->
    <div class="vip-cards">
      <div v-for="good in goods" :key="good.base.id" class="vip-card-item" :class="{ active: good.base.isSelect }" :data-type="good.base.type" @mouseover="handleCardHover(good.base.id)">
        <!-- 会员卡头部 -->
        <div class="card-header block block-large">
          <!-- <img v-if="good.base.markUrl" :src="good.base.markUrl" class="card-mark" /> -->
          <h3 class="card-title">{{ good.base.title }}</h3>
          <NImage v-if="good.base.markUrl" :src="good.base.markUrl" height="42px" class="card-tag" />
        </div>

        <!-- 价格信息 -->
        <div class="price-info block block-small">
          <div class="current-price">
            <span class="amount">¥{{ good.base.price }}</span>
          </div>
          <div v-if="good.base.originalPrice" class="original-price">¥{{ good.base.originalPrice }}</div>
        </div>

        <!-- 积分信息 -->
        <div class="points-info block block-small">{{ good.base.points }}</div>

        <!-- 购买按钮 -->
        <button class="buy-button block block-small" :class="{ 'is-selected': good.base.isSelect }" @click="handleBuy(good.base)">立即购买</button>

        <!-- 服务列表 -->
        <div class="service-list">
          <template v-for="(serviceCategory, categoryIndex) in good?.services" :key="categoryIndex">
            <div class="service-category">
              <div class="category-title block block-large"></div>
              <div class="service-item" v-for="service in serviceCategory.list" :key="service.type">
                <div
                  class="service-value block block-small"
                  :class="{
                    'is-active': service.value === true,
                    'is-text': typeof service.value !== 'boolean',
                    'is-inactive': service.value === false,
                  }"
                >
                  <NImage v-if="typeof service.value === 'boolean' && service.value" :src="good.base.type === 'pro' ? BlueCheckedImg : good.base.type === 'package' ? OrangeCheckedImg : PurpleCheckedImg" preview-disabled width="16" height="16" />
                  <span v-else>{{ service.value }}</span>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { NImage } from 'naive-ui'
import type { Privilege } from '../types'
import PurpleCheckedImg from '@/assets/images/purple-checked.png'
import BlueCheckedImg from '@/assets/images/blue-checked.png'
import OrangeCheckedImg from '@/assets/images/orange-checked.png'
import { defineProps, watch, PropType } from 'vue'

const props = defineProps({
  serviceHeader: {
    type: Object as PropType<Privilege['serviceHeader']>,
    default: () => ({}),
  },
  goods: {
    type: Array as PropType<Privilege['goods']>,
    default: () => [],
  },
})
watch(props, (newV) => console.log(newV))
const handleCardHover = (id: number) => {
  props?.goods?.forEach((good) => {
    good.base.isSelect = good.base.id === id
  })
}
const handleBuy = ({ type, id: goodsId }) => {
  window.$aiwork.openRecharge({
    type,
    goodsId,
  })
}
</script>

<style lang="less" scoped>
// 定义统一的block类
.block {
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #ecedee;
}
.block-large {
  height: 82px;
}

.block-small {
  height: 48px;
}
.vip-card {
  display: flex;
  gap: 24px;
  border-radius: 12px;
  min-height: 600px;
  max-width: 1400px;
  margin: 0 auto;
}

.service-categories {
  display: flex;
  flex-direction: column;
  min-width: 180px;
  border-radius: 8px;

  .category-group {
    border-bottom: 1px solid #eee;
    &:last-child {
      border-bottom: none;
    }
  }

  .category-item {
    gap: 8px;
    color: #151515;
    font-size: 26px;
    width: 100%;
    display: flex;
    justify-content: flex-start;
    .service-icon {
      width: 37px;
      height: 37px;
      object-fit: contain;
    }
  }

  .category-list {
    display: flex;
    flex-direction: column;

    .list-item {
      color: #666;
      font-size: 13px;
      position: relative;
      width: 100%;
      display: block;
      line-height: 48px;
    }
  }
}

.vip-cards {
  display: flex;
  gap: 16px;
  flex: 1;
  overflow-x: auto;

  .vip-card-item {
    flex: 1;
    min-width: 10%;
    max-width: 32.4%;
    border: 1px solid #eee;
    border-radius: 20px;
    transition: all 0.3s;
    background: #f7f7fc;
    display: flex;
    flex-direction: column;
    align-items: center;

    &:hover {
      border-color: #6366f1;
      box-shadow: 0 4px 20px rgba(99, 102, 241, 0.1);
    }

    &.active {
      &[data-type='ultra'] {
        border-color: #7f4dff;
        background: #f3f3fe;
      }
      &[data-type='pro'] {
        border-color: #67a0ff;
        background: #f1f6fe;
      }
      &[data-type='package'] {
        border-color: #ffb668;
        background: #fdf9e8;
      }
    }
  }
}

.card-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  width: 100%;
  border-bottom: 1px dashed #eee;
  .card-title {
    color: #262626;
    font-size: 20px;
    line-height: 20px;
    font-weight: 700;
  }
  .card-mark {
    width: 48px;
    height: 48px;
    object-fit: contain;
  }

  .card-tag {
    position: absolute;
    top: 0px;
    right: 0px;
    height: 42px;
  }
}

.price-info {
  text-align: center;
  display: flex;
  align-items: center;
  column-gap: 15px;
  .current-price {
    .amount {
      font-size: 18px;
      color: #262626;
    }
  }

  .original-price {
    font-size: 14px;
    color: #999;
    text-decoration: line-through;
    margin-top: 4px;
  }
}

.points-info {
  font-size: 14px;
  color: #946aff;
  padding: 4px 12px;
  border-radius: 12px;

  .vip-card-item[data-type='pro'] & {
    color: #3783ff;
  }

  .vip-card-item[data-type='package'] & {
    color: #ff5d44;
  }
}

.buy-button {
  width: 120px;
  padding: 8px 0;
  background: rgba(99, 102, 241, 0.1);
  color: #3d3d3d;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;

  &:hover,
  &.is-selected {
    background: linear-gradient(270deg, #b679ff 0%, #6b5efe 100%);
    color: #fff;
    transform: translateY(-1px);
  }

  &.is-selected {
    .vip-card-item[data-type='pro'] & {
      background: linear-gradient(90deg, #0e56ff 0%, #5e8efe 100%);
    }

    .vip-card-item[data-type='package'] & {
      background: linear-gradient(90deg, #ff5d44 0%, #ff7505 100%);
    }
  }
}

.service-list {
  width: 100%;
  display: flex;
  flex-direction: column;
  border-top: 1px dashed #eee;

  .service-category {
    display: flex;
    flex-direction: column;

    .category-title {
      font-size: 14px;
      color: #666;
      text-align: center;
      .block();
      .block-large();
    }
  }

  .service-item {
    .service-value {
      font-size: 14px;
      color: #666;
      transition: all 0.3s;
      .block();
      .block-small();

      &.is-active {
        color: #6366f1;
        font-weight: 500;

        .status-icon {
          color: #6366f1;
        }
      }

      &.is-inactive {
        color: #999;

        .status-icon {
          color: #999;
        }
      }

      &.is-text {
        color: #333;
      }

      .status-icon {
        font-style: normal;
        font-size: 16px;
      }
    }
  }
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
