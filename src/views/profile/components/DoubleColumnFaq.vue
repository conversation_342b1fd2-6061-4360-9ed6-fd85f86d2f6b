<template>
  <div class="double-column-faq">
    <div class="faq-column">
      <div
        v-for="(item, index) in leftColumnItems"
        :key="'left-' + index"
        class="faq-item"
        :class="{ 'is-active': activeIndex === index }"
      >
        <div class="faq-container">
          <div
            class="faq-question"
            @click="toggleItem(index)"
          >
            <p class=" text-left">{{ item.question }}</p>
            <NIcon size="20">
              <n-icon :component="activeIndex === index ? Minus : Plus" />
            </NIcon>
          </div>
          <transition name="slide">
            <div v-if="activeIndex === index" class="faq-answer">
              <div class="divider"></div>
              <p>{{ item.answer }}</p>
            </div>
          </transition>
        </div>
      </div>
    </div>
    <div class="faq-column">
      <div
        v-for="(item, index) in rightColumnItems"
        :key="'right-' + (index + leftColumnItems.length)"
        class="faq-item"
        :class="{ 'is-active': activeIndex === (index + leftColumnItems.length) }"
      >
        <div class="faq-container">
          <div
            class="faq-question"
            @click="toggleItem(index + leftColumnItems.length)"
          >
            <p>{{ item.question }}</p>
            <NIcon size="20">
              <n-icon :component="activeIndex === (index + leftColumnItems.length) ? Minus : Plus" />
            </NIcon>
          </div>
          <transition name="slide">
            <div v-if="activeIndex === (index + leftColumnItems.length)" class="faq-answer">
              <div class="divider"></div>
              <p>{{ item.answer }}</p>
            </div>
          </transition>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue';
import { NIcon } from 'naive-ui';
import { Minus, Plus } from '@icon-park/vue-next';

interface FAQItem {
  question: string;
  answer: string;
}

const props = defineProps<{
  items: FAQItem[];
}>();

const activeIndex = ref<number | null>(null);

const leftColumnItems = computed(() => {
  return props.items.slice(0, Math.ceil(props.items.length / 2));
});

const rightColumnItems = computed(() => {
  return props.items.slice(Math.ceil(props.items.length / 2));
});

const toggleItem = (index: number) => {
  activeIndex.value = activeIndex.value === index ? null : index;
};
</script>

<style scoped>
.double-column-faq {
  display: flex;
  gap: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.faq-column {
  flex: 1;
  min-width: 0;
}

.faq-item {
  margin-bottom: 20px;
}

.faq-container {
  width: 100%;
  border: 1px solid #B8B8B8;
  border-radius: 10px;
  overflow: hidden;
}

.faq-question {
  width: 100%;
  height: 90px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 30px;
  cursor: pointer;
}

.faq-question p {
  font-size: 24px;
  color: #3D3D3D;
}

.faq-answer {
  width: 100%;
  padding: 0 30px 30px;
  background: white;
  border-radius: 10px;
  margin: 0 auto;
  text-align: left;
}

.faq-answer p {
  font-size: 16px;
  color: #666666;
}

.divider {
  width: 100%;
  height: 1px;
  background: #B8B8B8;
  margin-bottom: 20px;
}

.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
  max-height: 500px;
  overflow: hidden;
}

.slide-enter-from,
.slide-leave-to {
  max-height: 0;
  opacity: 0;
  padding-top: 0;
  padding-bottom: 0;
}

@media (max-width: 768px) {
  .double-column-faq {
    flex-direction: column;
  }
}
</style>
