import { post } from "@/utils/request";

// http://showdoc.weimob.com/index.php?s=/560&page_id=36178
export function fetchBindPay<T>(params: any) {
	return post<T>({
		url: `/api3/aiwork/sign/bindPay`,
		data: {
			payOrderNo: "",
			...params,
		},
	});
}
// http://showdoc.weimob.com/index.php?s=/560&page_id=36179
export function fetchGoodsServices<T>(params: { goodsType: 'pro' | 'ultra' | 'package' } = { goodsType: 'pro' }) {
	return post<T>({
		url: `/api3/aiwork/goods/services`,
		data: {
			serviceId: "",
			...params,
		},
	});
}
export interface PersonalDetail {
	freeMembers: List[]
	member: Member;
	paperMember: PaperMember;
}

interface PaperMember {
	count: number;
	use: string;
}

interface Member {
	type: 'ultra' | 'pro' | 'package'
	isFree: boolean;
	isPermanent?: boolean;
	endDate?: string;
	count?: number;
	use?: string;
	list: List[];
}

interface List {
	name: string;
	desc: string;
}
// http://showdoc.weimob.com/index.php?s=/560&page_id=36180
export function fetchPersonalDetail2(params: any = {}) {
	return post<PersonalDetail>({
		url: `/api3/aiwork/personal/detail2`,
		data: {
			...params,
		},
	});
}

export interface ConsumeListResponse {
	count: number;
	rows: Array<{
		createdAt: string;
		name: string;
		action: string;
		type: string;
		desc: string;
	}>;
}

export function fetchConsumeList<T = ConsumeListResponse>(params: {
	page?: number;
	pageSize?: number;
	startDate?: string;
	endDate?: string;
	type?: 1 | 2; // 1购买，2消耗
}) {
	return post<T>({
		url: `/api3/aiwork/consume/list`,
		data: params,
	});
}
