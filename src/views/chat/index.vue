<script setup lang="ts">
import type { Ref } from "vue";
import type { CSSProperties } from "vue";
import { computed, onMounted, onUnmounted, ref, inject, watch, provide, reactive } from "vue";
import { useRoute, useRouter } from "vue-router";
import { storeToRefs } from "pinia";
import {
	NAutoComplete,
	NButton,
	NInput,
	useDialog,
	useMessage,
	NImage,
	NTag,
	NText,
	NSwitch,
	NPopover,
	NTooltip,
	NUpload,
	UploadCustomRequestOptions,
	NAlert,
	NProgress,
	NEllipsis,
	UploadFileInfo,
	NAvatar,
	NIcon,
} from "naive-ui";
import html2canvas from "html2canvas";
import { Message, Default } from "./components";
import { useScroll } from "../hooks/useScroll";
import { useChat } from "../hooks/useChat";
import { useCopyCode } from "../hooks/useCopyCode";
import { useUsingContext } from "../hooks/useUsingContext";
import HeaderComponent from "./components/Header/index.vue";
import { HoverButton, SvgIcon, Drawer } from "@/components/common";
import { useBasicLayout } from "@/hooks/useBasicLayout";
import {
	useAuthStore,
	useChatStore,
	usePromptStore,
	useSuggestionStore,
} from "@/store";
import {
	fetchChatAPIProcess,
	riskControl,
	chatbotsList,
	upload,
	chatbotShortcuts,
} from "@/chatgpt";
import { Member, AppList } from "@/components/common";
import { t } from "@/locales";
import Permission from "../components/Permission.vue";
import markList from "@/marked/marked";
import apps from "@/assets/apps.svg";
import { captchaInit } from "@/utils/functions";
import { Delete16Regular } from "@vicons/fluent";
import ApplicationSvg from "@/assets/aiwork/svg/application.svg";
import DirSvg from "@/assets/aiwork/svg/dir.svg";
import { fetchChatIcon, fetchQuestions, fetchUploadFile } from "./api";
import { SelfRequestCode } from "@/utils/request";
import getFileType from '@/utils/fileType';
import GlobalFloat from "@/components/common/GlobalFloat/index.vue";
import FileCard from './components/FileCard/index.vue'
import FileSuggestion from './components/FileSuggestion/index.vue'
import { useRequest } from "vue-hooks-plus";
import { ShortCuts, ThinkType } from "./types";
import ShortCut from './components/ShortCut/index.vue'
import AiAgentImg from '@/assets/images/ai_agent.png'
import AgentWelcome from "./components/AgentWelcome.vue";
import DeepseekChatImg from '@/assets/images/deepseek_chat.png'
import ChatKnowledgeIcon from '@/assets/chat_knowledge.svg';
import KnowledgeButton from "@/components/common/KnowledgeButton/index.vue";
import { getCredit } from "@/store/modules/auth/helper";


provide("addPrompt", (prompt: string) => handleSubmit(false, prompt))
const router = useRouter()

type ChatStatus = {
	uuid: string;
	chatIndex: number;
	stepText: string[]
}

const authStore = useAuthStore();
let controller = new AbortController();
const openLongReply = import.meta.env.VITE_GLOB_OPEN_LONG_REPLY === "true";

const route = useRoute();
const dialog = useDialog();
const ms = useMessage();

const chatStore = useChatStore();
const suggestionStore = useSuggestionStore();
const message = useMessage();

useCopyCode();

const { isMobile, isPC } = useBasicLayout();
const { addChat, updateChat, updateChatSome, getChatByUuidAndIndex } =
	useChat();
const { scrollRef, scrollToBottom, scrollToBottomIfAtBottom } = useScroll();
const { usingContext, toggleUsingContext } = useUsingContext();

const { uuid } = route.params as { uuid: string };

const dataSources = computed(() => chatStore.getChatByUuid(+uuid));
const currentHistory: any = computed(() => chatStore.getHistory(+uuid));
const suggestions = suggestionStore.getSetting(+uuid);
const suggestion = ref<any>(suggestions);
const fileUploading = ref(false);
const conversationList = computed(() =>
	dataSources.value.filter((item) => !item.inversion && !item.error)
);

const prompt = ref<string>("");
const loading = ref<boolean>(false);
const inputRef = ref<Ref | null>(null);
const showMember = ref<boolean>(false);
const needPermission = ref<boolean>(false);
const showApp = ref<boolean>(false);
const isMock = ref<boolean>(false);
const mockIndex = ref<number>(1);
const mockId = ref<number>(1);
// 是否联网
const isNetwork = ref(false);
// 是否深度思考
const isDeepseek = ref(true);
//知识库
const activeKnowledgeId = ref(null);
const disableNetwork = ref<string>("")
// 上传文件
const fileList = ref<any[]>([]);
// 添加PromptStore
const promptStore = usePromptStore();
const promptGroupList = ref([
	{
		name: "写作助手",
		id: 1,
		selected: true,
		icon: "ion:create-sharp",
		child: [
			"给我创作一个短视频剧情,穿越题材的王爷剧本 →",
			"帮我写一个关于“口红色号安利”的小红书文案 →",
			"给一个时尚女士挎包赋予生命,以挎包的视角,写一篇从潮流到过时的故事势  →",
		],
	},
	{
		name: "职场效率",
		id: 2,
		icon: "ic:round-question-answer",
		child: [
			"帮我写一份互联网实习工作周报  →",
			"请作为一名市场营销专家,制定一份[洗护用品]中秋节市场营销计划",
			"帮我写一份开学典礼致辞演讲稿   →",
		],
	},
	// {
	// 	name: "教育学习",
	// 	id: 3,
	// 	icon: "ic:baseline-menu-book",
	// 	child: [
	// 		"请设计一个生物课教案,课程主题是细胞的结构和功能.  →",
	// 		"你是一位考研的指导老师,为我制定一份为期6个月的英语专业课课程考研复习规划  →",
	// 		// "假如你是一个学前班教师,有哪些方法可以增加课堂上与孩子们的互动 →",
	// 	],
	// },
]);
const fileSuggestionPromptList = ref([
	{
		suggestion: "提炼文档主要内容"
	},
	{
		suggestion: "对文档内容润色"
	}
])
const currentMessageId = ref();
const suggestionList = ref();

const chatStatus = reactive<ChatStatus>({
	uuid: "",
	chatIndex: -1,
	stepText: [],
})
const latestFileInConversation = ref(false); // 最新文件是否在聊天中

provide("chatStatus", chatStatus);

// 使用storeToRefs,保证store修改后,联想部分能够重新渲染
const { promptList: promptTemplate } = storeToRefs<any>(promptStore);

// 未知原因刷新页面,loading 状态不会重置,手动重置
dataSources.value.forEach((item, index) => {
	if (item.loading) updateChatSome(+uuid, index, { loading: false });
});

function handleSubmit(ismock?: boolean, msg?: string) {
	if (ismock) {
		isMock.value = true;
	} else {
		isMock.value = false;
	}
	if (msg) {
		prompt.value = msg;
	}
	// 判断chatstore长度 如果没有就新建一个

	// if (dataSources.value.length === 0) {
	// 	chatStore.addHistory({
	// 		title: '新建会话',
	// 		uuid: 1002,
	// 		isEdit: false,
	// 		type: 'chat',
	// 		createTime: new Date().getTime()
	// 	})
	// }

	onConversation();
}
function addPrompt(item: any, msg: string, index: number) {
	prompt.value = msg;
	mockIndex.value = index;
	mockId.value = item?.id;
	handleSubmit(true);
}

// 开始聊天
async function onConversation() {
	let message = prompt.value;
	if (loading.value) return;

	if (!message || message.trim() === "") return;

	controller = new AbortController();
	inputRef.value?.focus();
	if (!latestFileInConversation.value && fileList.value?.length) {
		const file = fileList.value.at(-1);
		addChat(+uuid, {
			dateTime: new Date().toLocaleString(),
			text: message,
			fileObj: {
				name: file.name || file?.file?.name,
				size: file?.file?.size || file?.size,
				type: file.fileType || 'pdf',
			},
			inversion: true,
			error: false,
			conversationOptions: null,
			requestOptions: { prompt: message, options: null },
		});
		latestFileInConversation.value = true;
	} else {
		addChat(+uuid, {
			dateTime: new Date().toLocaleString(),
			text: message,
			inversion: true,
			error: false,
			conversationOptions: null,
			requestOptions: { prompt: message, options: null },
		});
	}

	scrollToBottom();

	loading.value = true;
	prompt.value = "";

	let options: Chat.ConversationRequest = {};

	const lastContext: any =
		conversationList.value[conversationList.value.length - 1]
			?.conversationOptions;

	if (currentMessageId.value && suggestions?.id !== currentMessageId.value) {
		delete lastContext?.parentMessageId;
		currentMessageId.value = null;
	}

	if (lastContext && usingContext.value) options = { ...lastContext };

	addChat(+uuid, {
		dateTime: new Date().toLocaleString(),
		text: "",
		loading: true,
		inversion: false,
		error: false,
		conversationOptions: null,
		requestOptions: { prompt: message, options: { ...options } },
	});
	scrollToBottom();

	// setTimeout(() => {
	// 	updateHistoryIcon(+uuid, dataSources.value.length - 1)
	// }, 300)

	if (isMock.value) {
		const cur: any = markList.find((item) => item.id === mockId.value);
		writing(cur.child[mockIndex.value].split(""), 0);
		return;
	}

	try {
		let lastText = "";
		// 文本请求
		const fetchChatAPIOnce = async (captchaData = {}) => {
			loading.value = true;
			updateChatSome(+uuid, dataSources.value.length - 1, {
				dateTime: new Date().toLocaleString(),
				text: "",
				inversion: false,
				error: false,
				loading: true,
			});
			chatStatus.uuid = uuid
			if (isNetwork.value) chatStatus.stepText = ['正在分析...']
			chatStatus.chatIndex = dataSources.value.length - 1
			let searchInfo = null
			let thinkType = ThinkType.None
			let questionInfo: { id?: number } = {}
			let messageList: string[] = []

			await fetchChatAPIProcess<Chat.ConversationResponse>({
				prompt: message,
				...captchaData,
				options,
				chatbotId: suggestion?.value?.id,
				signal: controller.signal,
				data: {
					files: fileList.value.map((item) => item.etag),
					isNetwork: isNetwork.value,
					isDeepseek: isDeepseek.value,
					publishAgentId: chatStore.getHistory(+uuid)?.agentId || null,
					knowledgeId: activeKnowledgeId.value,
				},
				onDownloadProgress: ({ event }) => {
					// timer && clearInterval(timer);
					const xhr = event.target;
					const { responseText } = xhr;
					let isLimit = false;
					try {
						if (responseText) {
							isLimit = JSON.parse(responseText)?.data?.isLimit;
							if (isLimit) {
								showMember.value = isLimit;
							}
						}
					} catch (error) { }

					try {
						let chunks = responseText
							.split("\n")
							.map((data) => JSON.parse(data));
						if (!questionInfo?.id) {
							questionInfo = chunks?.[chunks?.length - 1]?.quesInfo;
						}
						let text = chunks.map((data) => data.text).join("");
						let thinkInfo = chunks?.map(item => item?.reasoning_content)?.join("") || ""
						// 判断开启深度搜索
						if (isDeepseek.value) {
							if (!text?.length || text === " ") {
								thinkType = ThinkType.None
							} else if (text?.length > 0) {
								thinkType = ThinkType.Answer
							} else {
								thinkType = ThinkType.Thinking
							}
						}

						const data = chunks.at(-1);

						const isCompute = data.detail?.choices[0].finish_reason;
						const searchInfoRes = chunks.find(item => item.searchInfo) || [];

						if (isCompute == "stop") {
							chatStatus.uuid = ''
							chatStatus.chatIndex = -1
						}
						else if (data.message && !chatStatus.stepText.includes(data.message)) {
							chatStatus.stepText.push(data.message)
						}

						searchInfo = chunks.find(item => item.searchInfo)?.searchInfo || [];

						if (data.message) messageList.push(data.message)

						updateChat(+uuid, dataSources.value.length - 1, {
							dateTime: new Date().toLocaleString(),
							text: text,
							showThink: isDeepseek.value ? true : false,
							thinkType,
							thinkInfo,
							messageList,
							inversion: false,
							error: false,
							loading: true,
							conversationOptions: {
								conversationId: data.conversationId,
								parentMessageId: data.id,
							},
							searchInfo: searchInfo,
							requestOptions: { prompt: message, options: { ...options } },
							isLimit: isLimit,
							isCompute: isCompute == "stop" ? true : false,
						});

						if (
							openLongReply &&
							data.detail?.choices[0].finish_reason === "length"
						) {
							options.parentMessageId = data.id;
							message = "";
							return fetchChatAPIOnce();
						}

						scrollToBottomIfAtBottom();
					} catch (err) {
						console.log("err:", err)
					}
				},
			})
				.then((data) => {
					const { id } = questionInfo
					handleQuestion(+uuid, id as number)
					updateHistoryIcon(+uuid, id as number)
				})
				.catch(err => {
					console.log('error: ', err);
					if (err?.errcode === SelfRequestCode.NeedPay) {
						// window.$aiwork.openRecharge?.({ type: "ai" });
						// chatStore.deleteChatByUuid(+uuid, dataSources.value.length - 1);
						updateChat(+uuid, dataSources.value.length - 1, {
							dateTime: new Date().toLocaleString(),
							text: err?.message || "次数已达上限",
							tips: 1,
							step: 100,
							inversion: false,
							error: true,
							loading: false,
						})
						// window?.$aiwork?.openRecharge?.({ type: "ai" });
						return
					}
					// 取消生成
					if (err.code === "ERR_CANCELED") {
						updateChat(+uuid, dataSources.value.length - 1, {
							dateTime: new Date().toLocaleString(),
							text: dataSources.value.at(-1)?.text || '取消生成',
							step: 99,
							inversion: false,
							error: false,
							loading: false,
						})
						return
					}
				});
			updateChatSome(+uuid, dataSources.value.length - 1, { loading: false });
		};
		const isCaptcha: any = await riskControl('');
		if (isCaptcha?.captcha) {
			captchaInit(authStore.captchaAppId, async function (captchaData: any) {
				if (captchaData && captchaData.randstr) {
					try {
						await fetchChatAPIOnce(captchaData);
					} catch (error) {
						errorHandler(error, message, options);
					}
				} else {
					errorHandler(captchaData, message, options);
				}
			});
		} else {
			try {
				await fetchChatAPIOnce();
			} catch (error) {
				errorHandler(error, message, options);
			}
		}
	} catch (error: any) {
		errorHandler(error, message, options);
	} finally {
		loading.value = false;
	}
}

function errorHandler(error: any, message: string, options: any) {
	const errorMessage = error?.message || t("common.wrong");
	const isLimit = error.data?.isLimit ? error.data?.isLimit : false;
	if (error.message === "canceled") {
		updateChatSome(+uuid, dataSources.value.length - 1, {
			loading: false,
			isLimit,
		});
		scrollToBottomIfAtBottom();
		return;
	}

	const currentChat = getChatByUuidAndIndex(
		+uuid,
		dataSources.value.length - 1
	);

	if (currentChat?.text && currentChat.text !== "") {
		updateChatSome(+uuid, dataSources.value.length - 1, {
			text: `${currentChat.text}\n[${errorMessage}]`,
			error: false,
			loading: false,
			isLimit,
		});
		return;
	}

	updateChat(+uuid, dataSources.value.length - 1, {
		dateTime: new Date().toLocaleString(),
		text: errorMessage,
		step: 100,
		inversion: false,
		error: true,
		loading: false,
		conversationOptions: null,
		requestOptions: { prompt: message, options: { ...options } },
		isLimit,
	});
	scrollToBottomIfAtBottom();
}

const updateHistoryIcon = (uuid: number, id: number) => {
	const history = chatStore.getHistory(uuid);
	if (!history || history.icon) return
	// const chat = chatStore.getChatByUuidAndIndex(uuid, index);
	// if(!chat) return
	fetchChatIcon<any>({
		id
	}).then(data => {
		chatStore.updateHistory(uuid, { icon: data.title, title: data.title })
	})
}

// 推荐问题
const handleQuestion = (uuid, id: number) => {
	const chat = chatStore.getChatByUuidAndIndex(+uuid, dataSources.value.length - 1);
	if (!chat || chat.text.length < 50) return
	updateChatSome(+uuid, dataSources.value.length - 1, {
		questionLoading: true,
	})
	fetchQuestions<string[]>({ id }).then(data => {
		// questions.value = data
		updateChatSome(+uuid, dataSources.value.length - 1, {
			questionList: data,
			questionLoading: false,
		})
	})
}

let timerMock: NodeJS.Timeout;
let chatTxt = "";
function writing(msg: any, index: number) {
	const data = msg;

	if (index < data.length) {
		chatTxt += data[index];
		updateChat(+uuid, dataSources.value.length - 1, {
			dateTime: new Date().toLocaleString(),
			text: chatTxt,
			inversion: false,
			error: false,
			loading: true,
			requestOptions: { prompt: chatTxt, options: {} },
		});
		timerMock = setTimeout(writing, 10, msg, ++index);
	}
	if (index === data.length) {
		updateChatSome(+uuid, dataSources.value.length - 1, { loading: false });
		// updateHistoryIcon(+uuid, data.join(""))
		loading.value = false;
	}
}

//文字消息重试
async function onRegenerate(index: number) {
	if (loading.value) return;

	controller = new AbortController();

	const { requestOptions } = dataSources.value[index];

	let message = requestOptions?.prompt || "";

	return handleSubmit(false, message)

	// 重试用不着下面的方法
	{
		let options: Chat.ConversationRequest = {};

		if (requestOptions?.options) options = { ...requestOptions?.options };

		loading.value = true;
		let step = 0;
		updateChat(+uuid, index, {
			dateTime: new Date().toLocaleString(),
			text: "",
			step,
			inversion: false,
			error: false,
			loading: true,
			conversationOptions: null,
			requestOptions: { prompt: message, ...options },
		});
		let timer = setInterval(() => {
			if (step >= 4) return timer && clearInterval(timer);
			step = step + 1;
			updateChat(+uuid, dataSources.value.length - 1, {
				dateTime: new Date().toLocaleString(),
				text: "",
				inversion: false,
				error: false,
				loading: true,
				step,
			});
		}, 300);

		try {
			let lastText = "";
			const fetchChatAPIOnce = async (captchaData = {}) => {
				loading.value = true;
				await fetchChatAPIProcess<Chat.ConversationResponse>({
					prompt: message,
					...captchaData,
					options,
					chatbotId: suggestion?.value?.id,
					signal: controller.signal,
					data: {
						files: fileList.value.map((item) => item.etag),
						isNetwork: isNetwork.value,
					},
					onDownloadProgress: ({ event }) => {
						timer && clearInterval(timer);
						const xhr = event.target;
						const { responseText } = xhr;
						let isLimit = false;
						try {
							if (responseText) {
								isLimit = JSON.parse(responseText)?.data?.isLimit;
								if (isLimit) {
									showMember.value = isLimit;
								}
							}
						} catch (error) { }

						try {
							let chunks = responseText
								.split("\n")
								.map((data) => JSON.parse(data));
							let text = chunks.map((data) => data.text).join("");
							const data = chunks.at(-1);

							const isCompute = data.detail.choices[0].finish_reason;
							updateChat(+uuid, dataSources.value.length - 1, {
								dateTime: new Date().toLocaleString(),
								text: text,
								step: isCompute == "stop" ? 99 : 5,
								inversion: false,
								error: false,
								loading: true,
								conversationOptions: {
									conversationId: data.conversationId,
									parentMessageId: data.id,
								},
								searchInfo: data.searchInfo,
								requestOptions: { prompt: message, options: { ...options } },
								isLimit: isLimit,
								isCompute: isCompute == "stop" ? true : false,
							});

							if (
								openLongReply &&
								data.detail.choices[0].finish_reason === "length"
							) {
								options.parentMessageId = data.id;
								message = "";
								return fetchChatAPIOnce();
							}

							scrollToBottomIfAtBottom();
						} catch { }
					},
				})
					.catch(err => {
						if (err?.errcode === SelfRequestCode.NeedPay) {
							window.$aiwork.openRecharge?.({ type: "ai" });
							updateChat(+uuid, index, {
								dateTime: new Date().toLocaleString(),
								text: err?.message || "次数已达上限",
								step: 100,
								inversion: false,
								error: true,
								loading: false,
							})
						}
					});
				updateChatSome(+uuid, index, { loading: false });
			};
			const isCaptcha: any = await riskControl();
			if (isCaptcha.captcha) {
				captchaInit(authStore.captchaAppId, async function (captchaData: any) {
					if (captchaData && captchaData.randstr) {
						try {
							await fetchChatAPIOnce(captchaData);
						} catch (error) {
							failHandler(error, index, message, options);
						}
					} else {
						failHandler(captchaData, index, message, options);
					}
				});
			} else {
				try {
					await fetchChatAPIOnce();
				} catch (error) {
					failHandler(error, index, message, options);
				}
			}
		} catch (error: any) {
			failHandler(error, index, message, options);
		} finally {
			loading.value = false;
		}
	}
}

function failHandler(error: any, index: number, message: string, options: any) {
	if (error.message === "canceled") {
		updateChatSome(+uuid, index, {
			loading: false,
			step: 100,
			error: true,
		});
		return;
	}

	const errorMessage = error?.message || t("common.wrong");

	updateChat(+uuid, index, {
		dateTime: new Date().toLocaleString(),
		text: errorMessage,
		step: 100,
		inversion: false,
		error: true,
		loading: false,
		conversationOptions: null,
		requestOptions: { prompt: message, ...options },
	});
}

function handleExport() {
	if (loading.value) return;

	const d = dialog.warning({
		title: t("chat.exportImage"),
		content: t("chat.exportImageConfirm"),
		positiveText: t("common.yes"),
		negativeText: t("common.no"),
		onPositiveClick: async () => {
			try {
				d.loading = true;
				const ele = document.getElementById("image-wrapper");
				const canvas = await html2canvas(ele as HTMLDivElement, {
					useCORS: true,
				});
				const imgUrl = canvas.toDataURL("image/png");
				const tempLink = document.createElement("a");
				tempLink.style.display = "none";
				tempLink.href = imgUrl;
				tempLink.setAttribute("download", "chat-shot.png");
				if (typeof tempLink.download === "undefined")
					tempLink.setAttribute("target", "_blank");

				document.body.appendChild(tempLink);
				tempLink.click();
				document.body.removeChild(tempLink);
				window.URL.revokeObjectURL(imgUrl);
				d.loading = false;
				ms.success(t("chat.exportSuccess"));
				Promise.resolve();
			} catch (error) {
				ms.error(t("chat.exportFailed"));
			} finally {
				d.loading = false;
			}
		},
	});
}
// function selectAccordin(item: any) {
//   for (let index = 0; index < promptGroupList.value.length; index++) {
//     const element = promptGroupList.value[index]
//     if (element.id == item.id) {
//       element.selected = !item.selected
//     } else {
//       element.selected = false
//     }
//   }
// }
function handleDelete(index: number) {
	if (loading.value) return;

	dialog.info({
		title: t("chat.deleteMessage"),
		content: t("chat.deleteMessageConfirm"),
		positiveText: t("common.yes"),
		negativeText: t("common.no"),
		onPositiveClick: () => {
			chatStore.deleteChatByUuid(+uuid, index);
		},
	});
}

const handleFileRemove = (index: number) => {
	fileList.value = []
};

function handleUpdateVip() {
	showMember.value = true;
}

function handleClear() {
	if (loading.value) return;

	dialog.warning({
		title: t("chat.clearChat"),
		content: t("chat.clearChatConfirm"),
		positiveText: t("common.yes"),
		negativeText: t("common.no"),
		onPositiveClick: () => {
			chatStore.clearChatByUuid(+uuid);
		},
	});
}

function handleEnter(event: KeyboardEvent) {
	if (!isMobile.value) {
		if (event.key === "Enter" && !event.shiftKey) {
			event.preventDefault();
			handleSubmit();
		}
	} else {
		if (event.key === "Enter" && event.ctrlKey) {
			event.preventDefault();
			handleSubmit();
		}
	}
}

function handleStop() {
	if (loading.value) {
		controller.abort();
		loading.value = false;
		clearTimeout(timerMock);
	}
}

// 可优化部分
// 搜索选项计算,这里使用value作为索引项,所以当出现重复value时渲染异常(多项同时出现选中效果)
// 理想状态下其实应该是key作为索引项,但官方的renderOption会出现问题,所以就需要value反renderLabel实现
const searchOptions = computed(() => {
	if (prompt.value.startsWith("/")) {
		return promptTemplate.value
			.filter((item: { key: string }) =>
				item.key.toLowerCase().includes(prompt.value.substring(1).toLowerCase())
			)
			.map((obj: { value: any }) => {
				return {
					label: obj.value,
					value: obj.value,
				};
			});
	} else {
		return [];
	}
});

// value反渲染key
const renderOption = (option: { label: string }) => {
	for (const i of promptTemplate.value) {
		if (i.value === option.label) return [i.key];
	}
	return [];
};

const placeholder = computed(() => {
	const mj = chatStore.getHistory(+uuid);
	if (isMobile.value) return t("chat.placeholderMobile");
	return mj?.type ? '请输入你想要问的问题' : '请输入你想要问的问题';
});

const buttonDisabled = computed(() => {
	return loading.value || !prompt.value || prompt.value.trim() === "";
});
const functionButtonDisabled = computed(() => {
	return loading.value
});

const hasFile = computed(() => {
	return fileList.value.length > 0
})
const isIphonex = () =>
	/iphone/gi.test(navigator.userAgent) &&
	window.screen &&
	window.screen.height === 812 &&
	window.screen.width === 375;
const footerClass = computed(() => {
	let classes = ["p-4 pb-2 bg-[#fff]"];
	if (isMobile.value)
		classes = [
			"fixed",
			"left-0",
			"right-0",
			"p-1",
			"pr-3",
			"overflow-hidden",
			isIphonex() ? "bottom-[84px]" : "bottom-[56px]",
		];
	return classes;
});

function setMembers() {
	showMember.value = true;
}

const searchParams = new URLSearchParams(window.location.search);
const params: any = {};
for (const [key, value] of searchParams) {
	params[key] = value;
}
const botid = params?.botid;
const deepseekChat = params?.deepseek

const { data: shortCutsList } = useRequest<ShortCuts[]>(() => chatbotShortcuts({
	deepseek: deepseekChat ? true : undefined
}))

const { run: loadSuggestion } = useRequest(chatbotsList, {
	manual: true,
	onSuccess: (response: any) => {
		suggestionList.value = response;
		if (response && response.length > 0) {
			if (!!botid) {
				for (let item of response) {
					for (let mp of item.Chatbots) {
						if (botid == mp.id) {
							const data = {
								id: mp.id,
								name: mp.name,
								categoryId: item.id,
								categoryName: item.name,
								uuid: Number(+uuid),
							};
							suggestionStore.updateSetting(data, +uuid);
							console.log('suggestionStore.updateSetting', data);
							suggestion.value = data;
							currentMessageId.value = data.id;
						}
					}
				}
			}
		}
	}
})

onMounted(async () => {
	if (!dataSources.value.length) {
	} else {
		scrollToBottom();
	}
	if (inputRef.value && !isMobile.value) inputRef.value?.focus();

	loadSuggestion("");
});

onUnmounted(() => {
	if (loading.value) controller.abort();
});

const handleSuggest = (item: any) => {
	suggestion.value = item;
	currentMessageId.value = item.id;
};

const handleClose = () => {
	suggestionStore.removeSetting(suggestion.value?.id, +uuid);
	suggestion.value = null;
};
const handleSuggestClose = () => {
	showApp.value = false;
	loadSuggestion("");
};

const handleSearch = (keyword: string) => {
	loadSuggestion(keyword);
};
const getMobileClass = computed<CSSProperties>(() => {
	if (isMobile.value) {
		return {
			// height: "calc(100% - 135px)",
			height: "100%"
		};
	}
	return {
		height: "100%",
	};
});
const uploadFile = async () => {
	if (!fileList.value.length) return;
	fileUploading.value = true;
	fileList.value[0].uploading = true;
	latestFileInConversation.value = false;
	if (fileList.value[0].file?.size! >= 1024 * 1024 * 10) {
		message.error("文件大小不能超过10MB");
		fileList.value[0].uploading = false;
		handleFileRemove(0);
		return
	}
	const { ext: type } = await getFileType(fileList.value[0].file)
	fileList.value[0].fileType = type
	fetchUploadFile<any>({
		file: fileList.value[0].file,
		onUploadProgress: ({ progress }) => {
			fileList.value[0].progress = Math.round(progress * 100).toFixed(0);
			fileList.value[0].fileType = type;
		},
	}).then((data) => {
		// clearTimeout(progressRef.value);
		fileList.value = fileList.value.map((item) => ({
			...item,
			etag: data.etag,
			url: data.url,
			uploading: false,
			progress: 100,
		}));
		fileUploading.value = false
	}).catch(e => {
		message.error(e.errmsg || '上传失败');
		fileUploading.value = false
	});
};
const formatSize = (size: number) => {
	if (size > 1024 * 1024) return (size / 1024 / 1024).toFixed(2) + "MB";
	if (size > 1024) return (size / 1024).toFixed(2) + "KB";
	return size + "B";
};

watch(() => fileList.value, (v, prev) => {
	if (v.length && !prev.length) {
		isNetwork.value = false;
		disableNetwork.value = "上传文件时不支持联网搜索";
	}
	if (!v.length && prev.length) {
		disableNetwork.value = ""
	}
}, { deep: true })



const handleFileSuggestionSelect = (data: { suggestion: string }) => {
	prompt.value = data.suggestion
	handleSubmit()
}

const handleShortCutSelect = (shortCut: ShortCuts) => {
	if (shortCut.params.file) {
		fileList.value = [{ ...shortCut.params.file, etag: shortCut.params.file.id }]
	}
	if (shortCut.params.query) {
		prompt.value = shortCut.params.query
	}
	if (shortCut.params.chatBot) {
		let tempUuid = +uuid
		suggestion.value = {
			id: shortCut.params.chatBot,
			name: shortCut.params.chatBotName,
			uuid: tempUuid
		}
		currentMessageId.value = shortCut.params.chatBot;
		updateHistoryIcon(tempUuid, shortCut.params.chatBot)
	}
	if (shortCut.params.jumpUrl) {
		const protocol = window.location.protocol
		const host = window.location.host.includes('chat-qa.mjmobi.com') ? 'chat-qa.mjmobi.com' : 'aiwork365.cn'
		window.open(`${protocol}//${host}${shortCut.params.jumpUrl}`)
		// router.push(shortCut.params.jumpUrl)
	}
	if (shortCut.action === 'searchWeb') {
		isNetwork.value = true
	}
	if (shortCut.action.startsWith('ds')) {
		isDeepseek.value = true
	}

	handleSubmit()
}

const handleDeepseek = () => {
	isDeepseek.value = !isDeepseek.value
}
const handleNetwork = () => {
	isNetwork.value = !isNetwork.value;
}

const handleSelectKnowledge = (id: any) => {
	if(!!id) {
		disableNetwork.value = "暂不支持联网搜索";
	} else {
		disableNetwork.value = "";
	}
	activeKnowledgeId.value = id;
}
</script>

<template>
	<div class="flex flex-col h-full pt-[64px] sm:pt-0 sm:w-[100vw] sm:px-4 ipad:pt-0 ipad:w-[100vw] ipad:px-4">
		<HeaderComponent class="sm:-mx-4" v-if="!isPC" :using-context="usingContext" @handleMembers="setMembers"
			@export="handleExport" @toggle-using-context="toggleUsingContext" />
		<main class="flex-1 min-h-0">
			<div id="scrollRef" ref="scrollRef"
				class="overflow-y-auto overflow-x-visible relative hide-scrollbar w-[1000px] px-[50px] mx-auto sm:w-full sm:px-0 ipad:w-full ipad:px-0"
				:style="getMobileClass">
				<div id="image-wrapper" class="w-full m-auto dark:bg-[#101014]" :class="[isMobile ? 'py-2' : 'py-4']">
					<template v-if="!dataSources.length">
						<!-- <Default v-if="isMobile" /> -->
						<!-- 智能体欢迎语 -->
						<AgentWelcome v-if="currentHistory?.agentId" :agentId="currentHistory?.agentId"
							:teamId="currentHistory?.teamId" />
						<div v-if="!currentHistory?.agentId">
							<div v-if="!deepseekChat"
								class="text-[24px] sm:text-[18px] text-left mt-[80px] sm:mt-2 font-bold leading-[35px]">
								欢迎您，和<span class=" text-[#0E69FF] italic">AI Work365</span>一起探索世界吧！
							</div>
							<NImage v-else :src="DeepseekChatImg" class="h-[50px] w-auto mt-[80px]" object-fit="contain"
								preview-disabled />
							<div class=" text-[20px] text-[#333333] font-bold leading-[26px] mt-[14px]" v-if="!deepseekChat">
								我可以帮助您做这些事
							</div>
							<div v-else class="text-[16px] text-[#333333] leading-[21px] mt-[32px]">
								您身边的智能助手，我可以帮您阅读、答疑、分析、写作、做PPT，请把您的任务交给我吧~</div>

							<div class=" flex flex-row flex-wrap gap-[15px] mt-[32px]">
								<ShortCut v-for="(item, index) in shortCutsList" :short-cut="item" :key="index"
									@select="handleShortCutSelect" />
							</div>
						</div>
					</template>
					<template v-else>
						<div class="mt-[60px]">
							<!-- showThink: isDeepseek.value,
							thinkType,
							thinkInfo, -->
							<Message v-for="(item, index) of dataSources" :key="index" :date-time="item.dateTime"
								:file-obj="item.fileObj" :tips="item.tips" :text="item.text" :index="index" :inversion="item.inversion"
								:error="item.error" :loading="item.loading" :is-limit="item.isLimit" :is-compute="item.isCompute"
								:searchInfo="item.searchInfo" :show-think="item.showThink" :think-type="item.thinkType"
								:think-info="item.thinkInfo" :messageList="item.messageList"
								:show-question="index === dataSources.length - 1" :questionLoading="item.questionLoading"
								:questionList="item.questionList" @regenerate="onRegenerate(index)" @delete="handleDelete(index)"
								@update-vip="handleUpdateVip()" />
							<div class="sticky bottom-0 left-0 flex justify-center"
								style="background: linear-gradient(to top, rgba(249, 250, 255, 1) 0%, rgba(249, 250, 255, .7) 60%, rgba(249, 250, 255, 0) 100%);">
								<NButton v-if="loading" ghost type="primary" @click="handleStop"
									style="box-shadow: 0px 4px 10px 0px rgba(70, 114, 181, 0.2)">
									<template #icon>
										<SvgIcon icon="ri:stop-circle-line" />
									</template>
									停止生成会话
								</NButton>
							</div>
						</div>
					</template>
				</div>
			</div>
		</main>
		<!-- <div :class="footerClass"> -->
		<div class="w-[1000px] px-[50px] mx-auto sm:w-full sm:px-0 sm:pb-[50px]  ipad:w-full ipad:px-0 ipad:pb-[105px]">
			<div class="w-full m-auto mt-[20px]">
				<div class="flex items-center gap-[10px]">
					<NText type="primary" class="flex items-center gap-[10px] cursor-pointer" @click="showApp = true">
						<NImage :src="AiAgentImg" class="w-[14px] h-[14px]" preview-disabled />
						<!-- <ApplicationSvg class="w-[14px] h-[14px]" /> -->
						<span>AI助手</span>
					</NText>
					<NTag type="primary" size="small" closable @close="handleClose" v-if="suggestion?.id">{{
						suggestion?.name }}
					</NTag>
				</div>
				<!-- <div class="ml-[145px] flex items-center sm:ml-[47px]" v-if="!mjObj?.type">
          <div class="flex items-center cursor-pointer" @click="showApp = true">
            <NImage :src="apps" preview-disabled width="13" height="13" />
            <span class="pl-[5px] mr-[10px]">应用场景</span>
          </div>
          <NTag type="success" closable round @close="handleClose" v-if="suggestion?.id">{{ suggestion?.name }}</NTag>
        </div> -->
				<div
					class="flex flex-col w-full mt-[20px] mb-[20px] bg-white rounded-[10px] border-[1px] border-[#DEE2EE] p-[20px] sm:mt-2 ipad:mt-2"
					style="box-shadow: 0px 2.3px 11.51px 0px #dee2ee">
					<div v-if="fileList.length">
						<FileCard v-for="(item, index) in fileList" :key="index" :index="index" :url="item.url" :type="item.type"
							:file-type="item.fileType" :file="item.file" :progress="item.progress" :name="item.name"
							:chatting="loading" @close="handleFileRemove" />
					</div>
					<div v-if="fileList.length" class="my-[12px] mx-[0px] flex flex-row gap-x-[10px]">
						<FileSuggestion v-for="(item, index) in fileSuggestionPromptList" :suggestion="item.suggestion"
							:index="index" :disabled="fileUploading" @select="handleFileSuggestionSelect" />
					</div>
					<!-- <div v-if="fileList.length"
						class="border-b-[1px] border-[#DEE2EE] pb-[10px] flex justify-start gap-[12px]">
						<NAlert v-for="item in fileList" :showIcon="false"
							class="max-w-[240px] min-w-[160px] h-[70px] relative" closable style="--n-border: none"
							@close="() => {
								fileList = fileList.filter(x => x != item);
							}
								">
							<div v-if="/^image/.test(item.type)" class="absolute top-[1px] left-[1px]"
								style="width: calc(100% - 2px); height: calc(100% - 2px);">
								<NImage :src="item.url" class="w-full h-full object-cover" />
							</div>
							<div v-else>
								<NEllipsis :tooltip="false">{{ item.name }}</NEllipsis>
								<div class="text-[10px] text-[#636262] mt-[4px]">
									{{ formatSize(item.file.size) }}
								</div>
							</div>
							<div v-if="item.uploading"
								class="absolute top-0 right-0 bottom-0 left-0 bg-[#000] opacity-50 flex justify-center items-center rounded">
								<NProgress type="circle" style="width: 30px" :percentage="item.progress"
									:show-indicator="false" />
							</div>
						</NAlert>
					</div> -->
					<NAutoComplete class="flex-shrink-0" v-model:value="prompt" :options="searchOptions"
						:render-label="renderOption">
						<template #default="{ handleInput, handleBlur, handleFocus }">
							<NInput ref="inputRef" class="h-[60px] mb-[8px]" style="
									--n-border: none;
									--n-border-hover: none;
									--n-border-focus: none;
									--n-border-disabled: none;
									--n-box-shadow-focus: none;
								" v-model:value="prompt" type="textarea" :placeholder="placeholder" :rows="8" size="large"
								:autosize="{ minRows: 1, maxRows: isMobile ? 4 : 8 }" @input="handleInput" @focus="handleFocus"
								@blur="handleBlur" @keypress="handleEnter" />
						</template>
					</NAutoComplete>
					<div class="flex justify-between items-center w-full">
						<div v-if="currentHistory?.agentId"></div>
						<div v-else class="flex items-center gap-[10px]">
							<div>
								<NTooltip>
									<template #trigger>
										<NButton round :style="{
											'--n-color': isDeepseek ? '#DBEAFE' : '#fff',
											'--n-color-focus': isDeepseek ? '#DBEAFE' : '#fff',
											'--n-text-color': isDeepseek ? '#4D6BFE' : '#4c4c4c',
											'--n-border': isDeepseek ? '1px solid rgba(0, 122, 255, 0.15)' : '1px solid rgba(0, 0, 0, 0.12)',
											'--n-color-hover': isDeepseek ? '#C3DAF8' : '#E0E4ED',
											'--n-color-pressed': '#DBEAFE',
											'--n-text-color-hover': isDeepseek ? '#4D6BFE' : '#4c4c4c',
											'--n-text-color-pressed': '#4D6BFE',
											'--n-border-hover': isDeepseek ? '1px solid rgba(0, 122, 255, 0.15)' : '1px solid rgba(0, 122, 255, 0.15)',
											'--n-border-pressed': '1px solid rgba(0, 122, 255, 0.15)',
											'--n-border-focus': '1px solid rgba(0, 122, 255, 0.15)',
											'--n-text-color-focus': isDeepseek ? '#4D6BFE' : '#4c4c4c',
											'--n-width': isMobile ? '70px' : 'auto',
											'--n-padding': isMobile ? '0 6px' : '0 14px'
										}" :disabled="functionButtonDisabled" @click="handleDeepseek" :size="isMobile ? 'small' : 'medium'">
											<template #icon v-if="!isMobile">
												<IconThink />
											</template>
											<span class="text-[14px]" :class="{ 'text-[10px]': isMobile }">深度思考</span>
										</NButton>
									</template>
									<span>调用DeepSeek,解决推理问题</span>
								</NTooltip>
							</div>
							<div>
								<NTooltip>
									<template #trigger>
										<NButton round :style="{
											'--n-color': isNetwork ? '#DBEAFE' : '#fff',
											'--n-color-focus': isNetwork ? '#DBEAFE' : '#fff',
											'--n-text-color': isNetwork ? '#4D6BFE' : '#4c4c4c',
											'--n-border': isNetwork ? '1px solid rgba(0, 122, 255, 0.15)' : '1px solid rgba(0, 0, 0, 0.12)',
											'--n-color-hover': isNetwork ? '#C3DAF8' : '#E0E4ED',
											'--n-color-pressed': '#DBEAFE',
											'--n-text-color-hover': isNetwork ? '#4D6BFE' : '#4c4c4c',
											'--n-text-color-pressed': '#4D6BFE',
											'--n-border-hover': isNetwork ? '1px solid rgba(0, 122, 255, 0.15)' : '1px solid rgba(0, 122, 255, 0.15)',
											'--n-border-pressed': '1px solid rgba(0, 122, 255, 0.15)',
											'--n-border-focus': '1px solid rgba(0, 122, 255, 0.15)',
											'--n-text-color-focus': isNetwork ? '#4D6BFE' : '#4c4c4c',
											'--n-width': isMobile ? '70px' : 'auto',
											'--n-padding': isMobile ? '0 6px' : '0 14px'
										}" :disabled="functionButtonDisabled || hasFile || !!activeKnowledgeId" @click="handleNetwork"
											:size="isMobile ? 'small' : 'medium'">
											<template #icon v-if="!isMobile">
												<IconEarth />
											</template>
											<span :class="{ 'text-gray-400': disableNetwork, 'text-[10px]': isMobile }"
												class="text-[14px]">联网搜索</span>
										</NButton>
									</template>
									<span v-if="disableNetwork">{{ disableNetwork }}</span>
									<span v-else>开启联网模式后,将实时获取联网数据,提高数据的准确信和真实性.联网模式不支持连续对话</span>
								</NTooltip>
							</div>
							<KnowledgeButton @on-select-knowledge="handleSelectKnowledge" :disabled="functionButtonDisabled || hasFile || isNetwork" />
						</div>
						<div class="flex items-center gap-[20px]">
							<!-- <NTag v-for="file in fileList" :key="file.etag" size="small" closable type="info" @close="() => { fileList.splice(fileList.indexOf(file), 1) }">{{ file.name }}</NTag> -->
							<!-- 最大不能超过10MB -->
							<NUpload v-if="!currentHistory?.agentId" accept=".md,.pdf,.docx,.pptx,.excel" :show-file-list="false"
								:custom-request="uploadFile" @update:file-list="(data: any) => {
									fileList = [data.pop()]
									uploadFile()
									isNetwork = false;
								}">
								<NTooltip>
									<template #trigger>
										<NButton :disabled="!!activeKnowledgeId || isNetwork" text class="flex items-center text-[16px]" style="
												--n-text-color-hover: #3680f9;
												--n-text-color-pressed: #3680f9;
												--n-text-color-focus: #3680f9;
											">
											<template #icon>
												<DirSvg class="w-[14px] h-[14px]" />
											</template>
											<span>上传文件</span>
										</NButton>
									</template>
									<div v-if="!!activeKnowledgeId || isNetwork">暂不支持文件上传功能</div>
									<div v-else>
										<div>支持上传10M以内的文件</div>
										<div>.docx, .md, .pdf, .excel, .pptx</div>
									</div>
								</NTooltip>
							</NUpload>
							<NButton style="--n-border-radius: 6px" type="primary" :disabled="buttonDisabled"
								v-model:file-list="fileList" @click="handleSubmit(false)">
								<template #icon>
									<span class="dark:text-black">
										<SvgIcon icon="ri:send-plane-fill" />
									</span>
								</template>
								{{getCredit('chat')}}
							</NButton>
						</div>
					</div>
					<!-- <HoverButton @click="handleClear">
						<span
							class="text-xl text-[#4f555e] hover:text-[#3ebba2] dark:text-white"
						>
							<Delete16Regular class="w-[22px] h-[22px]" />
						</span>
					</HoverButton>
					<HoverButton v-if="!isMobile" @click="handleExport">
						<span
							class="text-xl text-[#4f555e] hover:text-[#3ebba2] dark:text-white"
						>
							<SvgIcon icon="ri:download-2-line" />
						</span>
					</HoverButton>
					<HoverButton v-if="!isMobile" @click="toggleUsingContext">
						<span
							class="text-xl hover:text-[#3ebba2]"
							:class="{
								'text-[#4b9e5f]': usingContext,
								'text-[#a8071a]': !usingContext,
							}"
						>
							<SvgIcon icon="ri:chat-history-line" />
						</span>
					</HoverButton>
					<NAutoComplete v-model:value="prompt" :options="searchOptions" :render-label="renderOption">
            <template #default="{ handleInput, handleBlur, handleFocus }">
              <NInput ref="inputRef" v-model:value="prompt" type="textarea" :placeholder="placeholder"
                :disabled="loading" :autosize="{ minRows: 1, maxRows: isMobile ? 4 : 8 }" @input="handleInput"
                @focus="handleFocus" @blur="handleBlur" @keypress="handleEnter" />
            </template>
          </NAutoComplete>
					<NButton
						type="primary"
						color="#3dbaa1"
						:disabled="buttonDisabled"
						@click="handleSubmit(false)"
					>
						<template #icon>
							<span class="dark:text-black">
								<SvgIcon icon="ri:send-plane-fill" />
							</span>
						</template>
					</NButton> -->
				</div>
			</div>
		</div>
		<GlobalFloat />
		<Member v-if="showMember" v-model:visible="showMember" />
		<Permission v-if="needPermission" v-model:visible="needPermission" />
		<Drawer v-if="showApp" v-model:visible="showApp" :data="suggestionList" @update-suggest="handleSuggest"
			@search="handleSearch" @close="handleSuggestClose" />
	</div>
</template>
<style lang="less">
@import url(./index.less);
</style>
