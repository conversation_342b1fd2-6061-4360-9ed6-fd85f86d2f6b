.markdown-body {
	background-color: transparent;
	font-size: 14px;

	p {
		white-space: pre-wrap;
	}

	ol {
		list-style-type: decimal;
	}

	ul {
		list-style-type: disc;
	}

	pre code,
	pre tt {
		line-height: 1.65;
		white-space: pre-wrap !important;
		word-wrap: break-word !important;
		overflow: visible !important;
		max-width: 100% !important;
	}

	.highlight pre,
	pre {
		background-color: #fff;
		overflow-x: visible !important;
		white-space: pre-wrap !important;
		word-wrap: break-word !important;
		max-width: 100% !important;
		text-align: left !important;
		padding: 12px !important;
		margin: 0 !important;
	}

	code.hljs {
		padding: 0 !important;
		white-space: pre-wrap !important;
		word-wrap: break-word !important;
		overflow: visible !important;
		text-align: left !important;
		display: block !important;
		
		&::before {
			content: '';
			white-space: nowrap;
		}
	}
	
	// 移除代码块前导空格
	pre code {
		text-indent: 0 !important;
		
		&::first-line {
			text-indent: 0 !important;
		}
	}

	.code-block {
		&-wrapper {
			position: relative;
			padding-top: 24px;
			overflow: visible !important;
			text-align: left !important;
			
			pre {
				white-space: pre-wrap !important;
				word-wrap: break-word !important;
				overflow: visible !important;
				text-align: left !important;
				padding: 12px !important;
				margin: 0 !important;
				
				code {
					white-space: pre-wrap !important;
					word-wrap: break-word !important;
					overflow: visible !important;
					display: block !important;
					text-align: left !important;
					padding: 0 !important;
				}
			}
		}

		&-header {
			position: absolute;
			top: 5px;
			right: 0;
			width: 100%;
			padding: 0 1rem;
			display: flex;
			justify-content: flex-end;
			align-items: center;
			color: #b3b3b3;

			&__copy {
				cursor: pointer;
				margin-left: 0.5rem;
				user-select: none;

				&:hover {
					color: #65a665;
				}
			}
		}
	}

}

html.dark {

	.message-reply {
		.whitespace-pre-wrap {
			white-space: pre-wrap;
			color: var(--n-text-color);
		}
	}

	.highlight pre,
	pre {
		background-color: #282c34;
		overflow-x: visible !important;
		white-space: pre-wrap !important;
		word-wrap: break-word !important;
		max-width: 100% !important;
		text-align: left !important;
		padding: 12px !important;
		margin: 0 !important;
	}
}
.tips-container {
	position: absolute;
	right: -194px;
	top: -5px;
	background-color: #E5EFFF;
	border-radius: 6px;
	color: #0E69FF;
	padding: 0 9px;
	display: flex;
	flex-direction: row;
	align-items: center;
	line-height: 36px;
	cursor: pointer;
}