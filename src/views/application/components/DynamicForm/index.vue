<script setup lang="ts">
import { ref, reactive } from 'vue'
import { NForm, NFormItem, NInput, NSelect, NDynamicTags, NButton, FormInst } from 'naive-ui'
import { getCredit } from '@/store/modules/auth/helper'

export type IFormProps = {
    label: string,
    options: string[],
    formType: string,
    isRequired: boolean,
    placeholder: string,
}

interface IProps {
    list: IFormProps[],
    loading: boolean
}

interface Emit {
    (e: 'create', array: any[]): void
}

const emit = defineEmits<Emit>()

defineProps<IProps>()
const formRef = ref<FormInst | null>(null)
const formData: any = reactive({})

const renderOptions = (opts: string[]) => {
    let options: any[] = [];
    opts.map(item => {
        options.push({
            label: item,
            value: item
        })
    })
    return options
}

const handleValidateClick = (e: MouseEvent) => {
    e.preventDefault()
    let formValue: Record<string, any>[] = []
    formRef.value?.validate((errors) => {
        if (!errors) {
            for (let item in formData) {
                formValue.push({
                    label: item,
                    value: formData[item]
                })
            }

            emit('create', formValue)
        }
    })
}

</script>
<template>
    <NForm :model="formData" ref="formRef">
        <template v-for="(field) in list">
            <template v-if="field.formType === 'input'">
                <NFormItem :label="field.label" :path="field.label" :rule="{
                    required: field.isRequired,
                    message: `请输入${field.label}`
                }">
                    <NInput v-model:value="formData[field.label]" :placeholder="field.placeholder" />
                </NFormItem>
            </template>
            <template v-if="field.formType === 'select'">
                <NFormItem :label="field.label" :path="field.label" :rule="{
                    required: field.isRequired,
                    message: `请选择${field.label}`
                }">
                    <NSelect v-model:value="formData[field.label]" :placeholder="field.placeholder"
                        :options="renderOptions(field.options)" />
                </NFormItem>
            </template>
            <template v-if="field.formType === 'multiSelect'">
                <NFormItem :label="field.label" :path="field.label" :rule="{
                    required: field.isRequired,
                    message: `请选择${field.label}`
                }">
                    <NSelect v-model:value="formData[field.label]" :placeholder="field.placeholder" :multiple="true"
                        :options="renderOptions(field.options)" />
                </NFormItem>
            </template>
            <template v-if="field.formType === 'textarea'">
                <NFormItem :label="field.label" :path="field.label" :rule="{
                    required: field.isRequired,
                    message: `请输入${field.label}`
                }">
                    <NInput type="textarea" v-model:value="formData[field.label]" :placeholder="field.placeholder" />
                </NFormItem>
            </template>
            <template v-if="field.formType === 'keywords'">
                <NFormItem :label="field.label" :path="field.label" :rule="{
                    required: field.isRequired,
                    message: `请输入${field.label}`
                }">
                    <NDynamicTags v-model:value="formData[field.label]" :placeholder="field.placeholder" />
                </NFormItem>
            </template>
        </template>
        <NFormItem>
            <div class="w-full mb-[40px] sm:mb-0" v-if="list?.length">
                <NButton type="success" color="#3dbaa1" round :loading="loading" style="width: 100%;"
                    @click="handleValidateClick">开始创作 {{getCredit('app')}}</NButton>
            </div>
        </NFormItem>
    </NForm>

</template>
