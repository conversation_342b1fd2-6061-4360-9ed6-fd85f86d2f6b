<script setup lang="ts">
import { <PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON>age, NIcon, <PERSON>ard, NElli<PERSON>, NImage, NProgress, NSpin } from "naive-ui"
import { DocumentCopy48Regular } from '@vicons/fluent'
import dayjs from 'dayjs'
import { computed, ref, watch } from 'vue';
import RichText from "@/components/richtext/RichText.vue"
import useClipboard from 'vue-clipboard3'
import OutlineImg from '@/assets/images/ai-outline.png'
import Dot from '@/assets/images/dot.png'
import FileUpload from '@/assets/fileUpload.png';
import { useRequest } from "vue-hooks-plus";
import PPTTemplate from './PPTTemplate.vue'

import { markdownToDocx } from "@/chatgpt";
import { useRoute, useRouter } from "vue-router";
import Poster from "./Poster.vue";
import DownPanel from "@/views/chat/components/downPanel.vue";
import { ThinkType } from "@/views/chat/types";

const message = useMessage();
const { toClipboard } = useClipboard()
interface Props {
    dataSources: any[];
    dataSource: string;
    extraDataSource: string;
    historyData: any[];
    isCollected: boolean;
    requestLoading: boolean;
    type: string;
    contentLoading: boolean;
    ispptGenerated: boolean;
    isDeepSeek: boolean;
    thinkType: ThinkType;
    thinkText: string;
    stop: () => void;
    collect: (isCollect: boolean) => void;
    expand?: (content: string, type: any) => void;
    rewrite?: (content: string, type: any) => void;
    abbreviate?: (content: string, type: any) => void;
    continue?: (content: string, type: any) => void;
}
interface Emit {
    (event: 'generate-ppt', id: string): void;
}

const emit = defineEmits<Emit>()

const props = withDefaults(defineProps<Props>(), {
    thinkType: ThinkType.None,
    thinkText: ''
})
const richtextRef = ref()
const $router = useRouter();
// 0 markdown 1 历史记录
const currentArea = ref(0)
const progress = ref(0)
const interval = ref<any>(null)
const pptTemplateVisible = ref(false)
const route = useRoute()
const router = useRouter()

const { run } = useRequest(markdownToDocx, {
    manual: true,
    onSuccess: (fileBlob, params) => {
        const blob = new Blob([fileBlob], { type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document" });
        const fileUrl = URL.createObjectURL(blob);
        const ele = document.createElement("a");
        ele.setAttribute("href", fileUrl);
        ele.setAttribute("download", params?.[0]?.name || '文件');
        ele.click();
        document.body.appendChild(ele);
    }
})



// 每秒增加进度条的值，直到达到100
const increaseProgress = () => {
    interval.value = setInterval(() => {
        if (progress.value < 100) {
            progress.value += 1
        } else {
            if (!props.contentLoading) {
                clearInterval(interval.value)
            }
        }
    }, 3000)
}
watch(() => props.contentLoading, (newVal) => {
    if (newVal) {
        progress.value = 0;
        increaseProgress()
    } else {
        progress.value = 100
        clearInterval(interval.value)
    }
})

const handleDownload = (item: any) => {
    run({
        chatId: item.id,
        name: item?.Createbots?.name
    })
}

const handleCopy = (text: string) => {
    toClipboard(text)
    message.success('复制成功')
}
const handleCollect = () => {
    props.collect(props.isCollected)
}
const handleCreateNewFile = () => {
    richtextRef?.value?.clearContent()
    currentArea.value = 0
}
const handleHistoryData = () => {
    currentArea.value = 1
}
const handleExportDocx = () => {
    if (richtextRef.value.hasContent()) {
        richtextRef.value.handlerExportDocx()
        message.success('导出成功')
    } else {
        message.error('请先输入内容')
    }
}

const handleExportPPT = () => {
    if (props.ispptGenerated) {
        $router.push('/ppt/workspace/home')
    } else {
        pptTemplateVisible.value = true
    }
}

const handleSelectPPTTemplate = (id: string) => {
    emit('generate-ppt', id)
    pptTemplateVisible.value = false
}

const handleGoToCreate = () => {
    currentArea.value = 0
}
const handleSwitchToPoster = () => {
    const markdownContent = richtextRef.value.getMarkdown()
    currentArea.value = 2
    return markdownContent
}
defineExpose({
    handleCreateNewFile
})

const handleUseEditorV2 = () => {
	localStorage.setItem('useEditorType', 'v2')
	router.replace({name: 'AppDetail', params: {id: route.params.id}, query: route.query})
}
</script>
<template>
    <div
        class="create-container h-full w-full sm:w-full sm:mt-4 sm:rounded-[10px] rounded-r-[10px] flex flex-col sm:h-auto  min-h-0">
        <div
            class=" w-full flex flex-row h-[55px] border-b-[1px] border-b-[#DCDCDC] pl-[44px] pr-[16px] bg-[#ffffff] rounded-tl-[4px] rounded-tr-[4px] z-[2] py-[13px] sm:px-4 sm:flex-col sm:gap-3 sm:h-auto">
            <div class=" flex flex-row justify-between flex-1 gap-10">
                <div class="flex items-center  cursor-pointer truncate" @click="handleCreateNewFile">
                    <NIcon class="w-[21px] h-[21px] text-[16px]">
                        <IconPlus />
                    </NIcon>
                    <span class=" pl-1 truncate">新建文件</span>
                </div>
                <div class="flex items-center  cursor-pointer truncate" @click="handleCollect">
                    <NIcon class="w-[21px] h-[21px] text-[16px]">
                        <IconStar v-if="!isCollected" />
                        <IconStarFill v-else />
                    </NIcon>
                    <span class=" pl-1 truncate">收藏</span>
                </div>

                <div class="flex items-center cursor-pointer truncate" @click="handleHistoryData">
                    <NIcon class="w-[21px] h-[21px] text-[16px]">
                        <IconHistory />
                    </NIcon>
                    <span class=" pl-1 truncate">历史记录</span>
                </div>
            </div>
            <!-- 导出word -->
            <div class=" flex-[40%] flex flex-row items-center justify-end sm:justify-start gap-x-2">
                <div class=" bg-[#0E69FF] h-[30px] px-4 text-[#ffffff] flex items-center justify-center rounded-[2px] cursor-pointer"
                    @click="handleSwitchToPoster">
                    <span class=" pl-1">海报</span>
                </div>
                <template v-if="type === 'long'">
                    <div class=" bg-[#ffffff] border border-[#0E69FF] h-[30px] w-[110px] text-[#0E69FF] flex items-center justify-center rounded-[2px] cursor-pointer"
                        @click="handleExportDocx">
                        <NIcon>
                            <IconFolderUpload />
                        </NIcon>
                        <span class=" pl-1">导出Word</span>
                    </div>
                    <div class=" bg-[#0E69FF] h-[30px] w-[110px] text-[#ffffff] flex items-center justify-center rounded-[2px] cursor-pointer"
                        @click="handleExportPPT">
                        <IconPPt />
                        <span class=" pl-1">{{ ispptGenerated ? '我的PPT' : '生成PPT' }}</span>
                    </div>
                </template>
                <template v-else>
                    <div class=" bg-[#0E69FF] h-[30px] w-[110px] text-[#ffffff] flex items-center justify-center rounded-[2px] cursor-pointer"
                        @click="handleExportDocx">
                        <IconFolderUpload />
                        <span class=" pl-1">导出Word</span>
                    </div>
                </template>
								<!-- <NButton @click="handleUseEditorV2" color="#ff69b4">体验新版</NButton> -->
								 <div class="flex px-3 gap-1 h-[30px] items-center justify-center text-white font-medium bg-gradient-to-r from-[#3456FF] to-[#AB8FFF] cursor-pointer text-sm rounded-sm" @click="handleUseEditorV2">体验新版</div>
            </div>

        </div>
        <!--  -->
        <!-- <div class="w-full h-[42px] bg-[#fff] border-b-[1px] border-b-[#DCDCDC]">

        </div> -->
        <DownPanel :show="false" class="!mb-[unset]" v-if="isDeepSeek">
            <template #trigger="slotProps">
                <div class="flex items-center gap-[4px] bg-[#fff] border-b-[1px] border-b-[#DCDCDC] h-[42px]">
                    <ArrowSvg class="w-[14px] h-[14px] transition-all duration-300"
                        :class="{ 'rotate-180': slotProps.show }" />
                    <div>
                        <IconThink class="w-[14px] h-[14px] relative top-[1px]" />
                        <span v-if="thinkType === ThinkType.Thinking">思考中...</span>
                        <span v-else-if="thinkType === ThinkType.Answer">已深度思考</span>
                    </div>
                </div>
            </template>
            <div class="px-[12px] border-l-4 border-[#e5e5e5] w-full markdown-body content-body !text-[#8b8b8b]"
                v-html="thinkText"></div>
        </DownPanel>
        <div v-show="currentArea === 0" class=" w-full h-full bg-white flex-1 min-h-0 flex flex-col">
            <div v-if="type === 'long' && contentLoading"
                class=" w-full h-full flex-1 flex flex-col items-center justify-center">
                <NImage :src="OutlineImg" class=" w-[107px] h-[80px] object-fill" />
                <span class=" text-[#3d3d3d] text-[14px] font-bold">AI正在生成文章...</span>
                <span class=" text-[#909090] text-[12px]">请耐心等待，不要离开当前页面</span>
                <n-progress type="line" color="#8488F2" :percentage="progress" class="!w-[160px]">
                    <span class=" text-[#A7ABC1] text-[12px] ">{{ progress }}%</span>
                </n-progress>
            </div>
            <div v-else class="flex-1 h-full relative min-h-0 flex flex-col">
                <!-- 在loading的时候 使用透明蒙层禁止选中 阻止点击穿透 -->
                <!-- <div v-if="requestLoading || contentLoading" class="absolute w-full h-full inset-0 bg-transparent pointer-events-auto z-10" @click.stop></div> -->
                <n-spin :show="requestLoading || contentLoading" style="--n-opacity-spinning:1;">
                    <template #icon></template>
                    <RichText class="flex-1 min-h-0" ref="richtextRef" :placeholder="`请先完善左侧区域表单信息, 然后点击 “开始创作”开始创作吧`"
                        :content="dataSource || ''" :extra-content="extraDataSource || ''" :expand="expand"
                        :rewrite="rewrite" :abbreviate="abbreviate" :continue="continue"
                        :loading="requestLoading || contentLoading" />
                </n-spin>
            </div>
            <div class="absolute bottom-[81px] left-0 w-full flex justify-center " v-if="requestLoading">
                <NButton color="#0E69FF" type="warning" ghost @click="stop" class="!bg-[#fff]">
                    停止
                    <NImage :src="Dot" preview-disabled width="14" height="14" class=" ml-[34px]" />
                </NButton>
            </div>

        </div>
        <Poster v-if="currentArea === 2" :markdown-content="handleSwitchToPoster()" @onBack="() => currentArea = 0" />
        <div v-show="currentArea !== 0 && currentArea !== 2"
            class="w-full h-[calc(100%-60px)] text-[#999] px-[20px] sm:px-0 pb-[20px] overflow-hidden overflow-y-scroll mt-[-45px]">
            <div class=" bg-[#0E69FF] w-[140px] h-[45px] flex flex-row justify-center items-center sticky top-[90%] z-[2] left-[50%] translate-x-[-50%] text-[#fff] cursor-pointer"
                @click="handleGoToCreate">
                返回创作
                <IconBack />
            </div>
            <!-- <div class="pt-[30%] " v-if="historyData.length == 0">
                <NEmpty />
            </div> -->
            <NCard v-for="(item, index) in historyData" :key="index" class="mt-[10px] first-of-type:mt-0">
                <div class="text-[#999] mb-[5px]">{{ dayjs(item.createdAt).format('YYYY-MM-DD HH:mm') }}
                </div>
                <div>
                    <NEllipsis :line-clamp="5" :tooltip="false">{{ item.text }}</NEllipsis>
                </div>
                <div class="flex justify-between items-center">
                    <div>
                        <NButton bordered round size="small" @click="handleCopy(item.text)" type="info" ghost>
                            <template #icon>
                                <NIcon>
                                    <DocumentCopy48Regular />
                                </NIcon>
                            </template>
                            复制
                        </NButton>
                    </div>
                    <div>
                        <NButton bordered size="small" @click="handleDownload(item)" type="info" ghost>
                            <template #icon>
                                <NImage :src="FileUpload" class=" w-[16px] h-[13px]" preview-disabled />
                            </template>
                            导出Word
                        </NButton>

                    </div>
                </div>
            </NCard>

        </div>
    </div>
    <PPTTemplate v-model:visible="pptTemplateVisible" @select="handleSelectPPTTemplate" />
</template>
<style lang="less">
.create-container {
    .n-tabs-nav-scroll-content {
        margin-left: 31px;
        height: 55px;
    }

    .n-tabs-pane-wrapper {
        position: unset;
    }

		.n-spin-container, .n-spin-content {
			height: 100%;
			min-height: 0;
			flex: 1;
			display: flex;
			flex-direction: column;
		}
}
</style>
