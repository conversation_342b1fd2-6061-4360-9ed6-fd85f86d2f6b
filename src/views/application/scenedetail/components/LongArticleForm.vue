<script lang="ts" setup>
import { NForm, NFormItem, NInput, NSelect, NDynamicTags, NButton, FormInst, NSwitch } from 'naive-ui'
import { renderOptions } from '@/utils/utils'
import UploadAndExtractText from './UploadAndExtractText.vue'
import LongArticleOutline from './LongArticleOutline.vue'
import { replaceText } from '@/plugins/directive'
import { computed, ref, watch } from 'vue'
import { getCredit } from '@/store/modules/auth/helper'
import KnowledgeButton from '@/components/common/KnowledgeButton/index.vue';

interface Props {
  currentRef: number | null
  allowedMaxStep: number
  currentStatus: any
  formData: any
  isNetwork?: boolean
  isKnowledge?: boolean
  list: any[]
  outlineContent?: string // 大纲本体
  outlineLoading: boolean // 大纲生成中
  contentLoading: boolean // 文章生成中
  outlineCreated: boolean // 大纲已生成
  handleFormReset: () => void
  handleFileParsed: (content: string) => void
  onFormValidate: (formData: any, isNetwork?: boolean, isDeepseek?: boolean, knowledgeId?:number|null) => void
  onOutlineGenerateContent: () => void
  onContentGenerate: () => void
}
const formRef = ref<FormInst | null>(null)
const props = defineProps<Props>()
const interval = ref<any>(null)
const handleLongArticleFormValidate = async (e: MouseEvent) => {
    e.preventDefault()
    try {
        await formRef.value?.validate()
        props.onFormValidate(props.formData, typeof props.isNetwork === 'boolean' ? networkRef.value : undefined, deepseekRef.value, knowledgeIdRef.value)
    } catch (error) {

    }
}
// outlineLoading为true的时候 生成一个持续2分钟的进度条变量 从0-100
const progress = ref(0)
const networkRef = ref(false);
const deepseekRef = ref(false);
const knowledgeIdRef = ref<number | null>(null);

// 每秒增加进度条的值，直到达到100
const increaseProgress = () => {
  interval.value = setInterval(() => {
    if (progress.value < 100) {
      progress.value += 1
    } else {
      if (!props.outlineLoading) {
        clearInterval(interval.value)
      }
    }
  }, 600)
}
watch(
  () => props.outlineLoading,
  (newVal) => {
    if (newVal) {
      progress.value = 0
      increaseProgress()
    } else {
      progress.value = 100
      clearInterval(interval.value)
    }
  },
)

const handleLongArticleGenerate = () => {
  props.onOutlineGenerateContent()
}

const handleContentGenerate = () => {
  props.onContentGenerate()
}
watch(
  () => props.allowedMaxStep,
  (newVal) => {
    console.log('allowedMaxStep', newVal)
  },
)
const credit = computed(() => getCredit('long') || '')

const handleSelectKnowledge = (value)=>{
	knowledgeIdRef.value = value;
}

</script>

<template>
  <NForm :model="formData" ref="formRef" class="dynamic-form-content h-full relative overflow-y-scroll HideScrollbar">
    <div class="h-[calc(100%-84px)] overflow-y-scroll HideScrollbar">
      <template v-for="field in list" v-if="currentRef === 1">
        <template v-if="field.formType === 'input'">
          <NFormItem
            :label="replaceText(field.label)"
            :path="field.label"
            :rule="{
              required: field.isRequired,
              message: `请输入${field.label}`,
            }"
          >
            <NInput v-model:value="formData[field.label]" :placeholder="field.placeholder" />
          </NFormItem>
        </template>
        <template v-if="field.formType === 'select'">
          <NFormItem
            :label="replaceText(field.label)"
            :path="field.label"
            :rule="{
              required: field.isRequired,
              message: `请选择${field.label}`,
            }"
          >
            <NSelect v-model:value="formData[field.label]" :placeholder="field.placeholder" :options="renderOptions(field.options as string[])" />
          </NFormItem>
        </template>
        <template v-if="field.formType === 'multiSelect'">
          <NFormItem
            :label="replaceText(field.label)"
            :path="field.label"
            :rule="{
              required: field.isRequired,
              message: `请选择${field.label}`,
            }"
          >
            <NSelect v-model:value="formData[field.label]" :placeholder="field.placeholder" :multiple="true" :options="renderOptions(field.options as string[])" />
          </NFormItem>
        </template>
        <template v-if="field.formType === 'textarea'">
          <NFormItem
            :label="replaceText(field.label)"
            :path="field.label"
            :rule="[
              {
                required: field.isRequired,
                message: `请输入${field.label}`,
              },
              {
                max: field.maxWord,
                message: `最多输入${field.maxWord}个字`,
              },
            ]"
          >
            <NInput type="textarea" v-model:value="formData[field.label]" :placeholder="field.placeholder" :rows="formData[field.label]?.length > 300 ? 13 : 6" :show-count="true" />
          </NFormItem>
        </template>
        <template v-if="field.formType === 'keywords'">
          <NFormItem
            :label="replaceText(field.label)"
            :path="field.label"
            :rule="{
              required: field.isRequired,
              message: `请输入${field.label}`,
            }"
          >
            <NDynamicTags v-model:value="formData[field.label]" :placeholder="field.placeholder" />
          </NFormItem>
        </template>
        <template v-if="field.formType === 'file'">
          <NFormItem :label="replaceText(field.label)" :path="field.label">
            <UploadAndExtractText :place-holder="field.placeholder" @file-parsed="handleFileParsed" />
          </NFormItem>
        </template>
      </template>
      <template v-if="isNetwork && currentRef === 1">
        <n-switch v-model:value="networkRef" /> <span class="text-[16px] leading-[23px] text-[#383838] relative top-[2px] left-[10px]">联网搜索</span>
        <div class="text-[#ADADAD] text-[14px] leading-[20px] mt-[10px] mb-[20px]">创作的内容具有实时性需求时，建议开启联网搜索，其他情况不建议开启。</div>
      </template>
      <template v-if="currentRef === 1">
        <n-switch v-model:value="deepseekRef" /> <span class="text-[16px] leading-[23px] text-[#383838] relative top-[2px] left-[10px]">DeepSeek 模型</span>
        <div class="text-[#ADADAD] text-[14px] leading-[20px] mt-[10px]">开启后将使用DeepSeek大模型进行创作</div>
      </template>
      <template v-if="isKnowledge && currentRef === 1">
        <div class="mt-[20px]">
            <KnowledgeButton @on-select-knowledge="handleSelectKnowledge" />
        </div>
      </template>
      <LongArticleOutline :outline="outlineContent" v-else-if="currentRef !== 1"></LongArticleOutline>
    </div>
    <NFormItem class="sticky bottom-0 w-full bg-[#fff] z-2">
      <div v-if="currentRef === 1" class="flex w-full flex-row gap-x-[11px] justify-center">
        <n-button type="success" :loading="outlineLoading || contentLoading" :disabled="outlineLoading || (contentLoading && currentRef < allowedMaxStep)" color="#0E69FF" class="flex-1 w-[50%] max-w-[360px]" @click="handleLongArticleFormValidate">下一步: 生成大纲</n-button>
        <n-button :loading="outlineLoading || contentLoading" :disabled="outlineLoading || contentLoading" type="default" color="#0066FE" ghost class="w-[36%] rounded-[4px] max-w-[173px]" @click="handleFormReset"> <IconDelete />清空录入 </n-button>
      </div>
      <div v-else-if="currentRef === 2" class="flex w-full flex-row gap-x-[11px] justify-center">
        <n-button type="success" :loading="outlineLoading || contentLoading" :disabled="outlineLoading || contentLoading || currentRef < allowedMaxStep" color="#0E69FF" class="flex-1 w-full max-w-[500px]" @click="handleLongArticleGenerate">下一步: 生成文章 {{ credit }}</n-button>
      </div>
      <div v-else class="flex w-full flex-row gap-x-[11px] justify-center">
        <n-button :loading="contentLoading" :disabled="contentLoading" type="default" color="#0066FE" class="flex-1 w-full max-w-[500px]" @click="handleContentGenerate">{{ outlineCreated ? `重新生成文章${credit}` : `生成文章${credit}` }}</n-button>
        <!-- <n-button :disabled="contentLoading" type="default" color="#0066FE" ghost
                    class=" w-[36%] rounded-[4px]  max-w-[173px]" @click="handleFormReset"><i
                        class="fi fi-rs-trash"></i>清空录入</n-button> -->
      </div>
    </NFormItem>
  </NForm>
</template>

<style lang="less" scoped></style>
