<script setup lang="ts">
import { onMounted, onUnmounted, computed, ref, reactive, nextTick } from 'vue'

import { NButton } from 'naive-ui'
import 'vue3-carousel/carousel.css'
import { Carousel, Slide, Navigation } from 'vue3-carousel'
import { useAuthStore, useVideoStore } from '@/store'
import { storeToRefs } from 'pinia'
import ProductCard from './components/ProductCard.vue'
import EffectCard from './components/EffectCard.vue'
import WaterfallItem from './components/WaterfallItem.vue'
import ImprovedWaterfallList from './components/ImprovedWaterfallList.vue'
import Text2videoImg from '@/assets/video/text2video.png'
import Img2videoImg1 from '@/assets/video/img2video1.png'
import Img2videoImg2 from '@/assets/video/img2video2.png'
import CreativeImg from '@/assets/video/creative.png'
import { useRouter } from 'vue-router'
import loadingImg from '@/assets/loading.png'
import { cdn2Compress } from '@/utils/cdn2Compress'
const authStore = useAuthStore()

const handleProductCardClick = (generationType: string, modelId?: number) => {
  const protocol = window.location.protocol
  const host = window.location.host.includes('chat-qa.mjmobi.com') ? 'media-chat-qa.mjmobi.com' : 'media.aiwork365.cn'
  const token = authStore.token
  window.location.href = `${protocol}//${host}/video/generate-video/?access_token=${token}&generationType=${generationType}${modelId ? `&modelId=${modelId}` : ''}`
}
const router = useRouter()
const homeStore = useVideoStore()
const isMounted = ref(false)
const videoWaterfallListRef = ref()
const waterfallContainerStyle = ref({ height: '3000px' })
const { visibleImages, loading, effectExamples, waterfallImages } = storeToRefs(homeStore)
const goToGenerateVideo = ({ effect, modelId }) => {

	const protocol = window.location.protocol
  const host = window.location.host.includes('chat-qa.mjmobi.com') ? 'media-chat-qa.mjmobi.com' : 'media.aiwork365.cn'
  const token = authStore.token
  window.location.href = `${protocol}//${host}/video/generate-video/?access_token=${token}&generationType=creative${modelId ? `&modelId=${modelId}` : ''}${effect ? `&effect=${effect}` : ''}`
}
const carouselRef = ref()
const carouselSlidesPerView = computed(() => {
  // 根据屏幕宽度动态调整 slides-per-view
  // 这里可以根据实际需求调整逻辑，例如使用响应式断点
  return 4.5 // 示例值，可以根据需要调整
})

const waterfallOptions = reactive({
  // 唯一key值
  rowKey: 'exampleId',
  // 卡片之间的间隙
  gutter: 16,
  // 是否有周围的gutter
  hasAroundGutter: false,
  // 卡片在PC上的宽度
  width: 280,
  // 自定义行显示个数，主要用于对移动端的适配
  breakpoints: {
    1440: {
      rowPerView: 5,
    },
    1024: {
      rowPerView: 4,
    },
    768: {
      rowPerView: 3,
    },
    480: {
      rowPerView: 2,
    },
  },
  // 动画效果
  animationEffect: 'animate__fadeInUp',
  // 动画时间
  animationDuration: 500,
  // 动画延迟
  animationDelay: 100,
  // 背景色
  backgroundColor: 'transparent',
  // imgSelector
  imgSelector: 'coverUrl',
  // 加载配置
  loadProps: {
    loading: loadingImg,
  },
  // 是否懒加载
  lazyload: false,
})

const handleScroll = () => {
  const scrollPosition = window.innerHeight + window.scrollY
  const pageHeight = document.documentElement.scrollHeight

  if (pageHeight - scrollPosition < 500) {
    homeStore.loadMore()
  }
}

// 鼠标悬停效果
const hoverCard = (index: number) => {
  const cards = document.querySelectorAll('.product-card')
  cards.forEach((card, i) => {
    if (i !== index) {
      ;(card as HTMLElement).style.opacity = '0.7'
    }
  })
}

const resetCards = () => {
  const cards = document.querySelectorAll('.product-card')
  cards.forEach((card) => {
    ;(card as HTMLElement).style.opacity = '1'
  })
}

onMounted(() => {
  homeStore.fetchVideoExamples()
  window.addEventListener('scroll', handleScroll)

  const cards = document.querySelectorAll('.product-card')
  cards.forEach((card, index) => {
    setTimeout(() => {
      card.classList.add('card-loaded')
    }, 100 * index)
  })
  nextTick(() => {
    isMounted.value = true
    setTimeout(() => {
      // 触发新瀑布流组件的布局更新
      videoWaterfallListRef.value?.forceUpdate()
      waterfallContainerStyle.value.height = 'auto'
    }, 100) // 减少延迟时间
  })
})

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped>
.home-container {
  /* margin: 0 auto; */
}

/* 自定义轮播图箭头样式 */
.custom-arrow {
  position: absolute;
  width: 100%;
  top: 50%;
  left: 0;
  transform: translateY(-50%);
  display: flex;
  justify-content: space-between;
  pointer-events: none;
  z-index: 1;
}

.custom-arrow--left,
.custom-arrow--right {
  pointer-events: auto;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.42);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

/* 产品卡片样式 */
.product-card {
  position: relative;
  overflow: hidden;
  border-radius: 16px;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.product-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 16px;
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  z-index: -1;
}

.product-card:hover {
  transform: scale(1.03) translateY(-8px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.product-card:hover::after {
  opacity: 1;
}

.card-loaded {
  animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 特效卡片样式 */
.effect-card {
  height: 320px;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.effect-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.15);
}

/* 瀑布流项目样式 */
.waterfall-item {
  break-inside: avoid;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.waterfall-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
}

/* 自定义按钮悬停效果 */
.n-button.n-button--primary-type {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
}

.n-button.n-button--primary-type::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.5s, height 0.5s;
}

.n-button.n-button--primary-type:hover::after {
  width: 300px;
  height: 300px;
}
.carousel {
  --vc-nav-background: rgba(0, 0, 0, 0.42); /* 与原 custom-arrow 背景色保持一致 */
  --vc-nav-border-radius: 50%; /* 与原 custom-arrow 保持一致 */
  --vc-nav-color: white; /* 箭头颜色 */
  --vc-nav-width: 40px; /* 箭头按钮宽度 */
  --vc-nav-height: 40px; /* 箭头按钮高度 */
  --vc-nav-box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); /* 阴影 */
  --vc-nav-transition: all 0.3s ease; /* 过渡效果 */
}

.carousel__slide {
  margin-right: 16px; /* 添加间距 */
  opacity: 1; /* 默认不透明 */
  transform: none; /* 默认无变换 */
}

/* 确保滑动时有过渡效果 */
.carousel__slide--sliding {
  transition: transform var(--carousel-transition);
}

.carousel.is-dragging .carousel__slide {
  transition: transform var(--carousel-transition);
}

/* 移除不必要的 3D 效果和透明度变化 */
.carousel__viewport {
  perspective: none;
}

.carousel__track {
  transform-style: flat;
}

.carousel__slide--prev,
.carousel__slide--active,
.carousel__slide--next,
.carousel__slide--next ~ .carousel__slide {
  opacity: 1;
  transform: none;
}
</style>
<template>
  <div class="home-container bg-gradient-to-b from-[#f5f7ff] via-[rgba(236, 241, 255, 0.8858)] to-[#e5f1ff]">
    <div class="max-w-[1600px] mx-[auto] pt-[44px] px-[50px]" >
      <!-- 顶部产品介绍卡片 -->
      <div class="product-cards-container">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 lg:gap-[20px]">
          <ProductCard title="文生视频" description="让文字智能变为视频，一键生成精彩视频" :images="[Text2videoImg]" cardClass="bg-[#EBF3FF] product-card-text2video cursor-pointer" @click="handleProductCardClick('text')" @mouseenter="hoverCard(0)" @mouseleave="resetCards" />

          <ProductCard title="图生视频" description="让静态图片秒变精彩视频，轻松实现创意" :images="[Img2videoImg1, Img2videoImg2]" cardClass="bg-[#F5EBFF] product-card-img2video cursor-pointer" @click="handleProductCardClick('image')" @mouseenter="hoverCard(1)" @mouseleave="resetCards" />

          <ProductCard title="创意特效" description="海量素材库+AI引擎，让想象力突破边界" :images="[CreativeImg]" cardClass="bg-[#EBFFF1] product-card-creative cursor-pointer" @click="handleProductCardClick('creative', 6)" @mouseenter="hoverCard(2)" @mouseleave="resetCards" />
        </div>
      </div>

      <!-- 中间特效创意轮播区 -->
      <div class="special-effects-container pt-12">
        <div class="flex justify-between items-center mb-8">
          <h2 class="text-2xl font-bold">特效创意</h2>
        </div>

        <Carousel v-if="effectExamples.length > 0" :items-to-show="carouselSlidesPerView" :transition="500" :wrap-around="true" snap-align="start" ref="carouselRef">
          <Slide v-for="(item, index) in effectExamples" :key="index">
            <EffectCard :title="item.effectText" :description="item.describe" :video-url="item.url" :poster="cdn2Compress(item.coverUrl!, {width: 400})" :icon="item.icon" :action-button-text="item.effectText" :views="'0'" :downloads="'0'" @click="goToGenerateVideo(item)" />
          </Slide>

          <template #addons>
            <Navigation />
          </template>
        </Carousel>
      </div>

      <!-- 底部瀑布流图片展示 -->
			<div class="waterfall-container" :style="waterfallContainerStyle">
				<div class="flex justify-between items-center mb-8">
					<h2 class="text-2xl font-bold">热门短片</h2>
				</div>
				<ImprovedWaterfallList
					v-if="isMounted"
					ref="videoWaterfallListRef"
					:list="waterfallImages"
					:options="waterfallOptions"
				/>
			</div>
    </div>
  </div>
</template>
