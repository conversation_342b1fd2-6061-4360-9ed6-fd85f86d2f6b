<template>
	<div
		class="relative"
		@mouseenter="handleMouseEnter"
		@mouseleave="handleMouseLeave"
	>
		  <img
		    v-show="!isHover"
		    :src="cdn2Compress(poster!, {quality: 70, width:400})"
		    :alt="title"
		    class="w-full aspect-[320/200] object-cover transition-transform duration-500 rounded-[6px] cursor-pointer"
		  />
		  <video
		    v-show="isHover"
		    ref="videoRef"
		    :src="videoUrl"
		    :poster="cdn2Compress(poster!, {quality: 70, width:400})"
		    :alt="title"
		    class="w-full aspect-[320/200] object-cover transition-transform duration-500 rounded-[6px] cursor-pointer"
		    loop
		    muted
		    preload="none"
		  />
		<div class="absolute top-[0px] left-[1px] z-10">
			<img
				class="text-sm font-medium text-white rounded-tl-[6px] rounded-br-[6px] shadow-md h-[30px]"
				:src="icon"
			/>
		</div>
		<span class="mt-[14px] text-[#3D3D3D] inline-block">{{ description }}</span>

		<div class="absolute bottom-[30px] left-[10px] z-10 tag rounded-[4px]">
			<button class="text-white px-4 py-2 text-sm ">
				{{ actionButtonText || "照片说话" }}
			</button>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { cdn2Compress } from '@/utils/cdn2Compress'

const videoRef = ref<HTMLVideoElement>();

defineProps({
	title: String,
	description: String,
	videoUrl: String,
	poster: String,
	icon: String,
	actionButtonText: String,
	views: String,
	downloads: String,
});

const isHover = ref(false);

const handleMouseEnter = () => {
  isHover.value = true;
  videoRef.value?.play();
};

const handleMouseLeave = () => {
	isHover.value = false;
	videoRef.value?.pause();
};
</script>
<style lang="less" scoped>
.tag {
	background: rgba(255, 255, 255, 0.16);
	color: #fff;
	border: 0.5px solid rgba(255, 255, 255, 0.56);
}
</style>
