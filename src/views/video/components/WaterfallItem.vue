<template>
  <div
    class="waterfall-item group"
    :style="{
      aspectRatio: itemStyle.aspectRatio,
    }"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    @click="goToGenerateVideo"
  >
    <div class="relative overflow-hidden rounded-xl cursor-pointer shadow-lg hover:shadow-xl transition-shadow duration-300">
      <img v-show="!isHovering" loading="lazy" :src="cdn2Compress(coverUrl!, {quality: 90, width: 400})" class="w-full object-cover transition-all duration-500 group-hover:scale-105" @load="handleImageLoad" @error="handleImageError" />
      <video preload="none" v-show="isHovering" ref="videoRef" :src="videoUrl" class="w-full object-cover transition-all duration-500 group-hover:scale-105" :poster="cdn2Compress(coverUrl!, {quality: 90, width: 400})" loop muted :data-id="taskCode" :autoplay="false"/>
      <div class="image-overlay absolute inset-0">
        <span class="absolute top-[9px] left-[8px] tag">
          <!-- 放tag -->
          {{ modelName }}
        </span>
        <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 via-black/40 to-transparent/10 opacity-0 group-hover:opacity-100 transition-all duration-300 backdrop-blur-[2px]">
          <div class="p-4 flex flex-col gap-2">
            <div class="flex items-center gap-2">
              <button
                class="flex-1 h-[30px] rounded-[4px] bg-[rgba(255,255,255,0.31)] hover:bg-[rgba(255,255,255,0.31)] backdrop-blur-[10px] text-white hover:text-white focus:text-white active:text-white font-medium text-sm transition-all duration-300 flex justify-center items-center"
              >
                生成同款
              </button>
              <n-icon class="text-white bg-[rgba(255,255,255,0.31)] !w-[30px] !h-[30px] rounded-[4px]" style="display: flex; align-items: center; justify-content: center" @click.stop="handleDownload">
                <FolderDownload style="width: 14px; height: 14px" />
              </n-icon>
              <n-icon class="text-white bg-[rgba(255,255,255,0.31)] !w-[30px] !h-[30px] rounded-[4px]" style="display: flex; align-items: center; justify-content: center">
                <Share style="width: 14px; height: 14px" />
              </n-icon>
            </div>
            <div class="flex items-end gap-1">
              <p class="text-sm font-medium text-white drop-shadow-sm line-clamp-2 flex-1">
                {{ prompt }}
              </p>
              <n-icon class="text-white cursor-pointer hover:opacity-80 transition-opacity flex items-center justify-center" @click.stop="copyPrompt"><CopyIcon class="flex items-center justify-center" /></n-icon>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { NIcon, useMessage } from 'naive-ui'
import { FolderDownload, Copy as CopyIcon, Share } from '@icon-park/vue-next'
import { useAuthStore } from '@/store'
import { cdn2Compress } from '@/utils/cdn2Compress'

const authStore = useAuthStore()

const videoRef = ref<HTMLVideoElement>()
const isHovering = ref(false)

const props = defineProps({
  videoUrl: String,
  height: Number,
  prompt: String,
  modelName: String,
  taskCode: {
    type: [String, Number],
    required: true,
  },
  modelId: Number,
  coverUrl: String,
  width: Number,
  bizType: String,
})

const emit = defineEmits<{
  imageLoaded: []
}>()

const handleImageLoad = () => {
  emit('imageLoaded')
}

const handleImageError = () => {
  console.warn('图片加载失败:', props.coverUrl)
}

const itemStyle = computed(() => {
  if (!props.width || !props.height) return {}

  // 根据图片原始宽高计算比例
  const aspectRatio = props.width / props.height

  return {
    aspectRatio,
  }
})

const handleMouseEnter = () => {
  isHovering.value = true
  videoRef.value?.play()
}

const handleMouseLeave = () => {
  isHovering.value = false
  videoRef.value?.pause()
}

const router = useRouter()

const goToGenerateVideo = () => {
  const protocol = window.location.protocol
  const host = window.location.host.includes('chat-qa.mjmobi.com') ? 'media-chat-qa.mjmobi.com' : 'media.aiwork365.cn'
  const token = authStore.token
  window.location.href = `${protocol}//${host}/video/generate-video?access_token=${token}&taskCode=${props.taskCode}&modelId=${props.modelId}`
}

const message = useMessage()

const copyPrompt = () => {
  navigator.clipboard.writeText(props.prompt || '')
  message.success('已复制到剪贴板')
}

const handleDownload = async () => {
  if (!props.videoUrl) {
    message.warning('视频链接无效')
    return
  }

  try {
    message.loading('正在准备下载...', { duration: 0 })
    const response = await fetch(props.videoUrl)
    const blob = await response.blob()
    const url = URL.createObjectURL(blob)

    const a = document.createElement('a')
    a.href = url
    a.download = `video-${props.taskCode || Date.now()}.mp4`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    message.destroyAll()
    message.success('视频下载开始')
  } catch (error) {
    message.destroyAll()
    message.error('下载失败: ' + (error as Error).message)
  }
}
</script>

<style scoped>
.waterfall-item {
  break-inside: avoid;
  transition: all 0.3s ease;
}

.waterfall-item:hover {
  transform: translateY(-6px);
}

.waterfall-item video {
  filter: brightness(1.02);
}

.waterfall-item:hover video {
  filter: brightness(1.05);
}

.image-overlay {
  background-blend-mode: overlay;
}
.tag {
  display: flex;
  background: rgba(0, 0, 0, 0.4);
  padding: 6px 12px;
  backdrop-filter: blur(10px);
  border-radius: 4px;
  color: #fff;
}
</style>
