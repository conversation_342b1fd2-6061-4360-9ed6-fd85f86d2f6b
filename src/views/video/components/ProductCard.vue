<template>
	<div
		class="product-card rounded-2xl p-6 flex items-center justify-between overflow-hidden relative cursor-pointer transition-all duration-300"
		:class="cardClass"
		@mouseenter="$emit('mouseenter')"
		@mouseleave="$emit('mouseleave')"
		@click="$emit('click')"
	>
		<div class="product-card-background absolute inset-0 z-0"></div>
		<div class="card-content z-10 max-w-full md:max-w-[70%] h-full">
			<h3 class="text-[24px] font-bold text-[#00218C]">{{ title }}</h3>
			<p class="text-[16px] text-[#304FB5] mt-[12px] mb-[20px] line-clamp-1">
				{{ description }}
			</p>
			<n-button
				class="mt-[20px] custom-button"
				type="primary"
				size="medium"
				ghost
				>{{ buttonText }}</n-button
			>
		</div>
		<div
			class="card-image bg-[#fff] rounded-[8px] relative"
			v-if="title === '文生视频'"
		>
			<img
				:src="images?.[0]"
				:alt="title"
				class="h-[114px] w-full p-[2px] object-cover"
			/>
			<div class="play-button">
				<div class="play-triangle"></div>
			</div>
		</div>
		<div
			class="card-image rounded-[8px] relative pr-[25px]"
			v-else-if="title === '图生视频'"
		>
			<img
				:src="images?.[0]"
				:alt="title"
				class="h-[95px] w-full p-[2px] object-cover relative z-[1] bg-[#fff]"
			/>
			<img
				:src="images?.[1]"
				:alt="title"
				class="absolute h-[95px] w-full p-[2px] object-cover top-[-20px] right-[-0px] bg-[#fff] opacity-[0.5]"
			/>
		</div>
		<div
			class="card-image-container bg-[#fff] rounded-[8px] relative p-[2px]"
			v-else
		>
			<div
				class="py-[8px] px-[6px] bg-gradient-to-b from-[#e4f6e5] to-[#fff] rounded-[8px]"
			>
				<img
					:src="images?.[0]"
					:alt="title"
					class="h-[77px] w-full object-cover object-[top] rounded-md shadow-md overflow-hidden"
				/>
				<div
					class="bg-[#9AE2A7] h-[9px] w-[78px] rounded-[2px] mt-[12px]"
				></div>
				<div
					class="bg-[#9AE2A7] h-[9px] w-[119px] rounded-[2px] mt-[7px] mb-[17px] "
				></div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import type { PropType } from "vue";

defineProps({
	title: String,
	description: String,
	buttonText: {
		type: String,
		default: "前往创作",
	},
	images: {
		type: Array as PropType<string[]>,
		default: () => [],
	},
	cardClass: String,
});

defineEmits(["mouseenter", "mouseleave", "click"]);
</script>

<style scoped>
.product-card {
	position: relative;
	overflow: hidden;
	max-height: 170px;
}

/* 通用卡片样式 */
.product-card::after {
	content: "";
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	border-radius: 1rem;
	box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
	opacity: 0;
	transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
	z-index: -1;
}

/* 文生视频卡片样式 */
.product-card-text2video {
	background-image: linear-gradient(90deg, #c7e1fc 0%, #9dbaf9 100%);
}

.product-card-text2video .card-image img {
	border-radius: 8px;
	/* box-shadow: 0 8px 16px rgba(0, 38, 96, 0.15);
  transform: rotate(3deg); */
}

/* 图生视频卡片样式 */
.product-card-img2video {
	background-image: linear-gradient(135deg, #f5ebff 0%, #ead6ff 100%);
}

.product-card-img2video .card-image img {
	border-radius: 12px;
	box-shadow: 0 8px 16px rgba(83, 0, 134, 0.15);
	transform: translateY(3px);
}

/* 创意特效卡片样式 */
.product-card-creative {
	background-image: linear-gradient(135deg, #ebfff1 0%, #d1ffe0 100%);
}

.product-card-creative .card-image img {
	border-radius: 50%;
	box-shadow: 0 8px 16px rgba(0, 96, 41, 0.15);
	transform: scale(0.95);
}

/* 自定义按钮样式 */
.custom-button {
	background: #fff;
	height: 34px;
	width: 102px;
	line-height: 34px;
	color: #304fb5;
	border-radius: 4px;
	padding: 6px 19px;
	margin-top: 20px;
	position: relative;
	overflow: hidden;
	transition: all 0.3s ease;
}

.custom-button::after {
	content: "";
	position: absolute;
	top: 50%;
	left: 50%;
	width: 0;
	height: 0;
	background-color: rgba(255, 255, 255, 0.2);
	border-radius: 50%;
	transform: translate(-50%, -50%);
	transition: width 0.5s, height 0.5s;
}

.custom-button:hover::after {
	width: 300px;
	height: 300px;
}

/* 文生视频按钮样式 */
.product-card-text2video .custom-button {
	--n-color: rgb(59, 130, 246);
	--n-color-hover: rgb(37, 99, 235);
	--n-text-color: rgb(59, 130, 246);
	--n-border-color: rgb(59, 130, 246);
}

/* 图生视频按钮样式 */
.product-card-img2video .custom-button {
	--n-color: rgb(139, 92, 246);
	--n-color-hover: rgb(124, 58, 237);
	--n-text-color: rgb(139, 92, 246);
	--n-border-color: rgb(139, 92, 246);
}

/* 创意特效按钮样式 */
.product-card-creative .custom-button {
	--n-color: rgb(16, 185, 129);
	--n-color-hover: rgb(5, 150, 105);
	--n-text-color: rgb(16, 185, 129);
	--n-border-color: rgb(16, 185, 129);
}

/* 播放按钮样式 */
.play-button {
	position: absolute;
	top: 65%;
	right: -18px;
	transform: translateY(-50%);
	width: 40px;
	height: 40px;
	background-color: #fff;
	border-radius: 50%;
	display: flex;
	justify-content: center;
	align-items: center;
	box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.play-triangle {
	width: 0;
	height: 0;
	border-top: 9px solid transparent;
	border-bottom: 9px solid transparent;
	border-left: 12px solid #0e69ff;
	margin-left: 3px; /* 稍微调整三角形位置，使其在视觉上居中 */
}
</style>
