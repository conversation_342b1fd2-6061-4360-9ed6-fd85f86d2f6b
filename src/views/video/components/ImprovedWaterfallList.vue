<template>
  <div class="masonry-container" ref="containerRef" :style="{ position: 'relative', width: '100%', height: `${containerHeight}px` }">
    <div
      v-for="(item) in layoutList"
      :key="item.id"
      class="masonry-item"
      :style="{
        position: 'absolute',
        top: `${item.y}px`,
        left: `${item.x}px`,
        width: `${item.width}px`,
        transition: 'top 0.3s, left 0.3s'
      }"
    >
      <WaterfallItem
        :video-url="item.data.url"
        :model-name="item.data.modelName"
        :height="item.data.height"
        :width="item.data.width"
        :prompt="item.data.describe"
        :task-code="item.data.exampleId"
        :model-id="item.data.modelId"
        :cover-url="item.data.coverUrl"
        :biz-type="item.data.bizType"
        @image-loaded="() => onItemImageLoad(item.id)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import type { PropType } from 'vue'
import WaterfallItem from './WaterfallItem.vue'

interface VideoItem {
  url: string
  modelName: string
  height: number
  width: number
  describe: string
  exampleId: number | string
  modelId: number
  coverUrl: string
  bizType: string
}

interface LayoutItem {
  id: string | number;
  x: number;
  y: number;
  width: number;
  height: number;
  data: VideoItem;
  loaded: boolean;
}

const props = defineProps({
  list: {
    type: Array as PropType<VideoItem[]>,
    required: true,
    default: () => []
  },
  options: {
    type: Object as PropType<any>,
    required: true,
    default: () => ({})
  }
})

const containerRef = ref<HTMLElement>()
const containerHeight = ref(0)
const layoutList = ref<LayoutItem[]>([])
const columnHeights = ref<number[]>([])
const ready = ref(false) // State to check if we are ready for calculation
const containerWidth = ref(0) // The reactive width of the container

const columnsCount = computed(() => {
  const width = containerWidth.value
  if (!width) return 2 // Default value before measurement
  const breakpoints = props.options.breakpoints || {}
  if (width >= 1440) return breakpoints[1440]?.rowPerView || 5
  if (width >= 1024) return breakpoints[1024]?.rowPerView || 4
  if (width >= 768) return breakpoints[768]?.rowPerView || 3
  if (width >= 480) return breakpoints[480]?.rowPerView || 2
  return 2
})

const gutter = computed(() => props.options.gutter || 16)
const rowKey = computed(() => props.options.rowKey || 'exampleId')

const calculateLayout = () => {
  if (!containerRef.value || !ready.value || containerWidth.value <= 0) return

  const currentContainerWidth = containerWidth.value
  const cols = columnsCount.value;
  const colWidth = (currentContainerWidth - (cols - 1) * gutter.value) / cols;
  if (colWidth <= 0) return;

  columnHeights.value = Array(cols).fill(0);
  const newLayoutList: LayoutItem[] = [];

  props.list.forEach((itemData, index) => {
    const itemAspect = itemData.height && itemData.width ? itemData.height / itemData.width : 1.25;
    const itemHeight = colWidth * itemAspect;

    const minHeight = Math.min(...columnHeights.value);
    const minIndex = columnHeights.value.indexOf(minHeight);

    const newItem: LayoutItem = {
      id: itemData[rowKey.value] || index,
      x: minIndex * (colWidth + gutter.value),
      y: minHeight,
      width: colWidth,
      height: itemHeight,
      data: itemData,
      loaded: false
    };
    newLayoutList.push(newItem);
    columnHeights.value[minIndex] += itemHeight + gutter.value;
  });

  layoutList.value = newLayoutList;
  containerHeight.value = Math.max(...columnHeights.value);
};

const onItemImageLoad = (id: string | number) => {
  const item = layoutList.value.find(it => it.id === id);
  if (item) {
    item.loaded = true;
  }
};

// Watch for initial data load
watch(
  [() => props.list, ready],
  ([list, isReady]) => {
    if (isReady && list.length > 0) {
      nextTick(() => {
        calculateLayout()
      })
    }
  },
  { deep: true }
)

let resizeObserver: ResizeObserver | null = null;
let resizeTimeout: ReturnType<typeof setTimeout> | undefined;

onMounted(() => {
  // We need to wait for the DOM to be ready
  nextTick(() => {
    if (!containerRef.value) return;

    resizeObserver = new ResizeObserver((entries) => {
      if (!entries || entries.length === 0) return
      const newWidth = entries[0].contentRect.width;
      if (newWidth > 0) {
        containerWidth.value = newWidth;
      }
    });

    resizeObserver.observe(containerRef.value);

    // Initial measurement after a short delay
    setTimeout(() => {
      if(containerRef.value) {
         containerWidth.value = containerRef.value.clientWidth;
      }
      ready.value = true;
    }, 200); // Delay to ensure everything is rendered
  });
});

onUnmounted(() => {
  if (resizeObserver && containerRef.value) {
    resizeObserver.unobserve(containerRef.value);
  }
  if (resizeTimeout) clearTimeout(resizeTimeout);
});

// A dedicated watcher for width changes to trigger layout calculation WITH DEBOUNCE
watch(containerWidth, (newWidth) => {
    if (!ready.value || newWidth <= 0) return;

    if (resizeTimeout) clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(() => {
        calculateLayout();
    }, 200); // 200ms debounce
});


defineExpose({
  forceUpdate: calculateLayout,
  renderer: calculateLayout
});
</script>

<style scoped>
.masonry-container {
  position: relative;
}

.masonry-item {
  animation: fadeInUp 0.5s ease-in-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
