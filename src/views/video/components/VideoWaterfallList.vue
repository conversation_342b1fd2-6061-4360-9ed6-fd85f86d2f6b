<script setup lang="ts">
import type { PropType } from 'vue'
import { ref } from 'vue'
import { Waterfall } from 'vue-waterfall-plugin-next'
import 'vue-waterfall-plugin-next/dist/style.css'
import WaterfallItem from './WaterfallItem.vue'
import { cdn2Compress } from '@/utils/cdn2Compress'

const props = defineProps({
  list: {
    type: Array as PropType<any[]>,
    required: true
  },
  options: {
    type: Object as PropType<any>,
    required: true
  }
})

const waterfallRef = ref()

defineExpose({
  waterfallRef
})

</script>

<template>
  <div class="waterfall-wrapper">
    <Waterfall
      ref="waterfallRef"
      class="waterfall-list"
    :list="list"
    :row-key="options.rowKey"
    :gutter="options.gutter"
    :has-around-gutter="options.hasAroundGutter"
    :width="options.width"
    :breakpoints="options.breakpoints"
    :img-selector="options.imgSelector"
    :background-color="options.backgroundColor"
    :animation-effect="options.animationEffect"
    :animation-duration="options.animationDuration"
    :animation-delay="options.animationDelay"
    :lazyload="options.lazyload"
    :load-props="options.loadProps"
    :cross-origin="options.crossOrigin"
  >
    <template #item="{ item }">
      <WaterfallItem
        :video-url="item.url"
        :model-name="item.modelName"
        :height="item.height"
				:width="item.width"
        :prompt="item.describe"
        :task-code="item.exampleId"
        :model-id="item.modelId"
        :cover-url="cdn2Compress(item.coverUrl!, {width: 280})"
				:biz-type="item.bizType"
      />
    </template>
    </Waterfall>
  </div>
</template>

<style scoped>
.waterfall-wrapper {
  width: 100%;
  min-height: 100vh;
}

.waterfall-list {
  width: 100%;
  height: auto;
  min-height: inherit;
}
</style>
