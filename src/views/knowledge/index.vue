<template>
	<div class="flex flex-col px-4 py-[30px] h-full">
		<div class="text-title font-bold text-[18px]">知识库</div>
		<div class="flex-1 min-h-0 flex gap-[12px] items-stretch mt-[20px]">
			<div class="bg-white px-4 py-6 w-[335px] flex flex-col" style="box-shadow: 0px 4px 9px 0px #EBEFFF;">
				<div class="flex justify-center mb-4 w-[300px] h-[50px] border-[#0E6EFF] border-solid border-[1px]">
					<NButton type="primary" text @click="addKnowledgeModal = { show: true, id: '', name: '' }">+ 添加知识库</NButton>
				</div>
				<div class="cell cursor-pointer rounded-sm mb-2" :class="{ active: activeKnowledgeId === item.id }"
					v-for="(item, index) in knowledgeList" :key="item.id" @click="activeKnowledgeId = item.id">
					<NEllipsis class="flex-1 text-[16px]">{{ item.name }}</NEllipsis>
					<NPopover trigger="click">
						<template #trigger>
							<EllipsisSvg class="w-6 h-6" />
						</template>
						<div class="-mx-[14px] -my-2 rounded-[6px] overflow-hidden">
							<div class="flex justify-between items-center p-3 px-4 gap-4 hover:bg-gray-50 cursor-pointer"
								@click="addKnowledgeModal = { show: true, ...item }">
								<img :src="EditImage" class="w-[14px] h-[14px]" />
								<span>编辑</span>
							</div>
							<div
								class="flex justify-between items-center p-3 px-4 gap-4 text-[#FF5100] hover:bg-gray-50 cursor-pointer"
								@click="() => handleDelete(item)">
								<DeleteSvg class="w-[14px] h-[14px]" />
								<span>删除</span>
							</div>
						</div>
					</NPopover>
				</div>
				<div class="flex justify-center mt-4" v-if="knowledgeListHasMore">
					<NButton type="primary" text :loading="loading">{{loading? '加载中...' : '加载更多'}}</NButton>
				</div>
			</div>
			<div class="flex-1 min-w-0 bg-white p-6" style="box-shadow: 0px 4px 9px 0px #EBEFFF;">
				<div class="flex justify-between items-center">
					<NSpace>
						<NButton type="primary" @click="importDocShow = true" :disabled="!activeKnowledgeId">导入文档</NButton>
						<NButton type="info" ghost :disabled="!activeKnowledgeId" @click="getKnowledgeDocList({ page: 1, name: '' })">刷新</NButton>
					</NSpace>
					<div>
						<NInput placeholder="文档查询" v-model:value="getKnowledgeDocListParams.name" clearable
							@update-value="handleInputSearch">
							<template #suffix>
								<SearchSvg class="w-3 h-3" />
							</template>
						</NInput>
					</div>
				</div>
				<NDataTable class="w-full mt-[25px]" remote :columns="columns" :data="knowlededgeDocList" :bordered="false"
					:scroll-x="800" :scroll-y="false" :pagination="pagination" />
			</div>
		</div>
		<ImportDoc v-model:show="importDocShow" :knowledge-id="activeKnowledgeId"
			@on-complete="getKnowledgeDocList({ page: 1 })" />
		<AddKnowledge v-model:show="addKnowledgeModal.show" :data="addKnowledgeModal"
			@on-complete="(id) => getKnowledgeList({ page: 1, id })" />
	</div>
</template>

<script setup lang="ts">
import { NButton, NDataTable, NEllipsis, NInput, NPopover, NSpace, useDialog, useMessage } from 'naive-ui';
import EllipsisSvg from '@/assets/aiwork/svg/ellipsis.svg'
import EditImage from '@/assets/aiwork/images/edit.png'
import DeleteSvg from '@/assets/aiwork/svg/delete.svg'
import SearchSvg from '@/assets/aiwork/svg/search.svg'
import RefreshSvg from '@/assets/aiwork/svg/refresh.svg'
import { h, onMounted, reactive, ref, watch } from 'vue';
import { TableColumn } from 'naive-ui/es/data-table/src/interface';
import ImportDoc from './components/ImportDoc.vue';
import AddKnowledge from './components/AddKnowledge.vue';
import request from '@/utils/request'
import dayjs from 'dayjs';
import { debounce } from '@/utils/functions/debounce';
import { getDocTypeIcon } from '@/utils/fileType/getDocTypeIcon';
import { getKnowledgeListApi, getKnowledgeFileList, delKnowledge, replaceKnowledgeFile, delKnowledgeFile } from './api';

const dialog = useDialog();
const message = useMessage()
const importDocShow = ref(false)
const addKnowledgeModal = ref({
	show: false,
	id: '',
	name: '',
})
const knowledgeList = ref<any>([])
const knowledgeListHasMore = ref(true)
const getKnowledgeListParams = ref({
	page: 1,
	pageSize: 10,
	name: ''
})
const activeKnowledgeId = ref('')
const knowlededgeDocList = ref<any>([])
const getKnowledgeDocListParams = ref({
	name: ''
})


onMounted(() => getKnowledgeList())
watch(() => activeKnowledgeId.value, (val) => {
	val && getKnowledgeDocList({ page: 1, knowledgeId: val })
})

const pagination = reactive({
	page: 1,
	pageSize: 20,
	showSizePicker: true,
	pageSizes: [10, 20, 50, 100],
	pageCount: 1,
	itemCount: 1,
	// prefix({ itemCount }) {
	//       return `Total is ${itemCount}.`
	//     },
	onChange: (page: number) => {
		pagination.page = page
		getKnowledgeDocList({ page })
	},
	onUpdatePageSize: (pageSize: number) => {
		pagination.pageSize = pageSize
		pagination.page = 1
		getKnowledgeDocList({ page: 1 })
	}
})

const loading= ref(false)
const getKnowledgeList = ({id, ...params}:any = {}) => {
	loading.value = true
	if(id) activeKnowledgeId.value = id
	const _params = {
		...getKnowledgeListParams.value,
		...params
	}
	return getKnowledgeListApi(_params)
		.then(res => {
			if (_params.page === 1) knowledgeList.value = res.rows
			else knowledgeList.value = knowledgeList.value.concat(res.rows)
			if (res.count <= knowledgeList.value.length) knowledgeListHasMore.value = false
			if (!activeKnowledgeId.value) activeKnowledgeId.value = knowledgeList.value[0]?.id
		}).finally(() => {
			loading.value = false
		})
}

const getKnowledgeDocList = (params: any) => {
	const _params = {
		page: pagination.page,
		pageSize: pagination.pageSize,
		knowledgeId: activeKnowledgeId.value,
		...getKnowledgeDocListParams.value,
		...params
	}
	getKnowledgeFileList(_params)
		.then(data => {
			const { page, pageSize, ...rest } = _params
			pagination.page = page
			pagination.pageSize = pageSize
			pagination.pageCount = Math.ceil(data.count / pageSize)
			pagination.itemCount = data.count
			getKnowledgeDocListParams.value = { ...rest }
			knowlededgeDocList.value = data.rows
		})
}

const columns: TableColumn[] = [
	{
		title: '文档名',
		key: 'filename',
		width: 240,
		fixed: 'left',
		render(row) {
			return h('div', { class: 'flex items-center gap-4' }, [
				h('img', { class: 'w-[27px] shrink-0', src: getDocTypeIcon(row.filename as string) }),
				h('div', { class: 'flex flex-col max-w-[200px]' }, [
					h(NEllipsis, { class: 'text-title text-[16px] inline-block' }, row.filename as string),
					h('span', { class: 'text-secondary' }, `ID: ${row.id as string}`)
				])
			])
		}
	},
	{
		title: '文件格式',
		key: 'fileFormat',
		width: 100
	},
	{
		title: '文件大小',
		key: 'size',
		width: 100,
		render(row: any) {
			return h('span', { class: 'text-secondary' }, row.size > 1024 * 1024 ? `${(row.size / 1024 / 1024).toFixed(2)}MB` : `${(row.size / 1024).toFixed(2)}KB`)
		}
	},
	{
		title: '学习状态',
		key: 'status',
		width: 100,
		render(row: any) {
			return h('div', {
				class: {
					'text-primary': row.status == 1 || row.status == 2,
					'text-red-400': row.status == 3
				},
				style: {
					'--n-icon-size': '12px',

				}
			}, {
				default: () => ['', '解析中...', '学习成功', '学习失败'][row.status],
				// icon: () => row.status == 3 && h(RefreshSvg),
			})
		}
	},
	{
		title: '创建时间',
		key: 'createdAt',
		width: 200,
		render(row) {
			return h('span', { class: 'text-secondary' }, dayjs(row.createdAt as string).format('YYYY-MM-DD HH:mm:ss'))
		}
	},
	{
		title: '创建人',
		key: 'User',
		width: 90,
		render(row: any) {
			return h('span', { class: 'text-secondary' }, row.User.nickname as string)
		}
	},
	{
		title: '操作',
		key: 'actions',
		width: 150,
		fixed: 'right',
		render(row) {
			return h('div', { class: 'flex gap-3' }, [
				h(NButton, { type: 'primary', text: true, onClick: () => { handleReplaceDoc(row) } }, '替换'),
				h(NButton, { type: 'error', text: true, onClick: () => handleDeleteDoc(row) }, '删除')
			])
		}
	}
]

// 替换知识库文档
const handleReplaceDoc = (item) => {
	const input = document.createElement('input')
	input.type = 'file'
	input.accept = '.doc,.docx,.pdf,.md,.xlsx,.pptx'
	input.onchange = () => {
		if (!input.files || !input.files[0]) return
		const file = input.files[0]
		if(file.size > 20 * 1024 * 1024) return message.error('文件大小不能超过20MB')
		message.loading('上传中...')
		const formData = new FormData()
		formData.append('file', file)
		formData.append('knowledgeId', activeKnowledgeId.value)
		formData.append('id', item.id)
		replaceKnowledgeFile(formData)
			.then(() => {
				message.destroyAll()
				message.success('替换成功')
				getKnowledgeDocList({})
			})
	}
	input.click()
}

const handleDelete = (item: any) => {
	document.body.click()
	const d = dialog.warning({
		title: '确认删除',
		content: '删除后会影响智能体的训练结果，请谨慎操作',
		positiveText: '确定',
		negativeText: '取消',
		onPositiveClick: () => {
			d.loading = true
			d.positiveText = '删除中...'
			return delKnowledge({ id: item.id })
				.then(res => {
				message.success('删除成功')
				getKnowledgeList({ page: 1 })
					.then(() => {
						if (activeKnowledgeId.value === item.id) activeKnowledgeId.value = knowledgeList.value[0].id
					})
				return
			})
		}
	})
}
const handleDeleteDoc = (item) => {
	dialog.warning({
		title: '确认删除',
		content: '删除后会影响智能体的训练结果，请谨慎操作',
		positiveText: '确定',
		negativeText: '取消',
		onPositiveClick: () => {
			message.loading('删除中...')
			return delKnowledgeFile({
					knowledgeId: activeKnowledgeId.value,
					id: item.id
				})
				.then(() => {
					message.success('删除成功')
					if(pagination.itemCount % pagination.pageSize <= 1) pagination.page = Math.max(pagination.page - 1, 1)
					getKnowledgeDocList({page: pagination.page})
					return true
				})
				.finally(() => {
					message.destroyAll()
					message.success('删除成功')
				})
		}
	})
}
const handleInputSearch = debounce(() => {
	getKnowledgeDocList({ page: 1 })
}, 500)
</script>

<style lang="less">
.cell {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 16px;
	transition: all 0.3s ease-in-out;

	&:hover,
	&.active {
		background-color: #E6F1FF;
		color: #0E69FF;
	}
}

.n-scrollbar>.n-scrollbar-container {
	overflow-y: hidden;
}
</style>
