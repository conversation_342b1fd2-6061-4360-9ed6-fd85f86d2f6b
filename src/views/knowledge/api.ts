import { post } from '@/utils/request';


// 知识库列表
export function getKnowledgeListApi<T>(params: any) {
    return post<T>({
        url: "/api3/aiwork/pknowledge/list",
        data: params,
    });
}

// 知识库列表-不分页
export function getKnowledgeListAll<T>(params: any) {
    return post<T>({
        url: "/api3/aiwork/pknowledge/listAll",
        data: params,
    });
}
// 新增知识库
export function addKnowledge<T>(params: any) {
    return post<T>({
        url: "/api3/aiwork/pknowledge/add",
        data: params,
    });
}
// 编辑知识库
export function updateKnowledge<T>(params: any) {
    return post<T>({
        url: "/api3/aiwork/pknowledge/update",
        data: params,
    });
}
// 删除知识库
export function delKnowledge<T>(params: any) {
    return post<T>({
        url: "/api3/aiwork/pknowledge/del",
        data: params,
    });
}
// 知识文档列表
export function getKnowledgeFileList<T>(params: any) {
    return post<T>({
        url: "/api3/aiwork/pknowledge/fileList",
        data: params,
    });
}
// 新增知识库文档
export function uploadKnowledgeFile<T>(params: any) {
    return post<T>({
        url: "/api3/aiwork/pknowledge/upload",
        data: params,
    });
}
// 替换知识库文档
export function replaceKnowledgeFile<T>(params: any) {
    return post<T>({
        url: "/api3/aiwork/pknowledge/replace",
        data: params,
    });
}
// 删除知识库文档
export function delKnowledgeFile<T>(params: any) {
    return post<T>({
        url: "/api3/aiwork/pknowledge/delFile",
        data: params,
    });
}