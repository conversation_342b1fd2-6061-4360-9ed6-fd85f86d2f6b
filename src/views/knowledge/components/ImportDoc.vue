<template>
	<NModal v-model:show="show" preset="card" class="w-[740px]" style="width: 740px;">
		<template #header>
			<div>导入文档</div>
		</template>
		<div class=" bg-white rounded-md">
			<NUpload directory-dnd :show-file-list="false" multiple :file-list="fileList" @change="handleUploadChange"
				accept=".doc,.docx,.pdf,.md,.xlsx,.pptx,.xlsx">
				<NUploadDragger class="w-full h-[229px] border-dashed border border-[#0066FF] overflow-y-scroll"
					style="--n-dragger-border: 1px dashed #0066FF;--n-dragger-color: #fff;">
					<div v-if="fileList.length" class="file-list min-h-full">
						<template v-for="(file, index) in fileList">
							<div
								class="file-item p-2 transition-all duration-300 ease-in-out hover:bg-[#EDF4FF] rounded-md flex items-center justify-start flex-col bg-gray-50" :class="{ '!bg-red-200': file.file && file.file.size > 30 * 1024 * 1024 }">
								<img :src="getDocTypeIcon(file.name)" class="w-[35px]" />
								<NEllipsis class="mt-[10px]">{{ file.name }}</NEllipsis>
								<div class="file-item-actions flex justify-between text-[12px] w-full mt-[8px]">
									<!-- <NButton type="primary" text>重新上传</NButton> -->
									<span class="text-gray-400" :class="{ 'text-red-500': file.file && file.file.size > 30 * 1024 * 1024 }">{{ getFileSize(file.file?.size) }}</span>
									<NButton type="error" text @click.stop="fileList.splice(index, 1)">删除</NButton>
								</div>
							</div>
						</template>
					</div>
					<div v-if="!fileList.length" class="h-[229px] flex flex-col items-center justify-center">
						<UploadSvg class="text-[#E4E4E4]" />
						<p class="mt-7">将文件拖拽至此区域或 <span class="text-primary">点击添加</span></p>
						<p class="text-gray-400 text-[12px] mt-[2]">支持.doc .docx .pdf .md .xlsx .pptx格式</p>
						<p class="text-gray-400 text-[12px]">限20M以内, 单次最多上传 10 个文档</p>
					</div>
				</NUploadDragger>
			</NUpload>
			<div class="mt-2 text-gray-400 text-[12px] text-center" :class="{ '!text-red-500': fileList.length > 10 }">{{ `${fileList.length}/10` }} {{ fileList.length > 10 ? '单次最多上传 10 个文档':'' }}</div>
			<div class="mt-3 bg-[#DDE2EB] bg-opacity-[28%] p-[10px] rounded-md">
				<p>温馨提示：</p>
				<ol class="text-secondary">
					<li>1. 文档导入过程中请不要关闭浏览器或浏览器标签页，否则会造成录入失败</li>
					<li>2. 文档内容中表格和图片可能存在无法学习的情况</li>
					<li>3. FAQ文档需按照参考文档示例上传才能准确识别，文档必须是.faq.xlsx格式 <a href="https://cdn2.weimob.com/static/aiwork365-web-stc/assets/模版.faq.xlsx" download class="text-primary cursor-pointer">下载FAQ模板</a></li>
					<li>4. 单次最多上传 10 个文档</li>
				</ol>
			</div>
			<div class="text-center mt-[18px]">
				<NButton type="primary" style="--n-width: 232px;--n-height: 44px;" :loading="loading" @click="handleImport">确认导入</NButton>
			</div>
		</div>
	</NModal>
</template>

<script setup lang="ts">
import { NButton, NEllipsis, NModal, NSpace, NText, NUpload, NUploadDragger, UploadFileInfo, useMessage } from 'naive-ui';
import UploadSvg from '@/assets/aiwork/svg/upload.svg'
import { ref, watch } from 'vue';
import request from '@/utils/request'
import { getDocTypeIcon } from '@/utils/fileType/getDocTypeIcon';
import { uploadKnowledgeFile } from '../api';

const show = defineModel<boolean>('show')
const props = defineProps<{ knowledgeId: string }>()
const emit = defineEmits<{
	onComplete: []
}>()

const fileList = ref<UploadFileInfo[]>([])
const message = useMessage()
const loading = ref(false)

watch(() => show.value, () => {
	if (!show.value) fileList.value = []
})

const handleImport = () => {
	if(loading.value) return
	if(!fileList.value.length) return message.error('请选择文件')
	if(fileList.value.length > 10) return message.error('单次最多上传 10 个文档')
	let overSize = true
	const formData = new FormData()
	formData.append('knowledgeId', props.knowledgeId)
	for (const file of fileList.value) {
		if(file.file && file.file.size > 30 * 1024 * 1024) return overSize = false
		formData.append('files', file.file as File)
	}
	if(!overSize) return message.error('文件大小不能超过30M')
	loading.value = true
	uploadKnowledgeFile(formData)
	.then(() => {
		show.value = false
		emit('onComplete')
	}).finally(() => loading.value = false)

}
const handleUploadChange = (data: { fileList: UploadFileInfo[] }) => {
	fileList.value = data.fileList
}

const getFileSize = (size?: number) => {
	if (!size) return '-'
	if (size < 1024) {
		return `${size}B`
	} else if (size < 1024 * 1024) {
		return `${(size / 1024).toFixed(2)}KB`
	} else if (size < 1024 * 1024 * 1024) {
		return `${(size / 1024 / 1024).toFixed(2)}MB`
	}
}
</script>

<style lang="less">
.file-list {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(106px, 120px));
	grid-gap: 8px;
	justify-content: center;
	align-items: center;
}

.file-item {
	max-width: 120px;
	height: 110px;
}

// .file-item .file-item-actions {
// 	display: none;
// }

// .file-item:hover .file-item-actions {
// 	display: flex;
// }
</style>
