<template>
	<!-- 新建/编辑智能体 -->
	<NModal v-model:show="show" preset="dialog">
		<template #header>
			<div>{{data?.id ? '编辑' : '新建'}}知识库</div>
		</template>
		<NForm ref="formRef" :model="form" label-placement="left" label-align="right" label-width="auto"
			require-mark-placement="right-hanging" class="pt-4" :rules="{ name: { required: true, message: '请输入知识库名称' } }">
			<div v-if="form.id" class="hidden">
				<NFormItem label="id" path="id">
					<NInput v-model:value="form.id" disabled />
				</NFormItem>
			</div>
			<NFormItem label="知识库名称" required path="name">
				<NInput v-model:value="form.name" show-count :maxlength="8" placeholder="请填写知识库名称" />
			</NFormItem>
			<div class="flex justify-center gap-[18px]">
				<NButton round @click="show = false" style="--n-width: 130px;">取消</NButton>
				<NButton type="primary" :loading="loading" round @click="submit" style="--n-width: 130px;">确认</NButton>
			</div>
		</NForm>
	</NModal>
</template>

<script setup lang="ts">
import { NAvatar, NButton, NDropdown, NForm, NFormItem, NInput, NModal, NPopover, NUpload } from 'naive-ui';
import { ref } from 'vue';
import request from '@/utils/request';
import { watch } from 'vue';
const show = defineModel<boolean>('show')
const props = defineProps<{ data?: any }>()
const emit = defineEmits<{
	onComplete: [id: number]
}>()

const form = ref<{
	name?: string,
	id?: string
}>({})
const formRef = ref<any>()
const loading = ref(false)

watch(() => props.data, value => {
	form.value = value
})

const submit = () => {
	formRef.value?.validate().then(() => {
		loading.value = true
		request({
			url: props.data?.id ? '/api3/aiwork/pknowledge/update' : '/api3/aiwork/pknowledge/add',
			method: 'POST',
			data: form.value
		}).then(data => {
			show.value = false
			emit('onComplete', data.id)
		}).finally(() => loading.value = false)
	})

}
</script>
