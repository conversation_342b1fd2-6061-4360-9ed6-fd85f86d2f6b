<template>
	<div class="w-full h-[58px] bg-white rounded-[10px] shadow-box flex justify-between items-center">
		<div class="flex items-center h-full gap-x-[6px]  relative top-[-4px]">
			<component :is="IconFolderUpload" />
			<n-dropdown placement="bottom-start" trigger="click" size="large" :options="options" @select="handleSelect">
				<span class=" cursor-pointer text-[16px] text-[#3d3d3d]">
					<div class="flex items-center gap-x-1 relative top-[1px]">
						上传文件
					</div>
				</span>
			</n-dropdown>
		</div>
		<div class="flex items-center gap-x-[25px]">
			<div class="text-[16px] text-[#3d3d3d]">演讲备注
				<NSwitch v-model:value="enableRemark" />
			</div>
			<div class="text-[16px] text-[#3d3d3d]">联网搜索
				<NSwitch v-model:value="webSearch" />
			</div>
		</div>
		<!-- 文本输入 是一个textarea -->
		<n-modal v-model:show="showTextModal" :on-after-leave="handleAfterLeave" :style="{ width: '804px' }">
			<n-card title="文本输入" role="dialog" aria-modal="true" style="width: 804px;">
				<n-input type="textarea" v-model:value="extraContent" placeholder="请输入文本内容" rows="10" show-count
					maxlength="8000" />
				<template #footer>
					<div class="flex justify-center">
						<!-- 最大宽度318px -->
						<n-button type="primary" :disabled="!extraContent" @click="handleTextSubmit"
							class="!max-w-[318px] !w-[40%]">确认</n-button>
					</div>
				</template>
			</n-card>
		</n-modal>
		<!-- 文件上传 -->
		<n-modal v-model:show="showUploadModal" title="上传文件" :on-after-leave="handleAfterLeave" :style="{ width: '804px' }">
			<n-card title="文本输入" role="dialog" aria-modal="true" style="width: 804px;">
				<n-upload action="/api/upload" :max="1" accept=".doc,.docx,.pdf" @finish="handleUploadFinish">
					<n-upload-dragger>
						<div class="flex flex-col items-center">
							<n-icon size="48" depth="3">
								<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
									<path
										d="M11 14.9861C11 15.5384 11.4477 15.9861 12 15.9861C12.5523 15.9861 13 15.5384 13 14.9861V7.82831L16.2428 11.0711L17.657 9.65685L12.0001 4L6.34326 9.65685L7.75748 11.0711L11 7.82854V14.9861Z">
									</path>
									<path d="M4 14H6V18H18V14H20V18C20 19.1046 19.1046 20 18 20H6C4.89543 20 4 19.1046 4 18V14Z">
									</path>
								</svg>
							</n-icon>
							<p>点击或者拖动文件到该区域来上传</p>
							<p class="text-gray-400 text-sm">支持 doc、docx、pdf 格式</p>
						</div>
					</n-upload-dragger>
				</n-upload>
			</n-card>
		</n-modal>
	</div>
</template>

<script lang="ts" setup>
import { NDropdown, NSwitch, NModal, NInput, NButton, NUpload, NUploadDragger, NIcon, NCard } from 'naive-ui';
import { h, ref } from 'vue';
import { icons } from '@/plugins/icons';
import { TypeEnums } from '../../types';
const { IconText, IconWord, IconFolderUpload } = icons

interface Emit {
	(e: 'update:search', value: { type: TypeEnums, webSearch: boolean, enableRemark: boolean, extraContent: string }): void
}

const emit = defineEmits<Emit>()

const Types = {
	[TypeEnums.TEXT]: IconText,
	[TypeEnums.UPLOAD]: IconWord
}
const enableRemark = ref(false)
const webSearch = ref(false)
const showTextModal = ref(false)
const showUploadModal = ref(false)
const extraContent = ref('')

const options = [
	{
		label: () => h('div', { class: 'flex items-center gap-x-1 text-[16px] text-[#505050]' }, [
			h(IconText),
			'文本输入'
		]),
		key: TypeEnums.TEXT,
	},
	{
		label: () => h('div', { class: 'flex items-center gap-x-1 text-[16px] text-[#505050]' }, [
			h(IconWord),
			'上传文档'
		]),
		key: TypeEnums.UPLOAD,
	},
]
const selected = ref(TypeEnums.TEXT)

const handleSelect = (key: TypeEnums) => {
	selected.value = key
	if (key === TypeEnums.TEXT) {
		showTextModal.value = true
	} else {
		showUploadModal.value = true
	}
}

const handleAfterLeave = () => {
	extraContent.value = ''
}

const handleTextSubmit = () => {
	showTextModal.value = false
	emit('update:search', {
		type: selected.value,
		webSearch: webSearch.value,
		enableRemark: enableRemark.value,
		extraContent: extraContent.value
	})
}

const handleUploadFinish = () => {
	showUploadModal.value = false
}
</script>

<style lang="less" scoped></style>
