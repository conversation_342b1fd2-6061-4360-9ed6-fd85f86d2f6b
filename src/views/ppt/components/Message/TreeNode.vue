<template>
  <ul class="tree HideScrollbar" :class="{ 'is-title': isTitle }">
    <li v-for="(item, index) in treeData" :key="index"
      :class="{ 'is-chapter': !item.paragraphNum, 'is-title': isTitle }">
      <div class="tree-item" :class="{ 'editing': currentId === item.id }" @mouseover="hoverId = item.id"
        @mouseleave="hoverId = null">
        <summary class="flex-1 max-w-[90%]">
          <n-input :maxlength="50" v-if="currentId === item.id" :default-value="item.title" ref="editInput"
            class="edit-input" @blur="finishEditing" @key.enter="finishEditing" />
          <div v-else>
            <span class="cursor-pointer truncate inline-block w-full"
              :class="[{ 'font-bold': isTitle || !item.paragraphNum }, hoverId === item.id ? 'hovered' : '']"
              @click="handleEdit(item)" @blur="finishEditing">{{ item.title }}</span>
          </div>
        </summary>
        <IconWritingFluently v-if="isTitle" class="cursor-pointer mx-[10px] text-[#8e90a5]" @click="handleEdit(item)" />

        <n-popover trigger="hover" placement="right" v-else @update:show="handleUpdateShow(item)">
          <template #trigger>
            <IconMoreThree class="cursor-pointer mx-[10px] text-[#8e90a5]"
              :class="[hoverId === item.id ? 'text-[#1B6FFF]' : '']" />
          </template>

          <div class="flex flex-col" v-if="item.paragraphNum" @mouseover="hoverId = item.id"
            @mouseleave="hoverId = null">

            <div class="flex items-center justify-start cursor-pointer hover:bg-[#f5f5f5]"
              @click="handleEdit(item)">
              <IconWritingFluently class="cursor-pointer mx-[10px] text-[#8e90a5]" @click="handleEdit(item)" /> 编辑
            </div>
            <div class="flex items-center justify-start cursor-pointer hover:bg-[#f5f5f5]"
              @click="handleUpgrade(item, index)">
              <IconBringForward class="cursor-pointer mx-[10px] text-[#8e90a5]" /> 升级
            </div>
            <div class="flex items-center justify-start cursor-pointer hover:bg-[#f5f5f5]"
              @click="handleAddSubset(item, index)">
              <IconAddSubset class="cursor-pointer mx-[10px] text-[#8e90a5]" /> 新增段落
            </div>
            <div class="flex items-center justify-start cursor-pointer hover:bg-[#f5f5f5]"
              @click="handleDelete(item, index)">
              <IconDelete class="cursor-pointer mx-[10px] text-[#8e90a5]" /> 删除
            </div>
          </div>
          <div v-else>
            <div class="flex items-center justify-start cursor-pointer hover:bg-[#f5f5f5]"
              @click="handleEdit(item)">
              <IconWritingFluently class="cursor-pointer mx-[10px] text-[#8e90a5]" @click="handleEdit(item)" /> 编辑
            </div>
            <div class="flex items-center justify-start cursor-pointer hover:bg-[#f5f5f5]" v-if="item.chapterNum !== 1"
              @click="handleDownGrade(item, index)">
              <IconSendBackward class="cursor-pointer mx-[10px] text-[#8e90a5]" /> 降级
            </div>
            <div class="flex items-center justify-start cursor-pointer hover:bg-[#f5f5f5]"
              @click="handleAddChapter(item, index)">
              <IconListAdd class="cursor-pointer mx-[10px] text-[#8e90a5]" /> 新增章节
            </div>
            <div class="flex items-center justify-start cursor-pointer hover:bg-[#f5f5f5]"
              @click="handleAddSubset(item, index)">
              <IconAddSubset class="cursor-pointer mx-[10px] text-[#8e90a5]" /> 新增段落
            </div>
            <div class="flex items-center justify-start cursor-pointer hover:bg-[#f5f5f5]"
              @click="handleDelete(item, index)">
              <IconDelete class="cursor-pointer mx-[10px] text-[#8e90a5]" /> 删除
            </div>
          </div>
        </n-popover>
        <span class="label absolute top-[8px] left-[-75px] text-[#8e90a5]" :class="`label-${item.depth}`">{{ item.depth
          === 0 ?
          item.label : item.label ? `${item.label}${item.chapterNum}` : '' }}</span>
      </div>
      <!-- <tree-node v-if="item.children && item?.children?.length" :tree-data="item.children"></tree-node> -->
    </li>
  </ul>
</template>

<script lang="ts" setup>
import { NInput, NEllipsis,NPopover } from 'naive-ui'
import { ref, nextTick } from 'vue'
import { useOutlineStore } from '@/store/modules/outline'
import { TreeNodes } from '../../types';
import { generateId } from '@/utils/utils';

interface Props {
  treeData: TreeNodes[]
  isTitle?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  isTitle: false
})

const outlineStore = useOutlineStore()

const hoverId = ref<string | null>(null)
const editInput = ref<HTMLInputElement | null>(null)
const currentId = ref<string | null>(null)

const handleUpdateShow = (item: TreeNodes) => {

}
const handleEdit = (item: TreeNodes) => {
  currentId.value = item.id
  nextTick(() => {
    editInput?.value?.focus?.()
  })
}
const finishEditing = (event: FocusEvent) => {
  const target = event.target as HTMLInputElement;
  if (!target.value) return
  const editedOutline = props.treeData.map(item => {
    if (item.id === currentId.value) {
      return { ...item, title: target.value }
    }
    return item
  })
  outlineStore.updateOutline(editedOutline, props.isTitle)
  currentId.value = null;
}
// 升级后把当前这一项目的chapterNum+1 paragraphNum=undefined
// 这一条之后的有paragraphNum的节点都归属到当前这一项目
// 然后之后的章节都+1 有paragraphNum的节点都+1
// 所以首先需要截取三段 一段是该节点之前的 原封不动
// 一段是该子节点到下一个章节之间所有的节点
// 一段是下一个章节到最后的所有节点
const handleUpgrade = (item: TreeNodes, currentIndex: number) => {

  // 找到下一个章节的索引
  const nextChapterIndex = props.treeData.findIndex((node, index) => {
    return index > currentIndex && !node.paragraphNum
  })

  // 分割三段
  const beforeNodes = props.treeData.slice(0, currentIndex)
  const currentAndChildrenNodes = props.treeData.slice(currentIndex, nextChapterIndex === -1 ? undefined : nextChapterIndex)
  const afterNodes = nextChapterIndex === -1 ? [] : props.treeData.slice(nextChapterIndex)

  // 升级当前节点
  const upgradedNode = {
    ...item,
    chapterNum: afterNodes.length ? afterNodes[0].chapterNum : beforeNodes[beforeNodes.length - 1].chapterNum + 1,
    paragraphNum: undefined,
    type: 'chapter',
    label: '章节'
  }

  // 更新子节点的章节号
  const updatedChildrenNodes = currentAndChildrenNodes.slice(1).map(node => ({
    ...node,
    chapterNum: upgradedNode.chapterNum,
    paragraphNum: node.paragraphNum ? node.paragraphNum + 1 : undefined
  }))

  // 更新后续章节的章节号
  const updatedAfterNodes = afterNodes.map(node => ({
    ...node,
    chapterNum: node.chapterNum + 1,
    paragraphNum: node.paragraphNum ? node.paragraphNum + 1 : undefined
  }))

  // 合并所有节点
  const updatedOutline = [
    ...beforeNodes,
    upgradedNode,
    ...updatedChildrenNodes,
    ...updatedAfterNodes
  ]

  outlineStore.updateOutline(updatedOutline, props.isTitle)
}
// 降级后分成三段, 首先一段当前章节之前的所有数据
// 其次是当前章节及当前章节内所有节点的数据
// 最后是当前节点之后的所有节点
// 将当前章节降级,chapterNum-1 paragraphNum = chapterNum-1
// 当前章节内所有的子章节,chapterNum-1 paragraphNum = chapterNum-1
// 然后合并到上一个章节去
// 后面所有的章节全部chapterNum-1 paragraphNum = chapterNum-1
const handleDownGrade = (item: TreeNodes, currentIndex: number) => {
  // 找到下一个章节的索引
  const nextChapterIndex = props.treeData.findIndex((node, index) => {
    return index > currentIndex && !node.paragraphNum
  })

  // 分割三段
  const beforeNodes = props.treeData.slice(0, currentIndex)
  const currentAndChildrenNodes = props.treeData.slice(currentIndex, nextChapterIndex === -1 ? undefined : nextChapterIndex)
  const afterNodes = nextChapterIndex === -1 ? [] : props.treeData.slice(nextChapterIndex)


  const beforeChapterNum = beforeNodes[beforeNodes.length - 1]?.chapterNum
  let beforeLastParagraphNum: number = beforeNodes[beforeNodes.length - 1]?.paragraphNum ?? 0
  // 降级当前节点
  const downgradedNode = {
    ...item,
    chapterNum: beforeChapterNum,
    paragraphNum: ++beforeLastParagraphNum,
    type: 'paragraph',
    label: ''
  }

  // 更新子节点的章节号和段落号
  const updatedChildrenNodes = currentAndChildrenNodes.slice(1).map(node => ({
    ...node,
    chapterNum: beforeChapterNum,
    paragraphNum: ++beforeLastParagraphNum
  }))

  // 更新后续章节的章节号
  const updatedAfterNodes = afterNodes.map(node => ({
    ...node,
    chapterNum: node.chapterNum - 1,
    paragraphNum: node.paragraphNum ? node.paragraphNum : undefined
  }))

  // 合并所有节点
  const updatedOutline = [
    ...beforeNodes,
    downgradedNode,
    ...updatedChildrenNodes,
    ...updatedAfterNodes
  ]

  outlineStore.updateOutline(updatedOutline, props.isTitle)
}

const handleAddChapter = (item: TreeNodes, currentIndex: number) => {
  // 找到当前章节的索引
  const currentChapterIndex = props.treeData.findIndex((node, index) => {
    return index === currentIndex && !node.paragraphNum
  })

  // 分割三段
  const beforeNodes = props.treeData.slice(0, currentChapterIndex)
  const currentChapterAndParagraphs = props.treeData.slice(currentChapterIndex, currentIndex + 1)
  const afterNodes = props.treeData.slice(currentIndex + 1)

  const currentChapterNum = item.chapterNum

  // 新增章节
  const newChapter = {
    id: generateId(), // 生成一个唯一的ID
    title: '新增章节',
    chapterNum: currentChapterNum,
    paragraphNum: undefined,
    type: 'chapter',
    label: '章节'
  }

  // 更新当前和后续节点的章节号和段落号
  const updatedCurrentAndAfterNodes = [...currentChapterAndParagraphs, ...afterNodes].map(node => {
    if (node.chapterNum >= currentChapterNum) {
      return {
        ...node,
        chapterNum: node.chapterNum + 1,
        paragraphNum: node.paragraphNum ? node.paragraphNum : undefined
      }
    }
    return node
  })

  // 合并所有节点
  const updatedOutline = [
    ...beforeNodes,
    newChapter,
    ...updatedCurrentAndAfterNodes
  ]

  outlineStore.updateOutline(updatedOutline, props.isTitle)
}


const handleDelete = (item: TreeNodes, index: number) => {
  const isChapter = item.type === 'chapter';
  const currentChapterNum = item.chapterNum;

  // 删除节点并删除章节下的所有段落
  const updatedTreeData = props.treeData.filter((node, i) => {
    if (isChapter) {
      return node.chapterNum !== currentChapterNum;
    }
    return i !== index;
  });

  // 更新后续节点的章节号和段落号
  const updatedAfterNodes = updatedTreeData.map(node => {
    if (isChapter && node.chapterNum > currentChapterNum) {
      return {
        ...node,
        chapterNum: node.chapterNum - 1,
        paragraphNum: node.paragraphNum ? node.paragraphNum : undefined
      }
    } else if (!isChapter &&
      node.chapterNum === currentChapterNum &&
      typeof node.paragraphNum === 'number' &&
      typeof item.paragraphNum === 'number' &&
      node.paragraphNum > item.paragraphNum) {
      return {
        ...node,
        paragraphNum: node.paragraphNum - 1
      }
    }
    return node
  });

  outlineStore.updateOutline(updatedAfterNodes, props.isTitle);
}


const handleAddSubset = (item: TreeNodes, index: number) => {
  const newSubset = {
    id: generateId(),
    title: '新增段落',
    chapterNum: item.chapterNum,
    paragraphNum: item.paragraphNum ? item.paragraphNum + 1 : 1,
    type: 'paragraph',
    label: ''
  }

  // Find the last paragraph in the current chapter
  const lastParagraphIndex = props.treeData.reduce((lastIndex, node, i) => {
    if (node.chapterNum === item.chapterNum && typeof node.paragraphNum === 'number') {
      return i;
    }
    return lastIndex;
  }, index);

  const updatedTreeData = [
    ...props.treeData.slice(0, lastParagraphIndex + 1),
    newSubset,
    ...props.treeData.slice(lastParagraphIndex + 1).map(node => {
      if (node.chapterNum === item.chapterNum &&
        typeof node.paragraphNum === 'number' &&
        typeof item.paragraphNum === 'number' &&
        node.paragraphNum > item.paragraphNum) {
        return {
          ...node,
          paragraphNum: node.paragraphNum + 1
        }
      }
      return node
    })
  ]

  outlineStore.updateOutline(updatedTreeData, props.isTitle)
}

</script>

<style scoped>
.tree {
  --spacing: 1.5rem;
  --radius: 3px;

}

.tree:first-child {
  width: 90%;
  margin-left: 10%;
}

.tree.is-title {
  width: 90%;
  margin-left: 10%;
}

.tree li {
  display: block;
  position: relative;
  padding-left: calc(2 * var(--spacing) - var(--radius) - 2px);
}

.tree li.is-chapter {
  padding-left: calc(var(--spacing) - var(--radius) - 2px);
}

.tree ul {
  margin-left: calc(var(--radius) - var(--spacing));
  padding-left: 0;
}


.tree li:first-child::before {
  border-left: transparent;
}

.tree li.is-chapter::before {
  border-bottom: transparent;
}

.tree li.is-title:last-child::before {
  /* border-color: transparent; */
  border-bottom: transparent;
}

.tree li::before {
  content: '';
  display: block;
  position: absolute;
  top: calc(var(--spacing) / -2);
  left: -1px;
  width: calc(var(--spacing) + 2px);
  height: calc(var(--spacing) + 1px + 0.5rem);
  border: solid #E0E0E0;
  border-width: 0 0 1px 1px;
}

.tree li:last-child::before {
  height: calc(var(--spacing) + 1px + 0.5rem);
}

.tree summary {
  display: block;
  height: 38px;
  line-height: 38px;
}

.tree summary::marker,
.tree summary::-webkit-details-marker {
  display: none;
}

.tree summary:focus {
  outline: none;
}

.tree summary:focus-visible {
  outline: 1px dotted #000;
}

.tree li::after {
  content: '';
  display: block;
  position: absolute;
  top: calc(var(--spacing) / 2 - var(--radius));
  left: calc(var(--spacing) - var(--radius) - 1px);
  width: calc(2 * var(--radius));
  height: calc(2 * var(--radius));
  border-radius: 50%;
  background: #1B6FFF;
  margin-top: 0.5rem;
}

.tree li.is-chapter::after {
  left: calc(var(--spacing) - var(--radius) - 24px);
}
/*
.tree summary::before {
  content: '';
  z-index: 1;
  background: #fff;
  outline: 1px solid #1B6FFF;
  color: #fff;
  line-height: calc(2 * var(--radius) - 2px);
  text-align: center;
} */

.tree details[open]>summary::before {
  content: '';
}

.tree-item {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tree-item.editing {
  /* background-color: #f5f5f5; */
}

.hovered {
  background-color: #f5f5f5;
}

.edit-input {
  width: 100%;
  border: none;
  background: transparent;
  font-size: inherit;
  font-weight: inherit;
  padding: 0;
  margin: 0;
  outline: none;
}

.confirm-btn {
  position: absolute;
  right: 5px;
  top: 50%;
  transform: translateY(-50%);
  background: #E2EDFF;
  width: 42px;
  height: 22px;
  border-radius: 4px;
  color: #1B6FFF;
  text-align: center;
  line-height: 22px;
  font-size: 14px !important;
  font-weight: 400 !important;
}

.editing .confirm-btn {
  display: none;
}
</style>
