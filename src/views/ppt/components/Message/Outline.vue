<template>

    <div class="outline-container">
        <div class="outline-tree-header-container HideScrollbar">
            <TreeNode :tree-data="outlineTitleAndSubTitle" :is-title="true" />
        </div>
        <div class="outline-tree-container HideScrollbar">
            <TreeNode :tree-data="outline" />
        </div>
    </div>
</template>

<script lang="ts" setup>
import { storeToRefs } from 'pinia';
import TreeNode from './TreeNode.vue';
import { useOutlineStore } from '@/store/modules/outline'
const outlineStore = useOutlineStore()
const { outlineTitleAndSubTitle, outline } = storeToRefs(outlineStore)


</script>

<style scoped>
.outline-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    gap: 10px;
}


.outline-tree-header-container {
    border: 1px solid #B2B9D5;
    border-radius: 4px;
    padding: 10px;
    height: 100px;
    overflow-y: scroll;
}

.outline-tree-container {
    border: 1px solid #B2B9D5;
    border-radius: 4px;
    padding: 10px;
    overflow-y: scroll;
    flex: 1;
}
</style>
