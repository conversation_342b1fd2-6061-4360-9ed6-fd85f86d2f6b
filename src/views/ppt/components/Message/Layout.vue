<template>
  <div class="flex items-start justify-center w-full HideScrollbar">
    <!-- <div className="w-[520px]  mx-auto fixed left-[50px] sm:left-[20px] top-[80px] bg-bgInput rounded-[32px] ">
      <n-button type="default" round :bordered="false" @click="onBack">
        <template #icon>
          <i class="fi fi-rr-arrow-small-left text-white"></i>
        </template>
        <span class="text-white">返回</span>
      </n-button>
    </div> -->
    <div class="w-[689px] h-[calc(100vh-60px-64px)] max-h-[830px] sm:w-[375px] bg-white rounded-[10px] shadow-box relative px-5 py-5 shadow shadow-shadow1 overflow-hidden flex flex-col" style="height: calc(100vh - 60px -64px)">
      <div class="mb-[12px] flex-grow-0 flex-shrink-0">
        <NButton type="primary" ghost style="--n-border-radius: 6px" size="small" @click="onBack">
          <template #icon>
            <IconArrowLeft />
          </template>
          <span>返回</span>
        </NButton>
      </div>
      <div v-if="isloading" class="flex justify-center items-center flex-1">
        <n-image :src="loadingIcon" width="100" height="100" class="absolute left-1/2 top-1/2 -ml-[50px] -mt-[50px]" />
      </div>
      <div v-else class="h-[calc(100vh-60%)] flex-1 HideScrollbar overflow-auto" id="scrollRef">
        <slot></slot>
      </div>
      <div class="flex justify-between text-center bg-[#fff] py-3 px-5 flex-shrink-0 flex-grow-0 gap-x-[10px]">
        <n-button type="primary" :disabled="loading" class="flex-1" style="margin-left: 10px; --n-border-focus: transparent" @click="onCreate"> 生成PPT </n-button>
        <n-button type="primary" ghost class="mr-3 flex-1" :disabled="loading" @click="onRegenerate">
          <template #icon>
            <i class="fi fi-rr-rotate-right text-[14px]"></i>
          </template>
          换个大纲
        </n-button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { NButton, NImage } from 'naive-ui'
import loadingIcon from '@/assets/images/loading.gif'

interface IProps {
  loading: boolean
  isloading: boolean
}
interface Emit {
  (ev: 'regenerate'): void
  (ev: 'create'): void
  (ev: 'back'): void
}
const emit = defineEmits<Emit>()
defineProps<IProps>()

const onRegenerate = () => {
  emit('regenerate')
}

const onCreate = () => {
  emit('create')
}

const onBack = () => {
  emit('back')
}
</script>

<style scoped></style>
