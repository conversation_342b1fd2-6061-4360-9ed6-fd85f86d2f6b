<script lang="ts" setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useOutlineStore, usePptChatStore } from '@/store'
import { useScroll } from '@/hooks/useScroll'
import { fetchChatAPIProcess, fetchAddPPT, fetchPPTGenerate } from './api'
import { useRoute, useRouter } from 'vue-router'
import Layout from './components/Message/Layout.vue'
import Message from './components/Message/index.vue'
import Tree from './components/Message/Tree.vue'
import Loading from './components/Message/Loading.vue'
import Template from './components/Template/index.vue'
import FullscreenSpin from '@/components/FullscreenSpin.vue'
import { markdownToJson } from '@/utils/markdown/md2json'
import { SelfRequestCode } from '@/utils/request'
import { useMessage } from 'naive-ui'
import { caseList } from './mocks/case'
import { OutlineNode, TreeData, TreeNodes } from './types'
import { useRequest } from 'vue-hooks-plus'
import { BaseInfo } from './types'
import Outline from './components/Message/Outline.vue'
import { storeToRefs } from 'pinia'
import { transformOutline } from '@/utils/utils'
import { getToken } from '@/store/modules/auth/helper'
const message = useMessage()
const { scrollToBottom, scrollToBottomIfAtBottom } = useScroll()
// @ts-ignore
const openLongReply = import.meta.env.VITE_GLOB_OPEN_LONG_REPLY === 'true'
// 数据是否加载完成
const loading = ref<boolean>(false)
const chatPptStore = usePptChatStore()
const outlineStore = useOutlineStore()
let controller = new AbortController()
const route = useRoute()
const router = useRouter()
const { initOutline } = outlineStore
const { outline, outlineTitleAndSubTitle, isChange } = storeToRefs(outlineStore)

let uuid: number
const step = ref<number>(2)
const isLoading = ref<boolean>(false)
const compute = ref<boolean>(false)
const selectTemp = ref<boolean>(false)
const dataSources = computed(() => chatPptStore.chat)
const { outlineId, m } = route.query
const outlineNodes = ref<TreeNodes[]>([])
const rawOutlineNodes = ref<OutlineNode[]>([])

const searchLoading = ref<boolean>(false)
onMounted(() => {
  if (!m) {
    generateChat(outlineId as string)
  } else {
    handleCase(Number(outlineId))
  }
})

const { run, loading: pptGenerateLoading } = useRequest<{ baseInfo: BaseInfo }>(fetchPPTGenerate, {
  manual: true,
  onSuccess: (data) => {
    // router.push({
    // 	name: 'PPTEditor',
    // 	params: {
    // 		id: data?.baseInfo?.id
    // 	}
    // })
    const { id } = data?.baseInfo
    if (id) {
      const protocol = window.location.protocol
      const host = window.location.host.includes('localhost') ? 'localhost:1026' : window.location.host.includes('chat-qa.mjmobi.com') ? 'ppt2-chat-qa.mjmobi.com' : 'ppt2.aiwork365.cn'
      // const aiTeamId = sessionStorage.getItem('ai-team')
      // http://localhost:1026/ppt/editor/567
			const token = getToken();
      window.location.href = `${protocol}//${host}/ppt/editor/${id}?token=${token}`
    }
  },
})

const onCreatePPTByTemplate = () => {
  selectTemp.value = true
}

function extractJSONStrings(text) {
  const results = []
  let depth = 0
  let inString = false
  let start = -1
  let escape = false

  for (let i = 0; i < text.length; i++) {
    const char = text[i]

    if (char === '"' && !escape) {
      inString = !inString
    }

    if (inString && char === '\\' && !escape) {
      escape = true
      continue
    }

    if (char === '{' && !inString) {
      if (depth === 0) {
        start = i
      }
      depth++
    }

    if (char === '}' && !inString) {
      depth--
      if (depth === 0) {
        const jsonString = text.slice(start, i + 1)
        results.push(jsonString)
      }
    }

    escape = false
  }

  return results
}

const generateChat = async (outlineId: string) => {
  if (loading.value) return
  controller = new AbortController()
  chatPptStore.recordChat()
  let lastText = ''

  uuid = Date.now()
  loading.value = true
  isLoading.value = true
  step.value = 2
  scrollToBottom()
  try {
    await fetchChatAPIProcess<any>({
      outlineId,
      signal: controller.signal,
      onDownloadProgress: ({ event }) => {
        const xhr = event.target
        isLoading.value = false
        const { responseText } = xhr
        // const chunks = responseText.split("\n");
        const chunks = extractJSONStrings(responseText)
        const chunk = chunks[chunks.length - 1]
        try {
          const data = JSON.parse(chunk)
          if (!data.text || !['startSearch', 'endSearch', 'endOfProcess', 'endOfSearch'].includes(data?.text)) {
            if (!data.text) {
              return
            }
            searchLoading.value = false
            const text = chunks
              .map((x) => JSON.parse(x).text)
              .filter((text) => text !== 'startSearch' && text !== 'endOfSearch' && text !== 'endOfProcess' && text !== 'outlineNodes')
              .join('')
            const isCompute = data?.detail?.choices?.[0]?.finish_reason
            console.log('isCompute: ', isCompute)

            if (isCompute == 'stop') {
              chatPptStore.updateChat(
                {
                  text: lastText + (data.text || ''),
                  inversion: false,
                  loading: false,
                  uuid,
                  isCompute: true,
                },
                uuid,
              )
              compute.value = true
            } else {
              chatPptStore.updateChat(
                {
                  text,
                  inversion: false,
                  loading: false,
                  uuid,
                  isCompute: false,
                },
                uuid,
              )
            }

            if (openLongReply && data?.detail?.choices?.[0]?.finish_reason === 'length') {
              lastText = data.text
            }

            if (data?.text === 'outlineNodes') {
              const { title, subTitle, nodes } = data?.detail
              initOutline({ title, subTitle }, nodes)
              rawOutlineNodes.value = nodes
              setTimeout(() => {
                compute.value = true
              }, 2000)
            }

            scrollToBottomIfAtBottom()
          } else {
            if (data?.text === 'startSearch') {
              searchLoading.value = true
              return
            } else if (data?.text === 'endSearch') {
              searchLoading.value = false
              return
            } else if (data?.text === 'endOfProcess') {
              return
            }
          }
        } catch (error) {
          console.error(error)
          isLoading.value = false
          loading.value = false
        }
      },
    })
  } catch (error: any) {
    // if (error.errcode == "10000001") {
    // 	// needPermission.value = true;
    // 	step.value = 1;
    // } else if (error.errcode == "************") {
    // 	// needVip.value = true;
    // 	step.value = 1;
    // } else {
    // 	// message.error(error?.errmsg);
    // }
    console.log(error)
    isLoading.value = false
    loading.value = false
    window.history.back()
  } finally {
    loading.value = false
  }
}

const onRegenerate = () => {
  compute.value = false
  if (m) {
    const current = caseList.find((item) => item.id === Number(outlineId))
    if (current) {
      chatTxt = ''
      step.value = 2
      uuid = Date.now()
      chatPptStore.recordChat()
      loading.value = true
      initOutline(
        {
          title: current.nodes.title,
          subTitle: current.nodes.subTitle,
        },
        current.nodes?.nodes,
      ) // TODO
      scrollToBottom()
      writing(current?.markdown, 0)
    }
  } else {
    generateChat(outlineId as string)
  }
}

// 创建ppt loading
const createLoading = ref<boolean>(false)

const onCreatePPT = async (templateId = 1) => {
  try {
    if (dataSources.value && dataSources.value.length > 0) {
      // const markdown = markdownToJson(dataSources.value[0]?.text)
      const markdown = markdownToJson(dataSources.value[0].text.replace(/```markdown/g, '').replace(/```/g, ''))
      createLoading.value = true
      const data: any = await fetchAddPPT({ title: prompt, sourceData: markdown[0], templateId })
      if (data) {
        const protocol = window.location.protocol
        const host = window.location.host.includes('chat-qa.mjmobi.com') ? 'ppt-chat-qa.mjmobi.com' : 'ppt.aiwork365.cn'
        // router.push(`/editor/${data.id}`)
        // window.open(`${host}/ppt/editor/${data.id}?token=${getToken()}`, '_blank')
        window.location.href = `${protocol}//${host}/ppt/editor/${data.id}?token=${Math.random()}`
        createLoading.value = false
        // window.location.href = `/ppt/editor/${data.id}`
      }
    }
  } catch (error: any) {
    console.log(error)
    // if (error.errcode == '10000001') {
    // 	// needPermission.value = true
    // } else if (error.errcode == '************') {
    // 	// needVip.value = true
    // } else if (error.errcode == '************' || error.message.includes("充值")) {
    // 	// window?.$aiwork?.openRecharge?.({ type: 'ppt' })
    // 	return
    // }
    createLoading.value = false
  }
}

let chatTxt = ''
let timer = null
const writing = (msg: any, index: number) => {
  const data = msg
  if (index < data.length) {
    chatTxt += data[index]
    chatPptStore.updateChat(
      {
        text: chatTxt,
        inversion: false,
        loading: true,
        uuid,
        isCompute: false,
      },
      uuid,
    )
    timer = setTimeout(writing, 8, msg, ++index)

    scrollToBottomIfAtBottom()
  }
  if (index === data.length) {
    loading.value = false
    clearTimeout(timer)
    compute.value = true
  }
}

const handleCase = (id: number) => {
  const current = caseList.find((item) => item.id === Number(id))
  if (current) {
    chatTxt = ''
    step.value = 2
    uuid = Date.now()
    chatPptStore.recordChat()
    loading.value = true
    initOutline(
      {
        title: current.nodes.title,
        subTitle: current.nodes.subTitle,
      },
      current.nodes?.nodes,
    ) // TODO
    scrollToBottom()
    writing(current?.markdown, 0)
  }
}

const handleSelect = async (templateId: string) => {
  selectTemp.value = false
  const nodes = transformOutline([outlineTitleAndSubTitle.value, outline.value])
  run({
    outlineId: outlineId,
    templateId: templateId,
    ...nodes,
    isChange: isChange.value,
  })
}
</script>

<template>
  <div class="from-[#F5F7FF] via-[rgba(236,241,255,0.8858)] to-[#E5F1FF] bg-gradient-to-b h-[calc(100vh-64px)] HideScrollbar">
    <div class="max-w-5xl mx-auto my-[20px]" v-if="step === 2">
      <div class="flex justify-center items-center" v-if="searchLoading">
        <div class="w-[689px]">
          <div class="pl-[17px] w-[306px] h-[50px] rounded-[108px] bg-[#ffffff] leading-[50px] text-[#0E69FF] text-[14px] flex items-center">
            <IconLoading class="w-[14px] h-[14px] text-[#0E69FF]" />
            <span class="ml-[10px]">AI创作中，请耐心等待...</span>
          </div>
        </div>
      </div>
      <Layout v-else :loading="loading" :isloading="isLoading" @create="onCreatePPTByTemplate" @regenerate="onRegenerate" @back="$router.back()">
        <Message v-if="!compute" v-for="(item, index) of dataSources" :key="index" :text="item.text" :index="index" :inversion="item.inversion" :error="item.error" :loading="item.loading" :is-compute="item.isCompute" />
        <!-- <Tree v-if="compute" :text="dataSources[0]?.text" /> -->
        <Outline v-if="compute" />
        <Loading :loading="createLoading" />
      </Layout>
    </div>
    <Template v-if="selectTemp" :id="outlineId" v-model:visible="selectTemp" @select="handleSelect" />
    <FullscreenSpin :loading="pptGenerateLoading" tip="正在初始化..." />
  </div>
</template>
