export interface ReChargeDetail {
  goodsType?: 'pro' | 'ultra' | 'package' | 'paper' | 'CHECK' | 'PaperRepeat' | 'AIGCCheck' | 'AIGCRepeat'
  type?: 'pro' | 'ultra' | 'package' | 'paper' | 'CHECK' | 'PaperRepeat' | 'AIGCCheck' | 'AIGCRepeat'
  categoryId?: 2 | 3 | 4 | 6
  memberType?: string
  paperId?: number
  taskId?: number
  id?: string
  goodsId?: number
  multiple?: boolean
  [key as string]?: any
}

export interface AiworkType {
  openActivity?: () => void
  openLogin: () => Promise<any>
  openRecharge: (detail: ReChargeDetail) => Promise<any>
  landingUrl?: string // 支付上报用
  openSelectTeam?: (afterLogin?: boolean) => void
}


