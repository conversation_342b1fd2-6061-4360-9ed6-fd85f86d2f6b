<script lang="ts" setup>
import { NButton, NImage, NSpin, useMessage, NIcon } from 'naive-ui'
import RechargeImg from '@/assets/recharge.png'
import { computed, onMounted, onUnmounted, ref, toRefs, watch } from 'vue'
import { useRequest } from 'vue-hooks-plus'
import QrcodeVue from 'qrcode.vue'
import { fetchIsPay, fetchWechatRechargeQrCode, fetchWechatH5Pay, fetchSubmitMemberOrder, fetchMemberOrderIsPay } from './apis'
import dayjs from 'dayjs'
import RefreshSvg from '@/assets/aiwork/svg/refresh.svg'
import { useCountDown } from '@/hooks/useCountDown'
import { router } from '@/router'
import { ss } from '@/utils/storage'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { useUserStore } from '@/store'
import { textTransform } from '@/utils/textTransform'
import { ScrollText } from '@/components/scrollText'
import NoticeSvg from '@/assets/aiwork/svg/notice.svg'
import { isNumber } from 'lodash'
const message = useMessage()
const { isWX, isMobileDevice } = useBasicLayout()
interface Props {
  paymentList: any[]
  paymentDetail?: any
  isTeam?: number
  type?: string
  taskId?: string
}
interface Emit {
  (e: 'payment-success'): void
  (e: 'emit-select-team'): void
}
const messages = ref<any>([
  {
    type: 'paper',
    messages: [
      '1分钟前 用户2720购买500算力，支付成功!',
      '2分钟前 用户4456购买1500算力，支付成功!',
      '3分钟前 用户1136购买500算力，支付成功!',
      '5分钟前 用户6152购买300算力，支付成功!',
      '7分钟前 用户4259购买1500算力，支付成功!',
      '9分钟前 用户0405购买300算力，支付成功!',
      '12分钟前 用户2156购买500算力，支付成功!',
      '13分钟前 用户0728购买1500算力，支付成功!',
      '15分钟前 用户6344购买500算力，支付成功!',
      '19分钟前 用户7266购买500算力，支付成功!',
      '21分钟前 用户1799购买300算力，支付成功!',
      '23分钟前 用户4123购买300算力，支付成功!',
      '24分钟前 用户5213购买500算力，支付成功!',
      '25分钟前 用户0596购买300算力，支付成功!',
      '28分钟前 用户3114购买300算力，支付成功!',
      '29分钟前 用户5487购买500算力，支付成功!',
      '31分钟前 用户8268购买300算力，支付成功!',
    ],
  },
  {
    type: 'office',
    messages: [
      '1分钟前  用户6566购买文字至尊会员，支付成功!',
      '2分钟前  用户5656购买PPT年度会员，支付成功!',
      '3分钟前  用户2721购买文字至尊会员，支付成功!',
      '4分钟前  用户7421购买办公助手年度会员，支付成功!',
      '5分钟前  用户1136购买PPT月度会员，支付成功!',
      '6分钟前  用户2321购买办公助手季度会员，支付成功!',
      '7分钟前  用户6152购买1500论文算力包，支付成功!',
      '8分钟前  用户4259购买文字豪华至尊会员，支付成功!',
      '9分钟前  用户0405购买绘图年度会员，支付成功!',
      '10分钟前  用户2156购买PPT季度会员，支付成功!',
      '11分钟前  用户0625购买4000论文算力包，支付成功!',
      '12分钟前  用户7602购买办公助手月度会员，支付成功!',
      '13分钟前  用户6367购买绘图季度会员，支付成功!',
      '15分钟前  用户6789购买文字豪华至尊会员，支付成功!',
      '16分钟前  用户2345购买PPT年度会员，支付成功!',
      '18分钟前  用户1236购买文字至尊会员，支付成功!',
      '19分钟前  用户7890购买PPT季度会员，支付成功!',
      '20分钟前  用户8970购买文字季度畅享会员，支付成功!',
      '22分钟前  用户3450购买PPT月度会员，支付成功!',
      '24分钟前  用户4560购买500论文算力包，支付成功!',
      '26分钟前  用户8278购买文字月度畅享会员，支付成功!',
    ],
  },
  {
    type: 'pro',
    messages: [
      '1分钟前 183****9864购买Ultra版年度会员，支付成功！',
      '2分钟前 183****0405购买Pro版年度会员，支付成功！',
      '3分钟前 151****4145购买Ultra版终身会员，支付成功！',
      '4分钟前 157****2156购买Ultra版年度会员，支付成功！',
      '5分钟前 183****9466购买10000积分加油包，支付成功！',
      '6分钟前 151****5583购买Ultra版终身会员，支付成功！',
      '8分钟前 132****5503购买Pro版年度会员，支付成功！',
      '10分钟前 132****1874购买Ultra版终身会员，支付成功！',
      '12分钟前 150****3234购买30000积分加油包，支付成功！',
      '14分钟前 133****8657购买Ultra版年度会员，支付成功！',
      '16分钟前 195****9983购买Ultra版终身会员，支付成功！',
      '18分钟前 181****0051购买Ultra版季度会员，支付成功！',
      '20分钟前 184****7793购买Ultra版月度会员，支付成功！',
      '24分钟前 185****1902购买Pro版年度会员，支付成功！',
      '26分钟前 195****9448购买30000积分加油包，支付成功！',
      '28分钟前 186****4405购买Pro版季度会员，支付成功！',
      '30分钟前 181****3151购买Ultra版终身会员，支付成功！',
    ],
  },
])
const props = withDefaults(defineProps<Props>(), {
  type: '',
})
const latestMessage = computed(() => {
  return messages.value.find((item: any) => item.type === props?.type)?.messages || messages.value[1].messages
})

const emit = defineEmits<Emit>()
const checkedPayment = ref(isMobileDevice.value && !isWX.value ? 2 : 1)
const countdown = ref<string | null>(null)
const countdownTimer = ref<any>(null)
const qrcodeAging = ref(false) //二维码失效
const store = localStorage.getItem('SECRET_TOKEN') as string
const userStore = useUserStore()
const aliPayDomain = ref<string>(window.location.origin)

const handleSwitchPayment = (item: { id: number }) => {
  checkedPayment.value = item.id
}
const countDown = useCountDown({
  // 倒计时 15分钟
  time: 15 * 60 * 1000,
  millisecond: true,
  autoStart: false,
})
const { current } = toRefs(countDown)

watch(
  () => props.paymentDetail,
  (v) => {
    refreshPayQrcode()
  },
  { deep: true },
)

const refreshPayQrcode = (force = false) => {
  if (props.type === 'office') {
    runSubmitMemberOrder({
      memberId: props.paymentDetail?.id,
      taskId: props?.taskId,
      payType: checkedPayment.value === 1 ? 'wxpay' : 'alipay',
    })
  } else {
    run({
      goodsId: props.paymentDetail?.id,
      paperId: props.paymentDetail?.paperInfo?.id ?? undefined,
    })
  }
}

const { run: runMemberCheckIsPay, cancel: cancelMemberCheckIsPay } = useRequest(fetchMemberOrderIsPay, {
  manual: true,
  pollingInterval: 2000,
  pollingWhenHidden: false,
  refreshDeps: [checkedPayment],
  onBefore: () => {
    qrcodeAging.value = false
  },
  onSuccess: (data: { isPay?: boolean }) => {
    if (data?.isPay) {
      // Office会员订单支付成功埋点
      window.rprm.rec({
        elementid: 'payment_success',
        payment_type: checkedPayment.value === 1 ? 'wechat' : 'alipay',
        payment_goods_id: props.paymentDetail?.id,
        payment_scene: props.type,
        payment_price: props.paymentDetail?.originalPrice,
        payment_task_id: props?.taskId,
      })

      emit('payment-success')
      cancelMemberCheckIsPay()
    }
  },
})

const {
  data: submitMemberOrderData,
  loading: submitMemberOrderLoading,
  run: runSubmitMemberOrder,
} = useRequest<any, any>(fetchSubmitMemberOrder, {
  manual: true,
  debounceWait: 300,
  onSuccess: (data) => {
    countDown.start()
    runMemberCheckIsPay({
      orderNo: data?.orderNo,
    })
  },
})

const { data, loading, run } = useRequest<any, any>(fetchWechatRechargeQrCode, {
  // ready: !!props?.paymentDetail?.id,
  manual: true,
  debounceWait: 300,
  onSuccess: (data) => {
    countDown.start()
    aliPayDomain.value = data.alipayUrl ? data.alipayUrl : window.location.origin
    runCheckIsPay()
  },
})

const { run: runCheckIsPay, cancel } = useRequest(fetchIsPay, {
  manual: true,
  pollingInterval: 2000,
  pollingWhenHidden: false,
  onBefore: () => {
    qrcodeAging.value = false
  },
  onSuccess: (data: { status?: boolean }) => {
    if (data?.status) {
      // 支付成功埋点
      window.rprm.rec({
        elementid: 'payment_success',
        payment_type: checkedPayment.value === 1 ? 'wechat' : 'alipay',
        payment_goods_id: props.paymentDetail?.id,
        payment_scene: props.type,
        payment_price: props.paymentDetail?.originalPrice,
        payment_task_id: props?.taskId,
      })

      emit('payment-success')
      cancel()
    }
  },
})

onUnmounted(() => {
  cancel()
  countdownTimer.value && clearInterval(countdownTimer.value)
})

const startCountdown = () => {
  if (countdownTimer.value) clearInterval(countdownTimer.value)
  let targetTime = dayjs().add(15, 'm')
  countdownTimer.value = setInterval(() => {
    const diff = targetTime.diff(dayjs())
    if (diff <= 0) {
      clearInterval(countdownTimer.value)
      qrcodeAging.value = true
    }
    const m = Math.floor(diff / 60000)
    const s = Math.floor((diff % 60000) / 1000)
    countdown.value = `00:${m < 10 ? '0' + m : m}:${s < 10 ? '0' + s : s}`
  }, 500)
}

const paymentListRender = computed(() => {
  return props?.paymentList.filter((item) => {
    if (!isMobileDevice.value) return true
    if (!isWX.value) return item.id !== 1
    if (isWX.value) return item.id === 1
    return false
  })
})

const qrCodeImage = computed(() => {
  if (props.type === 'office') {
    return submitMemberOrderData?.value?.url
  } else if (checkedPayment.value === 1) {
    return data.value?.url
  } else {
    const token = JSON.parse(store).data
    // 生成url
    // 规则是当前域名+/aiwork/alipay/prepay/:goodsId?paperId=1
    // paperId是可选的
    // 使用URLParams生成
    const url = new URL(
      `/api3/aiwork/alipay/prepay/${props?.paymentDetail?.id}?access_token=${token}&url=${encodeURIComponent(window.landingUrl || window.location.href)}`,
      // window.location.origin
      aliPayDomain.value,
    )
    if (props?.paymentDetail?.paperInfo?.id) {
      url.searchParams.set('paperId', props?.paymentDetail?.paperInfo?.id ?? '')
    }
    return url.href
  }
})
const payPrice = computed(() => {
  // 如果有paymentDetail.price 则用这个值，否则用originalPrice-discount
  if (isNumber(props?.paymentDetail?.price)) {
    return props?.paymentDetail?.price
  } else {
    return (Number(props?.paymentDetail?.originalPrice) - Number(props?.paymentDetail?.discount) || 0).toFixed(2)
  }
})
const gotoUserPaymentAgreement = () => {
  // router.push({ name: "UserPaymentAgreement" });
  window.open('https://aiwork365.cn/userpaymentagreement')
}
const gotoPrivacyPolicy = () => {
  window.open('https://aiwork365.cn/privacypolicy')
}
const gotoUserAgreement = () => {
  window.open('https://aiwork365.cn/useragreement')
}

const toH5Pay = () => {
  const paymentDetail = props?.paymentDetail
  let url
  if (isWX.value) {
    url = `${location.origin}/api3/aiwork/alipay/wxh5pay/${paymentDetail.id}?goodsId=${paymentDetail.id}&access_token=${ss.get('SECRET_TOKEN')}&url=${encodeURIComponent(window.landingUrl)}`
  } else {
    url = qrCodeImage.value
  }
  // window.location.replace(url)
  // window.location.href = url;
  // window.location.href = url;
  window.open(url)
}
</script>
<template>
  <div class="flex-1 flex flex-row gap-x-[24px] justify-start pt-[22px] pl-[20px] pb-[12px] bg-[#fff] rounded-bl-[7px] rounded-br-[7px] sm:p-3 sm:w-full sm:overflow-hidden sm:gap-x-3 sm:flex-col sm:items-start">
    <NSpin :show="loading || submitMemberOrderLoading" content-class="sm:flex sm:items-center sm:gap-4 sm:w-full">
      <div v-if="!isMobileDevice" class="p-[10px] bg-white relative border-[1px] border-[#959595] rounded-[7px]">
        <qrcode-vue :value="qrCodeImage" :size="154" level="H" />
        <div v-if="qrcodeAging" class="absolute top-0 left-0 w-full h-full flex flex-col justify-center items-center bg-[rgba(0,0,0,0.5)] text-white cursor-pointer">
          <RefreshSvg class="w-[40px] h-[40px]" @click="() => refreshPayQrcode()" />
          <span class="mt-[5px]">二维码已失效</span>
        </div>
        <div v-if="isTeam && !userStore.userInfo?.uid" class="absolute top-0 left-0 w-full h-full flex flex-col justify-center items-center bg-[rgba(0,0,0,0.6)] text-white cursor-pointer backdrop-blur-sm" @click="() => refreshPayQrcode(true)">
          <RefreshSvg class="w-[40px] h-[40px]" />
          <span class="mt-[5px]">请登录</span>
        </div>
      </div>
      <!-- 充值渠道 mobile -->
      <div class="sm:flex flex-col gap-y-[13px] pt-[6px] hidden">
        <div
          v-for="item in paymentListRender"
          class="border-[1px] rounded-[5px] w-[131px] h-[39px] flex flex-row justify-between items-center gap-x-[5px] cursor-pointer pl-[13px] pr-[12px]"
          @click="handleSwitchPayment(item)"
          :key="item.id"
          :class="item.id === checkedPayment ? 'border-[#20C300]' : 'border-[#C9D3DF]'"
        >
          <NImage :src="item.url" class="w-[20px] h-[20px]" preview-disabled />
          <span class="flex-1 text-[12px] leading-[17px] text-[#3D3D3D]">{{ item.name }}</span>
          <IconCheck v-if="item.id === checkedPayment" class="text-[#20C300] w-[14px] h-[14px] text-[14px] relative top-[-2px]"></IconCheck>
        </div>
      </div>
    </NSpin>
    <div class="flex flex-col flex-1">
      <div class="flex flex-row items-start gap-x-[16px] sm:flex-col">
        <!-- 充值渠道 -->
        <div class="flex flex-col gap-y-[13px] pt-[6px] sm:hidden">
          <div
            v-for="item in paymentList"
            class="border-[1px] rounded-[5px] w-[201px] h-[42px] flex flex-row justify-between items-center gap-x-[5px] cursor-pointer pl-[13px] pr-[12px]"
            @click="handleSwitchPayment(item)"
            :key="item.id"
            :class="item.id === checkedPayment ? 'border-[#20C300]' : 'border-[#C9D3DF]'"
          >
            <NImage :src="item.url" class="w-[20px] h-[20px]" preview-disabled />
            <span class="flex-1 text-[12px] leading-[17px] text-[#3D3D3D]">{{ item.name }}</span>
            <IconCheck v-if="item.id === checkedPayment" class="text-[#20C300] w-[14px] h-[14px] text-[14px] relative top-[-2px]"></IconCheck>
          </div>
        </div>
        <!-- 应付金额 -->
        <div class="flex-1 h-full flex flex-col justify-between items-start sm:flex-wrap sm:mt-3">
          <div class="flex flex-row items-center">
            <span class="text-[16px] leading-[18px] text-[#3d3d3d]">应付金额:</span>
            <span class="flex flex-row items-end">
              <span class="text-[16px] text-[#3D3D3D] pb-[10px]">￥</span>
              <span class="text-[34px] font-bold text-[#3D3D3D] leading-[42px] pb-[7px]">{{ payPrice }}</span>
            </span>

            <span v-if="Number(paymentDetail?.discount) > 0" class="bg-[#FFEFE6] text-[12px] leading-[23px] text-[#FF0000] text-center px-[13px] ml-[9px] rounded-[4px]">已优惠￥{{ paymentDetail?.discount }}</span>
          </div>
          <div v-if="isMobileDevice" class="hidden sm:block ipad:block">
            <NButton type="primary" class="!w-[200px]" size="large" @click="toH5Pay">去支付</NButton>
          </div>
          <div v-if="current.total > 0" class="discount inline-flex items-center bg-[#FFF1EB] rounded-[4px] h-[36px] text-[12px] text-[#FF5B03] leading-[36px] px-[17px] mt-[7px]">
            <NImage src="https://cdn2.weimob.com/saas/saas-fe-sirius-orion-node/production/zh-CN/503/quan.png" class="w-[14px] h-[12px] inline-block" preview-disabled />
            <span class="ml-[10px] mr-[10px] w-1 border-l-[1px] border-[#FF5B03] h-[12px] inline-block"></span>
            限时优惠
            <span class="inline-flex w-[20px] h-[20px] items-center justify-center bg-[#FF8A4C] text-[#fff] rounded-[2px] ml-[4px] mr-[4px]">{{ current.hours }}</span
            >: <span class="inline-flex w-[20px] h-[20px] items-center justify-center bg-[#FF8A4C] text-[#fff] rounded-[2px] ml-[4px] mr-[4px]">{{ current.minutes }}</span
            >:
            <span class="inline-flex w-[20px] h-[20px] items-center justify-center bg-[#FF8A4C] text-[#fff] rounded-[2px] ml-[4px] mr-[4px]">{{ current.seconds }}</span>
            后失效
          </div>
        </div>
      </div>
      <div class="w-[calc(100%-22px)] h-[28px] bg-[#F1F7FF] rounded-[4px] leading-[28px] mt-[16px] font">
        <ScrollText v-if="latestMessage.length > 0" :current="2" :autoplay="true" :interval="2000" :delayUpdate="1500" :height="28">
          <div v-for="(message, index) in latestMessage" class="flex items-center justify-start px-[8px] h-[28px]">
            <NIcon :size="16" class="mr-[5px]">
              <NoticeSvg />
            </NIcon>
            <span class="text-gray-500 text-[14px]">{{ textTransform(message) }}</span>
          </div>
        </ScrollText>
      </div>
      <!-- 协议认同 -->
      <div class="flex flex-row text-[14px] leading-[18px] pt-[14px]">
        <span class="text-[#999999]">支付默认同意</span>
        <span class="text-[#3680F9] cursor-pointer" @click="gotoUserPaymentAgreement">《用户支付协议》</span>
        <span class="text-[#999999]">、</span>
        <span class="text-[#3680F9] cursor-pointer" @click="gotoUserAgreement">《用户协议》 </span><span class="text-[#999999]">及</span><span class="text-[#3680F9] cursor-pointer" @click="gotoPrivacyPolicy">《隐私条款》</span>
      </div>
    </div>
  </div>
</template>
<style lang="less" scoped></style>
