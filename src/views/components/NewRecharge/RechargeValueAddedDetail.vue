<script setup lang="ts">
import { NImage, NIcon } from 'naive-ui'
import RobotImg from '@/assets/images/robot.gif'
import CheckedImg from '@/assets/images/checked.png'
import { replaceText } from '@/plugins/directive'
import { computed } from 'vue'
import { Right as IconRight } from '@icon-park/vue-next'

interface Props {
  showRobot: boolean
  goodsType: string
  selectedGood: any
}
const props = defineProps<Props>()

const ValueAddedTitle = computed(() => {
  return props?.selectedGood?.mark || ''
})

const ValueAddedList = computed(() => {
  const desc = props?.selectedGood.allDesc || ''
  return desc.split(/<br>|\n/).filter((item) => item.trim())
})
const openDetail = () => {
  window.open(`${location.origin}/profile/viplevels`)
}
</script>

<template>
  <div class="flex flex-col w-[full] h-[100%] px-[15px] py-0">
    <!-- Hi~我是你的智能办公助 手，一站式办公神器 -->
    <div class="robot-container">
      <NImage src="https://cdn2.weimob.com/saas/saas-fe-sirius-orion-node/production/zh-CN/503/vipbanner.png" class="w-[195px] h-[82px]" preview-disabled />
    </div>
    <!-- <div
			class="text-[16px] font-bold text-[#3D3D3D] leading-[21px] mt-[30px] mb-[19px] text-center"
		>
			{{ replaceText(ValueAddedTitle) }}
		</div> -->
    <div class="mt-[44px] pl-[10px] flex flex-col gap-y-[14px]">
      <div v-for="(item, index) in ValueAddedList" :key="item + index" class="flex flex-row items-start gap-x-[8px]">
        <div class="h-[full] pt-[3px] flex items-start">
          <NImage :src="CheckedImg" class="w-[10px] h-[10px]" preview-disabled />
        </div>
        <span class="flex-1 text-[#3D3D3D] text-[12px] leading-[16px]">{{ replaceText(item) }}</span>
      </div>
    </div>
    <template v-if="['pro', 'ultra', 'package'].includes(props.goodsType)">
      <div class="text-[#443BFF] pl-[10px] mt-[40px] cursor-pointer" @click="openDetail()">
        了解更多权益明细
        <NIcon size="10"><IconRight theme="outline" /></NIcon>
      </div>
      <div class="mt-[22px]">
        <NImage src="https://cdn2.weimob.com/saas/saas-fe-sirius-orion-node/production/zh-CN/503/iocnlogo.png" class="w-[169px] h-[31px]" preview-disabled />
      </div>
    </template>
  </div>
</template>

<style lang="less" scoped>
.robot-container {
}
.divider {
  width: 100%;
  height: 1px;
  background-color: #ccdee7;
  margin-top: 31px;
}
</style>
