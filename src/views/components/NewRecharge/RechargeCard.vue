<script setup lang="ts">
import { computed } from "vue";
import { NEllipsis, NImage } from "naive-ui";

interface Props {
	id: number;
	name: string;
	originalPrice: string;
	price?: number;
	discount: string;
	description: null;
	discountDesc: null;
	alias: string;
	selected: boolean;
	tag?: string;
	giftDesc?: string;
	markUrl?: string;
	mark?: string;
	allDesc?: string;
}
interface Emit {
	(e: "update-select-good", item: Props): void;
}
const props = defineProps<Props>();
const emit = defineEmits<Emit>();
const discountDescList = props.discountDesc
	? (props.discountDesc as string)
			.split(/<br>|\n/)
			.filter((item) => item.trim())
	: [];
const handleClick = () => {
	emit("update-select-good", props);
};
const price = computed(() => {
	// 如果有price则用这个值，否则用originalPrice-discount
	if (props?.price !== undefined) {
		return props.price;
	} else {
		return (
			Number(props?.originalPrice) - Number(props?.discount) || 0
		).toFixed(2);
	}
});
</script>

<template>
	<div
		class="recharge-card-container recharge-card"
		:class="selected ? 'recharge-card-selected ' : ''"
		@click="handleClick"
	>
		<div
			v-if="markUrl"
			class="absolute top-[0px] right-[0] z-[3]"
			style="transform: scale(0.5); transform-origin: top right"
		>
			<NImage :src="markUrl" preview-disabled />
		</div>
		<div class="flex flex-col items-center flex-1">
			<span class="text-[#ff7703] text-[16px] leading-[21px] mb-[4px]">{{
				name
			}}</span>
			<div>
				<span class="text-[13px] text-[#5E5E5E] line-through"
					>￥{{ Number(originalPrice) }}</span
				>
				<span class="text-[13px] text-[#5E5E5E] pl-[4px]"
					>{{ description }}</span
				>
			</div>
			<div
				class="flex flex-row justify-center items-baseline mt-[2px] mb-[4px]"
			>
				<span class="text-[12px] text-[#292929]">￥</span>
				<span class="text-[38px] text-[#292929] font-bold leading-37px">{{
					price
				}}</span>
			</div>
			<div
				class="text-[12px] text-[#5E5E5E] leading-[16px] flex flex-col gap-[8px] text-center"
			>
				<span v-for="item in discountDescList" :key="item">{{ item }}</span>
			</div>
			<NImage
				v-if="giftDesc && giftDesc.length > 0 && giftDesc?.startsWith('http')"
				class="absolute left-0 bottom-0 w-full leading-[33px] bg-[#F7F1FF] text-[#883DFF] text-center text-[12px] rounded-br-[10px] rounded-bl-[10px]"
				:src="giftDesc"
				preview-disabled
			/>
		</div>
	</div>
</template>

<style lang="less">
.recharge-card-container {
	&:extend(.recharge-card);
	width: 100%;
	border-radius: 14px;
	cursor: pointer;
	border-radius: 10px;
	border: 1px solid #d8dde5;
	position: relative;
	display: flex;
	flex-direction: column;
	background-color: #fff;

	.n-card__content {
		padding: unset !important;
	}
}

.recharge-card {
	min-width: 180px;
	max-width: 240px;
	// height: 100%;
	height: 212px;
	// border: 2px solid transparent !important;
	transition: height 0.3s ease;
	padding: 28px 14px 0 14px;
}

.recharge-fat-card-container.recharge-fat-card-container {
	// width: calc(30% - 12.5px);
	width: 183px;
	max-width: 183px;
	height: 100%;
	flex-shrink: 0;
}

.recharge-card-selected {
	border-radius: 12px;
	border-image-slice: 1;
	transition: height 0.3s ease;
	background: linear-gradient(180deg, #f4faff 0%, #ffffff 65%);

	&::after {
		content: "";
		position: absolute;
		left: 0;
		bottom: 0;
		width: 100%;
		height: 100%;
		border-radius: 10px;
		// border: 2px solid #8f41e9 !important;
		// border-image: linear-gradient(90deg, #FB2189 0%, #B55DFD 100%) 1;
		// border-image-slice: 1;
		z-index: 2;
		// box-shadow: 0px 4.95px 18.55px 0px rgba(191, 204, 217, 0.3);
		padding: 2px;
		/* 边框宽度 */
		background: linear-gradient(90deg, #fb2189 0%, #b55dfd 100%);
		-webkit-mask: linear-gradient(#fff 0 0) content-box,
			linear-gradient(#fff 0 0);
		-webkit-mask-composite: xor;
		mask-composite: exclude;
		pointer-events: none;
	}
}
</style>
