import { useUserStore } from "@/store";
import { post } from "@/utils/request";
import { CheckRechargeProps } from "../types";

/**
 * 获取会员列表
 * @returns
 */
export function fetchMemberList<T>(params?: {
	memberType?:
		| "DOC_MEMBER"
		| "DOC_TRANS_MEMBER"
		| "DOC_DECODE_MEMBER"
		| "DOC_DECODE_MEMBER_2";
}) {
	return post<T>({
		url: "/api3/adtool/member/getMemberList",
		data: params,
	});
}

export function fetchPaperCheckDetail<T>(params: { id: string }) {
	return post<T>({
		url: "/api3/aiwork/order/paperCheckDetail",
		data: params,
	});
}

// 创建aigc订单
export function createAigcCheckOrder<T>(params: {
	type: string;
	file?: File;
	content?: string;
	title: string;
	author: string;
	submitType: "paper" | "report";
	action: 'aigc' | 'aigcAmend'
	id?: string;
}) {
	const formData = new FormData();
	if (params.id) {
		formData.append("id", params.id);
	}
	formData.append("type", params.type);
	formData.append("title", params.title);
	formData.append("author", params.author);
	formData.append("submitType", params.submitType);
	formData.append("action", params.action)
	if (params.content) {
		formData.append("content", params.content);
	} else if (params.file) {
		formData.append("file", params.file);
	}

	return post<T>({
		url: "/api3/aiwork/order/paperAigc",
		data: formData,
		headers: { "Content-Type": "multipart/form-data" },
	});
}



// 创建降重订单
export function createPaperCheckOrder<T>(params: {
	type: string;
	file?: File;
	content?: string;
	title: string;
	author: string;
	submitType: "paper" | "report";
	id?: string;
}) {
	const formData = new FormData();
	if (params.id) {
		formData.append("id", params.id);
	}
	formData.append("type", params.type);
	formData.append("title", params.title);
	formData.append("author", params.author);
	formData.append("submitType", params.submitType);
	if (params.content) {
		formData.append("content", params.content);
	} else if (params.file) {
		formData.append("file", params.file);
	}

	return post<T>({
		url: "/api3/aiwork/order/paperRepeat",
		data: formData,
		headers: { "Content-Type": "multipart/form-data" },
	});
}

export function fetchCheckRecharge<T>(params: CheckRechargeProps) {
	const formData = new FormData();
	formData.append("type", params.type);
	formData.append("title", params.title);
	formData.append("author", params.author);

	if (params.content) {
		formData.append("content", params.content);
	} else if (params.file) {
		formData.append("file", params.file?.file);
	}
	return post<T>({
		url: "/api3/aiwork/order/createPaperCheck",
		data: formData,
		headers: { "Content-Type": "multipart/form-data" },
	});
}

/**
 * 获取充值模板
 * @returns
 */
export function fetchRechargeTemplate<T>(params: {
	source?: string;
	goodsType: string;
	paperId?: number;
	isTeam?: number;
}) {
	return post<T>({
		url: "/api3/aiwork/goods/list",
		data: params,
	});
}
/**
 * 获取微信充值QR
 * @returns
 */
export function fetchWechatRechargeQrCode<T>(params: {
	goodsId?: string;
	paperId?: number;
}) {
	return post<T>({
		url: "/api3/aiwork/alipay/prepay/" + params.goodsId,
		data: {
			url: window.landingUrl || "",
			teamId: useUserStore().curTeam?.id || "",
			...params,
		},
	});
}
/**
 * 查询是否充值
 * @returns
 */
export function fetchIsPay<T>() {
	return post<T>({
		url: "/api3/aiwork/pay/ispay",
	});
}

/**
 * 微信H5支付
 * @returns
 */
export function fetchWechatH5Pay<T>(params: {
	goodsId?: string;
	url?: string;
	token?: string;
}) {
	return post<T>({
		url: "/api3/aiwork/alipay/wxh5pay/" + params.goodsId,
		method: "GET",
		data: {
			url: window.landingUrl || "",
			teamId: useUserStore().curTeam?.id || "",
			...params,
		},
	});
}

/**
 * 提交Office会员订单
 * @returns
 */
export function fetchSubmitMemberOrder<T>(params: {
	memberId: string;
	taskId?: string;
	payType: "wxpay" | "alipay";
}) {
	return post<T>({
		url: "/api3/adtool/member/submitOrder",
		data: {
			teamId: useUserStore().curTeam?.id || "",
			...params,
		},
	});
}

/**
 * 查询Office会员订单支付状态
 * @returns
 */
export function fetchMemberOrderIsPay<T>(params: { orderNo: string }) {
	return post<T>({
		url: "/api3/adtool/member/isPay",
		data: params,
	});
}
