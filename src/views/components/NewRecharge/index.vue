<script lang="ts" setup>
import { ref, watch, nextTick } from 'vue'
import { NModal, useMessage, NImage, NSpin } from 'naive-ui'
import RechargeCardList from './RechargeCardList.vue'
import PaymentDetail from './PaymentDetail.vue'
import { useRequest } from 'vue-hooks-plus'
import { createAigcCheckOrder, createPaperCheckOrder, fetchCheckRecharge, fetchPaperCheckDetail, fetchRechargeTemplate, fetchMemberList } from './apis'
import Vip from './imgs/vip.png'

import { AiworkType, ReChargeDetail } from './types'
import RechargeValueAddedDetail from './RechargeValueAddedDetail.vue'
import { useUserStore } from '@/store'
import RechargeHeaderUser from './RechargeHeaderUser.vue'
import PaperRecharge from './PaperRecharge.vue'
import PaperPremiumRecharge from './PaperPremiumRecharge.vue'
import OfficeRecharge from './OfficeRecharge.vue'

const tabList = ref([
  {
    type: 'pro',
    name: 'Pro版会员',
  },
  {
    type: 'ultra',
    name: 'Ultra版会员',
  },
  {
    type: 'package',
    name: '积分加油包',
  },
])

const defaultPayParam: ReChargeDetail = { type: 'pro' }
const rechargeList = ref<any[] | any>([])
const paperComputilityList = ref<any[]>([])
const selectedGood = ref<any>({})
const checkedPayment = ref(1)
const activeKey = ref('pro')
const payParam = ref<ReChargeDetail>(defaultPayParam)
const show = ref(false)
const paymentList = ref([
  {
    id: 1,
    name: '微信支付',
    url: 'https://cdn2.weimob.com/static/aiwork365-web-stc/assets/wxpay.png',
  },
  {
    id: 2,
    name: '支付宝支付',
    url: 'https://cdn2.weimob.com/static/aiwork365-web-stc/assets/alipay.png',
  },
])
const message = useMessage()
const userStore = useUserStore()
const promiseResolvers = ref<any>()
const prevTeamId = ref(userStore.curTeam?.id || null)

;(window.$aiwork as AiworkType).openRecharge = async (detail: ReChargeDetail) => {
  console.log('>>>detail', detail)

  const category = {
    5: 'CHECK', // 查重
    7: 'PaperRepeat', // 降重
    8: 'AIGCCheck',
    9: 'AIGCRepeat',
    paper: 'paper',
    pro: 'pro',
    ultra: 'ultra',
    package: 'package',
    office: 'office',
  }
  if (detail.categoryId && category[detail.categoryId]) {
    activeKey.value = category[detail.categoryId]
    detail.goodsType = detail.type
  } else {
    detail.goodsType = category[detail.type as string] || 'pro'
    activeKey.value = detail.goodsType as string
  }

  promiseResolvers.value = Promise.withResolvers()

  const showPayment = () => {
    show.value = true
    payParam.value = detail
    openPayment(detail)
  }

  if (!userStore.userInfo?.uid) {
    ;(window.$aiwork as AiworkType)
      .openLogin?.()
      .then(() => {
        showPayment()
      })
      .catch(() => {
        handleClose()
        // message.error('登录后使用')
      })
  } else {
    showPayment()
  }

  return promiseResolvers.value.promise
}

watch(
  () => show.value,
  (value) => {
    if (!value) return
    prevTeamId.value = userStore.curTeam?.id || null
    window.rprm.rec({
      elementid: 'recharge_start',
      payment_scene: activeKey.value,
      payment_paper_id: payParam.value?.paperId,
      payment_category_id: payParam.value?.categoryId,
    })
  },
)

const handleClose = () => {
  show.value = false
  activeKey.value = 'pro'
  payParam.value = defaultPayParam
  promiseResolvers.value?.reject({ type: 'cancel', message: '取消支付' })
  promiseResolvers.value = null
  selectedGood.value = {}
  paperComputilityList.value = []

  nextTick(() => {
    rechargeList.value = []
    selectedGood.value = {}
    activeKey.value = 'pro'
  })
}

interface RechargeProps {
  id: number
  name: string
  originalPrice: string
  discount: string
  description: null
  discountDesc: null
  alias: string
  selected: boolean
  tag?: string
}
const {
  run: runFetchRechargeTemplate,
  loading,
  refresh: refreshFetchRechargeTemplate,
} = useRequest(
  (data: ReChargeDetail) => {
    return fetchRechargeTemplate({ ...data })
  },
  {
    manual: true,
    debounceWait: 100,
    onSuccess: (res: any, params) => {
      if (params?.[0]?.multiple) {
        res.forEach((item: any) => {
          item.selected = false
        })
        paperComputilityList.value = res as any[]
      } else {
        if (Array.isArray(res)) {
          let hasSelected = false
          // 有tag的 选中tag 没有tag的选中第一项
          res.forEach((item: RechargeProps) => {
            if (params?.[0].goodsId) {
              if (item.id === params?.[0].goodsId) {
                item.selected = true
                hasSelected = true
                selectedGood.value = item
              } else {
                item.selected = false
              }
              return
            }

            if (item.tag) {
              item.selected = true
              hasSelected = true
              selectedGood.value = item
            } else {
              item.selected = false
            }
          })

          if (!hasSelected && res.length > 0) {
            res[0].selected = true
            selectedGood.value = res[0]
          }
          rechargeList.value = res
        } else {
          rechargeList.value = []
          selectedGood.value = res
        }
      }
    },
  },
)

const openPayment = (data) => {
  if (activeKey.value === 'CHECK') {
    if (payParam.value.id) {
      runPaperCheckDetail({
        id: payParam.value.id,
      })
    } else {
      runCheckRecharge({
        ...payParam.value,
        categoryId: undefined,
      })
    }
    return
  }
  if (activeKey.value === 'PaperRepeat') {
    return runCreatePaperCheckOrder({
      ...payParam.value,
      categoryId: undefined,
    })
  }
  if (activeKey.value === 'AIGCCheck' || activeKey.value === 'AIGCRepeat') {
    return runCreateAigcCheckOrder({
      ...payParam.value,
      categoryId: undefined,
    })
  }
  if (activeKey.value === 'office') {
    return runFetchMemberList({
      ...payParam.value,
    })
  }

  // pro和ultra加油包是package
  runFetchRechargeTemplate({
    goodsType: data.goodsType,
    paperId: payParam?.value?.paperId || data?.paperId || undefined,
    goodsId: data?.goodsId || undefined,
  })

  if (payParam.value.type === 'paper' && data.paperId) {
    setTimeout(() => {
      runFetchRechargeTemplate({
        goodsType: data.goodsType,
        multiple: true,
      })
    }, 1000)
  }
}

// 论文降aigc专用
const { run: runCreateAigcCheckOrder } = useRequest(createAigcCheckOrder, {
  manual: true,
  debounceWait: 100,
  onSuccess: (res) => {
    if (Array.isArray(res)) {
      let hasSelected = false
      // 有tag的 选中tag 没有tag的选中第一项
      res.forEach((item: RechargeProps) => {
        if (item.tag) {
          item.selected = true
          hasSelected = true
          selectedGood.value = item
        } else {
          item.selected = false
        }
      })
      if (!hasSelected && res.length > 0) {
        res[0].selected = true
        selectedGood.value = res[0]
      }
      rechargeList.value = res
    } else {
      rechargeList.value = []
      selectedGood.value = res
    }
  },
})
// 论文降重专用
const { run: runCreatePaperCheckOrder } = useRequest(createPaperCheckOrder, {
  manual: true,
  debounceWait: 100,
  onSuccess: (res) => {
    if (Array.isArray(res)) {
      let hasSelected = false
      // 有tag的 选中tag 没有tag的选中第一项
      res.forEach((item: RechargeProps) => {
        if (item.tag) {
          item.selected = true
          hasSelected = true
          selectedGood.value = item
        } else {
          item.selected = false
        }
      })
      if (!hasSelected && res.length > 0) {
        res[0].selected = true
        selectedGood.value = res[0]
      }
      rechargeList.value = res
    } else {
      rechargeList.value = []
      selectedGood.value = res
    }
  },
})

// 论文查重专用
const { run: runCheckRecharge, loading: checkRechargeLoading } = useRequest(fetchCheckRecharge, {
  manual: true,
  debounceWait: 100,
  onSuccess: (res) => {
    if (Array.isArray(res)) {
      let hasSelected = false
      // 有tag的 选中tag 没有tag的选中第一项
      res.forEach((item: RechargeProps) => {
        if (item.tag) {
          item.selected = true
          hasSelected = true
          selectedGood.value = item
        } else {
          item.selected = false
        }
      })
      if (!hasSelected && res.length > 0) {
        res[0].selected = true
        selectedGood.value = res[0]
      }
      rechargeList.value = res
    } else {
      rechargeList.value = []
      selectedGood.value = res
    }
  },
})
// 论文查重重新支付专用
const { run: runPaperCheckDetail, loading: paperCheckDetailLoading } = useRequest(fetchPaperCheckDetail, {
  manual: true,
  debounceWait: 100,
  onSuccess: (res) => {
    if (Array.isArray(res)) {
      let hasSelected = false
      // 有tag的 选中tag 没有tag的选中第一项
      res.forEach((item: RechargeProps) => {
        if (item.tag) {
          item.selected = true
          hasSelected = true
          selectedGood.value = item
        } else {
          item.selected = false
        }
      })
      if (!hasSelected && res.length > 0) {
        res[0].selected = true
        selectedGood.value = res[0]
      }
      rechargeList.value = res
    } else {
      rechargeList.value = []
      selectedGood.value = res
    }
  },
})

// office支付专用
const { run: runFetchMemberList, loading: fetchMemberListLoading } = useRequest(fetchMemberList, {
  manual: true,
  debounceWait: 100,
  onSuccess: (res) => {
    if (Array.isArray(res)) {
      let hasSelected = false
      // 有tag的 选中tag 没有tag的选中第一项
      res.forEach((item: RechargeProps) => {
        if (item.tag) {
          item.selected = true
          hasSelected = true
          selectedGood.value = item
        } else {
          item.selected = false
        }
      })
      if (!hasSelected && res.length > 0) {
        res[0].selected = true
        selectedGood.value = res[0]
      }
      rechargeList.value = res
    } else {
      rechargeList.value = []
      selectedGood.value = res
    }
  },
})

const handleSwitchPayment = (item: { id: number }) => {
  checkedPayment.value = item.id
}

const handlePaymentSuccess = () => {
  show.value = false
  message.success('支付成功')

  setTimeout(() => {
    promiseResolvers.value?.resolve()
    promiseResolvers.value = null
  }, 300)
}

// 切换会员类型
const handleSwitchTab = (type: string) => {
  activeKey.value = type
  runFetchRechargeTemplate({
    goodsType: type,
  })
}

// 选择产品
const handleUpdateSelectGood = (item: any, selected?: boolean) => {
  console.log(item)

  if (!paperComputilityList.value.length) {
    selectedGood.value = item
    rechargeList.value.map((item: RechargeProps) => {
      if (item.id === selectedGood.value.id) {
        item.selected = true
      } else {
        item.selected = false
      }
    })
  } else {
    if (selected) {
      selectedGood.value = item
    } else {
      selectedGood.value = rechargeList?.value?.[0]
    }
    paperComputilityList.value.map((item: any) => {
      if (item.id === selectedGood.value.id) {
        item.selected = selected
      } else {
        item.selected = false
      }
    })
  }
}
</script>

<template>
  <NModal
    :show="show"
    style="
      background: url(https://cdn2.weimob.com/saas/saas-fe-sirius-orion-node/production/zh-CN/503/vipbg.png);
      --n-padding-top: 0;
      --n-padding-bottom: 0;
      --n-padding-left: 0;
      border-radius: 10px;
      background-position: center;
      background-repeat: no-repeat;
      background-size: cover;
      padding-bottom: 20px;
    "
    :style="{ width: '1050px' }"
    class="recharge-container relative sm:!w-[96vw] sm:rounded-md sm:overflow-hidden ipad:!w-[100vw]"
    :closable="false"
    reset="card"
    @mask-click="handleClose"
    @close="handleClose"
    :on-esc="handleClose"
  >
    <template #default>
      <NSpin :show="loading">
        <div class="flex flex-row items-center justify-between pl-[38px] pr-[29px] recharge-header-container sm:hidden ipad:hidden">
          <RechargeHeaderUser />
          <div class="flex items-center gap-x-[26px]">
            <div v-if="!['paper', 'CHECK', 'PaperRepeat', 'AIGCCheck', 'AIGCRepeat', 'office'].includes(activeKey)" class="flex flex-row gap-x-[5px] items-center flex-1">
              <span class="text-[#FF1A00] text-[16px] leading-[24px]">*</span>
              <span class="text-[#151515] text-[14px] leading-[24px]">注：会员权益中的积分消耗是按单项最大值累计的，具体额度请查看实际使用情况</span>
            </div>
            <IconClose class="w-[14px] h-[14px] text-[14px] relative cursor-pointer text-[#404040]" @click="handleClose" />
          </div>
        </div>

        <div class="flex flex-row justify-start sm:w-full ipad:w-full">
          <div class="w-[226px] pt-[2px] pr-[1px] sm:hidden ipad:hidden flex justify-center shrink-0 grow-0">
            <RechargeValueAddedDetail :goods-type="activeKey" :selected-good="selectedGood" />
          </div>
          <div class="flex-1 pt-[2px] pr-[22px] sm:p-3 sm:w-full ipad:w-full min-w-0">
            <div v-if="!payParam.paperId && ['pro', 'ultra', 'package', 'paper'].includes(activeKey)" class="tabs-wrap flex">
              <template v-if="activeKey !== 'paper'">
                <div
                  v-for="(item, index) in tabList"
                  :key="item.type"
                  class="tabs-item"
                  :class="`tab${index + 1} ${activeKey === item.type ? 'active' : ''}`"
                  :style="`background-image: url('https://cdn2.weimob.com/saas/saas-fe-sirius-orion-node/production/zh-CN/503/tabn${index + 1}-${activeKey === item.type ? 2 : 1}.png')`"
                  @click="handleSwitchTab(item.type)"
                >
                  <span>{{ item.name }}</span>
                  <NImage :src="Vip" width="14" height="14" preview-disabled review-disabled> </NImage>
                </div>
              </template>
              <template v-else>
                <div class="bg-[#FFFFFF] flex-1 rounded-tl-[7px] rounded-tr-[7px] leading-[56px] text-[#333333] text-[20px] font-bold pl-[16px]">论文算力包</div>
              </template>
            </div>
            <!-- 查重 | 降重 | AIGC查重 | AIGC降重 -->
            <PaperPremiumRecharge v-if="['CHECK', 'PaperRepeat', 'AIGCCheck', 'AIGCRepeat'].includes(activeKey)" :recharge-list="rechargeList || []" :goods-type="activeKey" />
            <PaperRecharge v-else-if="activeKey === 'paper' && payParam.paperId" :show="show" :recharge-list="rechargeList" :paper-computility-list="paperComputilityList" @update-select-good="handleUpdateSelectGood" />
            <OfficeRecharge v-else-if="activeKey === 'office'" :recharge-list="rechargeList || []" :member-type="payParam?.memberType" @update-select-good="handleUpdateSelectGood" />
            <RechargeCardList v-else :recharge-list="rechargeList" @update-select-good="handleUpdateSelectGood" />
            <div class="divider px-[20px] pt-[20px]">
              <div></div>
            </div>
            <NSpin :show="loading">
              <PaymentDetail v-if="show" :payment-detail="selectedGood" :payment-list="paymentList" @payment-success="handlePaymentSuccess" :type="activeKey" :task-id="payParam?.taskId" />
            </NSpin>
          </div>
        </div>
      </NSpin>
    </template>
  </NModal>
</template>

<style lang="less" scoped>
.recharge-header-container {
  height: 80px;
  overflow: hidden;
}

.recharge-container {
  .n-base-close {
    display: none !important;
  }
}

.divider {
  background-color: #fff;
  > div {
    width: 100%;
    height: 1px;
    background: #eeeeee;
  }
}

.tabs-wrap {
  position: relative;
  height: 46px;

  .tabs-item {
    cursor: pointer;
    height: 46px;
    position: absolute;
    top: 0;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    background-size: contain;
    background-repeat: no-repeat;
    color: #212f46;
    @media screen and (max-width: 768px) {
      background: linear-gradient(90deg, rgba(105, 150, 255, 0.33) 0%, rgba(217, 236, 255, 0.62) 100%) !important;
      width: 33% !important;
      position: static !important;
      border-radius: 5px;
      margin-right: 10px;
    }

    &.tab1 {
      left: 0;
      width: 267px;
      background-position: left top;
    }
    &.tab2 {
      left: 248px;
      width: 302px;
      background-position: bottom center;
    }
    &.tab3 {
      right: 0;
      width: 277px;
      background-position: top right;
    }
    &.active {
      color: #fff;
      // background: linear-gradient(90deg, #0095ff 0%, #9e5dff 99%);
      @media screen and (max-width: 768px) {
        background: linear-gradient(90deg, #0095ff 0%, #9e5dff 99%) !important;
      }
    }
  }
}
</style>
