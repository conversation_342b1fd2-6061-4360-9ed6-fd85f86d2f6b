<script lang="ts" setup>
import RechargeCard from './RechargeCard.vue';
import LeftArrowImg from '@/assets/images/left_arrow.png';
import RightArrowImg from '@/assets/images/right_arrow.png';

import { NImage } from 'naive-ui';
import { computed, nextTick, ref, watch, onMounted } from 'vue';

interface Props {
    rechargeList: any[]
}
interface Emit {
    (ev: 'update-select-good', item: any): void
}
const props = defineProps<Props>()
const emit = defineEmits<Emit>()
const containerRef = ref<HTMLElement | null>(null)
const showRightArrow = ref(false)
const showLeftArrow = ref(false)

// 检查是否需要显示箭头
const checkArrows = () => {
    if (!containerRef.value) return

    const { scrollLeft, scrollWidth, clientWidth } = containerRef.value

    // 只有当内容宽度大于容器宽度时才显示箭头
    const hasOverflow = scrollWidth > clientWidth

    showLeftArrow.value = hasOverflow && scrollLeft > 0
    showRightArrow.value = hasOverflow && Math.ceil(scrollLeft + clientWidth) < scrollWidth
}

// 监听容器大小变化
onMounted(() => {
    if (!containerRef.value) return

    const resizeObserver = new ResizeObserver(() => {
        checkArrows()
    })

    resizeObserver.observe(containerRef.value)

    // 初始检查
    nextTick(() => {
        checkArrows()
    })
})

// 监听列表变化
watch(() => props.rechargeList, () => {
    nextTick(() => {
        checkArrows()
    })
}, { deep: true })

const handleScrollToLeft = () => {
    const { scrollLeft } = containerRef.value as HTMLElement
    containerRef.value?.scrollTo({
        left: scrollLeft - 250,
        behavior: 'smooth'
    })

    setTimeout(() => {
        checkArrows()
    }, 300)
}

const handleScrollToRight = () => {
    const { scrollLeft } = containerRef.value as HTMLElement
    containerRef.value?.scrollTo({
        left: scrollLeft + 250,
        behavior: 'smooth'
    })

    setTimeout(() => {
        checkArrows()
    }, 300)
}

</script>

<template>

    <div class="bg-[#FFFFFF] min-h-[212px] relative sm:w-full sm:overflow-hidden pt-[4px]">
        <div class="flex flex-row gap-x-[9px] h-[232px] overflow-x-scroll HideScrollbar px-[21px] test-container items-end sm:px-3"
            :style="{
                '--left-radial': showLeftArrow ? 'block' : 'none',
                '--right-radial': showRightArrow ? 'block' : 'none'
            }" ref="containerRef">
            <RechargeCard v-for="item in rechargeList"
                @update-select-good="($event) => emit('update-select-good', $event)" :key="item.id" :alias="item.alias"
                :description="item.description" :discount="item.discount" :name="item.name" :id="item.id"
                :selected="item.selected" :discount-desc="item.discountDesc" :original-price="item.originalPrice"
                :tag="item.tag" :gift-desc="item.giftDesc" :mark-url="item.markUrl" :mark="item.mark"
                :all-desc="item.allDesc" :price="item.price" />
        </div>
        <NImage :src="LeftArrowImg" @click="handleScrollToLeft"
            class=" w-[26px] h-[26px] absolute top-[50%] left-[24px] z-[2] cursor-pointer" preview-disabled
            v-if="showLeftArrow" />
        <NImage :src="RightArrowImg" @click="handleScrollToRight"
            class=" w-[26px] h-[26px] absolute top-[50%] right-[14px] z-[2] cursor-pointer" preview-disabled
            v-if="showRightArrow" />
    </div>

</template>


<style lang="less">
.test-container {
    &::before {
        content: " ";
        width: 51px;
        height: calc(100% - 50px);
        display: var(--left-radial);
        top: 23px;
        left: 0;
        position: absolute;
        filter: blur(20px);
        transform: scale(1.2);
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.77) 0%, rgba(255, 255, 255, 0) 100%);
        z-index: 1;
    }

    &::after {
        content: " ";
        width: 51px;
        height: calc(100% - 50px);
        display: var(--right-radial);
        top: 23px;
        right: 0;
        position: absolute;
        filter: blur(20px);
        background: linear-gradient(90deg, rgba(255, 255, 255, 0.77) 0%, rgba(255, 255, 255, 0) 100%);
        transform: scale(1.2);
        z-index: 1;
    }
}
</style>
