<template>
	<div>
		<!-- 个人版 -->
		<div
			class="flex gap-2 items-center cursor-pointer"
		>
			<NAvatar
				:size="40"
				round
				:src="userStore.userInfo?.avatar || defaultAvatar"
			/>
			<div class="flex flex-col items-start">
				<div class="flex items-center">
					<span v-if="userStore.userInfo?.uid" class="text-title">{{
						userStore.userInfo?.name
					}}</span>
					<span v-else class="text-title">临时用户</span>
				</div>
				<div
					class="grow-0 leading-[12px] text-[12px] bg-[#1C68FF] rounded-[2px] text-white flex nowrap items-center p-1 cursor-pointer"
				>
					个人版
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { useUserStore } from "@/store";
import { NAvatar, NModal } from "naive-ui";
import defaultAvatar from "@/assets/avatar.jpg";
import { onMounted, ref, watch } from "vue";

const showChange = defineModel<boolean>("show-change");
const emit = defineEmits<{
	onTeamChange: [teamId?: string, prevTeamId?: string];
}>();
const props = defineProps<{ isTeam?: number; disabled?: boolean }>();

const userStore = useUserStore();
const prevTeamId = ref(userStore.curTeam?.id);

watch(
	() => userStore.userInfo.uid,
	(value) => {
		if (value) userStore.getTeamList();
	},
	{ immediate: true }
);

watch(
	() => showChange.value,
	(value) => {
		if (value) {
			if (!userStore.curTeam) userStore.changeTeam(userStore.teamList?.[0]?.id);
			prevTeamId.value = userStore.curTeam?.id;
		}
	}
);
watch(
	() => userStore.teamList,
	(value) => {
		if (props.isTeam && !userStore.curTeam) {
			if (value?.length === 1) userStore.changeTeam(value[0].id);
			if (value?.length > 1) showChange.value = true;
		}
	}
);

const handlePersonClick = () => {
	if (props.disabled) return;
	if (!userStore.userInfo.uid) return window.$aiwork?.openLogin?.();
	showChange.value = true;
};
</script>
