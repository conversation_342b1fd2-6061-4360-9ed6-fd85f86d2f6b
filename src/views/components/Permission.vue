<script setup lang="ts">
import { computed, ref, watch } from "vue";
import {
	NButton,
	NInput,
	NModal,
	useMessage,
	NImage,
	NForm,
	NFormItem,
	NText,
	NTabs,
	NTab,
} from "naive-ui";
import { fetchGetCode, fetchCodeLogin } from "@/chatgpt";
import { useAuthStore, useUserStore } from "@/store";
import { useBasicLayout } from "@/hooks/useBasicLayout";
import { isPhoneNumber } from "@/utils/is";
import Qrcode from "./Qrcode.vue";
import logoImage from "@/assets/aiwork/logo.png";
import { AiworkType } from "@/typings"; // 导入 AiworkType
import { setUser } from "@/store/modules/auth/helper"; // 导入 setUser

// 登录状态管理
const promiseResolvers = ref<any>();
const show = ref(false);
const loading = ref(false);
const ms = useMessage();
const authStore = useAuthStore();
const userStore = useUserStore();
const { isMobile } = useBasicLayout();

// 登录类型切换：'wechat' 或 'phone'
const loginType = ref<string>("wechat"); // 默认微信登录

// 手机号登录相关
let countdownSeconds = ref(60);
let countdownActive = ref(false);
const formValue = ref<{ username: string; password: string }>({
	username: "",
	password: "",
});

const disabled = computed(
	() =>
		!formValue.value?.username?.trim() ||
		!formValue.value.password ||
		loading.value
);
const disabled3 = computed(
	() => !formValue.value?.username?.trim() || countdownActive.value
);

const countdownLabel = computed(() => {
	if (countdownActive.value) {
		return `重新获取(${countdownSeconds.value})`;
	} else {
		return "获取验证码";
	}
});

(window.$aiwork as AiworkType).openLogin = () => {
	promiseResolvers.value = Promise.withResolvers();
	show.value = true;
	return promiseResolvers.value.promise;
};

watch(
	() => show.value,
	(newValue) => {
		if (newValue) {
			window.rprm.rec({
				elementid: "login",
			});
		}
	}
);

async function handleVerify(event: MouseEvent) {
	event.preventDefault();
	const name = formValue.value?.username;
	const pwd = formValue.value?.password;

	if (!name || !pwd) return;
	try {
		loading.value = true;
		let user: any;
		user = await fetchCodeLogin(name, pwd); // 假设短信登录使用fetchCodeLogin

		if (!user) {
			ms.error("登录失败");
			return;
		}

		authStore.setToken(user.token);
		setUser(user);
		userStore.updateUserInfo({
			name: user.nickname || user.username || user.phone,
			description: "用户信息",
			uid: Number(user.id), // 确保为number类型
		});
		ms.success("登录成功");
		window.rprm.rec({
			elementid: "login_success",
			logon_type: "phone",
		});
		show.value = false;
		promiseResolvers.value?.resolve();
		promiseResolvers.value = null;
		userStore.getTeamList();
	} catch (error: any) {
		ms.error(error.message || "error");
		authStore.removeToken();
		formValue.value.password = "";
		window.rprm.rec({
			elementid: "login_fail",
			logon_type: "phone",
			login_error: error.message || "error",
		});
	} finally {
		loading.value = false;
	}
}

async function handleCode(event: MouseEvent) {
	event.preventDefault();
	if (disabled3.value) {
		return;
	}
	if (!isPhoneNumber(formValue.value?.username.trim())) {
		ms.error("请输入正常的手机号码");
		return;
	}
	countdownActive.value = true;

	let interval = setInterval(() => {
		countdownSeconds.value--;
		if (countdownSeconds.value <= 0) {
			clearInterval(interval);
			countdownSeconds.value = 60;
			countdownActive.value = false;
		}
	}, 1000);
	try {
		await fetchGetCode(formValue.value?.username.trim());
		ms.success("发送成功");
	} catch (error) {
		countdownSeconds.value = 60;
		countdownActive.value = false;
		ms.error("发送失败");
	}
	return;
}

const handClose = () => {
	show.value = false;
	promiseResolvers.value?.reject({ type: "cancel", message: "取消登录" });
	promiseResolvers.value = null;
	window.rprm.rec({
		elementid: "login_cancel",
	});
};

const handSuccess = () => {
	show.value = false;
	promiseResolvers.value?.resolve();
	promiseResolvers.value = null;
	window.rprm.rec({
		elementid: "login_success",
		logon_type: "qrcode",
	});
};

function handlePress(event: KeyboardEvent) {
	if (event.key === "Enter" && !event.shiftKey) {
		event.preventDefault();
		handleVerify(event)
	}
}

const handleChangeLoginType = (type: string) => {
	loginType.value = type;
};
const gotoUserAgreement = () => {
  window.open('https://aiwork365.cn/useragreement')
}

const gotoPrivacyPolicy = () => {
  window.open('https://aiwork365.cn/privacypolicy')
}
</script>

<template>
	<NModal
		v-model:show="show"
		:on-mask-click="handClose"
		:on-esc="handClose"
		:style="{
			width: '900px',
			height: '543px',
			background: '#fff',
			display: 'flex',
		}"
	>
		<div class="modal-content-wrapper">
			<!-- 左侧权益面板 -->
			<div class="left-panel">
				<!-- 背景图片和渐变 -->
				<div class="left-panel-bg"></div>

				<!-- Logo -->
				<div class="logo-section">
					<NImage :src="logoImage" class="logo-img" preview-disabled />
				</div>

				<!-- 标题 -->
				<div class="login-detail">
					<h2 class="main-title">重磅福利 注册即送</h2>
					<p class="sub-title">登录后畅享6重专属权益</p>

					<!-- 权益列表 -->
					<ul class="benefits-list">
						<li><span class="check-icon"></span>3次AI聊天/天</li>
						<li><span class="check-icon"></span>1次短文创作/天</li>
						<li><span class="check-icon"></span>1张AI绘图/天</li>
						<li><span class="check-icon"></span>1次AI生成PPT/天</li>
						<li><span class="check-icon"></span>1次海报创作/天</li>
						<li><span class="check-icon"></span>享深度思考&联网搜索</li>
					</ul>
				</div>
			</div>

			<!-- 右侧登录面板 -->
			<div class="right-panel">
				<!-- 关闭按钮 -->
				<div class="close-button" @click="handClose"></div>

				<!-- 登录方式切换 -->
				<n-tabs
					type="segment"
					size="large"
					v-model:value="loginType"
					:style="{
						width: '350px',
						height: '45px',
						margin: '0',
						'--n-tab-padding': '0 16px',
						'--n-tab-gap': '0',
					}"
				>
					<n-tab name="wechat">微信登录</n-tab>
					<n-tab name="phone">短信登录</n-tab>
				</n-tabs>

				<!-- 微信登录内容 -->
				<div v-if="loginType === 'wechat'" class="wechat-login-content">
					<Qrcode :isbind="false" @close="handClose" @success="handSuccess" />
				</div>

				<!-- 短信登录内容 -->
				<div v-if="loginType === 'phone'" class="phone-login-content">
					<NForm ref="formRef" :model="formValue">
						<NFormItem>
							<NInput
								v-model:value="formValue.username"
								placeholder="请输入手机号"
								@keypress="handlePress"
							/>
						</NFormItem>
						<NFormItem>
							<div class="verification-code-input">
								<NInput
									v-model:value="formValue.password"
									type="text"
									maxlength="4"
									placeholder="请输入验证码"
									@keypress="handlePress"
								/>
								<NButton
									quaternary
									type="primary"
									:disabled="disabled3"
									@click="handleCode"
								>
									{{ countdownLabel }}
								</NButton>
							</div>
						</NFormItem>
						<NFormItem>
							<NButton
								type="primary"
								size="large"
								:loading="loading"
								:disabled="disabled"
								style="width: 100%"
								@click="handleVerify"
							>
								登录
							</NButton>
						</NFormItem>
					</NForm>
				</div>

				<!-- 协议提示 -->
			<!-- 协议提示 -->
			<div class="agreement-text" v-if="loginType === 'phone'">
					<div class=" text-center">
						未注册手机验证后自动注册登录
					</div>
					<div>
						注册即表示同意
						<NText type="primary" class=" cursor-pointer" @click="gotoUserAgreement">《用户协议》</NText>和<NText
							type="primary" class=" cursor-pointer" @click="gotoPrivacyPolicy"
							>《隐私条款》</NText
						>
					</div>
				</div>
				<p v-else class="agreement-text">
					登陆即表示同意<NText type="primary" @click="gotoUserAgreement"
						>《用户协议》</NText
					>和<NText type="primary" @click="gotoPrivacyPolicy">《隐私条款》</NText>
				</p>
			</div>
		</div>
	</NModal>
</template>
<style lang="less" scoped>
.modal-content-wrapper {
	display: flex;
	width: 100%;
	height: 100%;
	border-radius: 10px;
	overflow: hidden;
	box-shadow: 0px 2.56px 6.4px 0px rgba(172, 181, 191, 0.21); // 从DSL绑定阴影

	.left-panel {
		position: relative;
		width: 460px; // 根据图片比例调整
		display: flex;
		flex-direction: column;
		padding: 39px 37px;
		box-sizing: border-box;
		overflow: hidden; // 确保背景图片不溢出

		.left-panel-bg {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: url(https://cdn2.weimob.com/saas/saas-fe-sirius-orion-node/production/zh-CN/503/img.png)
				no-repeat center center;
			background-size: contain;
			border-radius: 10px 0px 0px 10px;
			z-index: 0;
		}
		> * {
			position: relative; // 确保内容在背景之上
			z-index: 2;
		}

		.logo-section {
			display: flex;
			align-items: center;
			margin-bottom: 16px;

			.logo-img {
				height: 26px; // 根据DSL logo组高度
				width: auto;
				margin-right: 8px;
			}

			.logo-text {
				font-size: 18px; // 假设字体大小
				color: #121519; // 假设颜色
				font-weight: bold;
			}
		}
		.login-detail {
			padding: 0 0 0 20px;
			.main-title {
				display: inline;
				font-family: "Microsoft YaHei";
				font-size: 33px; // 调整大小以匹配图片
				line-height: 44px;
				font-weight: bold;
				background: linear-gradient(
					90deg,
					#0044ff 0%,
					#9e51f7 94%
				); // 注册即送 文本颜色
				background-clip: text;
				color: transparent;
			}

			.sub-title {
				display: inline-block;
				text-align: center;
				border-radius: 33px;
				width: 244px;
				font-size: 20px; // 调整大小以匹配图片
				line-height: 44px;
				color: #ffffff;
				margin-top: 10px;
				margin-bottom: 30px;
				background: linear-gradient(
					90deg,
					#0044ff 0%,
					#9e51f7 94%
				); // 注册即送 文本颜色
				opacity: 0.5;
			}

			.benefits-list {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				gap: 12px;
				list-style: none;
				padding: 0;

				li {
					display: flex;
					align-items: center;
					font-family: "Microsoft YaHei";
					font-size: 15px;
					color: #151515;
					line-height: 20px;
					row-gap: 13px;
					align-items: center;
					.check-icon {
						display: inline-block;
						width: 15px;
						height: 15px;
						margin-right: 5px;
						background-image: url("https://cdn2.weimob.com/saas/saas-fe-sirius-orion-node/production/zh-CN/503/blue-check-icon.png");
						background-repeat: no-repeat;
						background-position: center;
						background-size: contain;
					}
				}
			}
		}
	}
	.right-panel {
		position: relative;
		flex-grow: 1;
		background: #ffffff;
		flex: 1;
		padding: 65px 40px;
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		align-items: center; // 居中内容

		.close-button {
			position: absolute;
			top: 20px;
			right: 20px;
			width: 12px; // 调整大小以匹配图片
			height: 12px; // 调整大小以匹配图片
			cursor: pointer;
			background-image: url("data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.125 6L12 10.875L10.875 12L6 7.125L1.125 12L0 10.875L4.875 6L0 1.125L1.125 0L6 4.875L10.875 0L12 1.125L7.125 6Z' fill='%23666666'/%3E%3C/svg%3E");
			background-size: contain;
			background-repeat: no-repeat;
		}

		.n-tabs {
			.n-tabs-nav.n-tabs-nav--segment-type {
				background-color: #ebecf0;
				border-radius: 20px;
				padding: 3px;

				.n-tabs-tab {
					font-family: "Microsoft YaHei";
					font-size: 14px;
					color: #222222;
					border-radius: 17px;
					margin: 0;
					height: 34px;
					line-height: 34px;

					&.n-tabs-tab--active {
						background-color: #ffffff;
						color: #222222;
					}
				}
			}
		}

		.wechat-login-content {
			display: flex;
			flex-direction: column;
			align-items: center;
			margin-top: 20px; // 调整位置

			.wechat-tip {
				font-family: "Microsoft YaHei";
				font-size: 12px; // 根据DSL font_116_62299 size
				color: #3d3d3d;
				margin-top: 20px;
			}
		}

		.phone-login-content {
			width: 350px; // 与tab宽度一致
			margin-top: 20px; // 调整位置

			.n-form-item {
				margin-bottom: 20px; // 调整间距
			}

			:deep(.n-input) {
				height: 52px;
				.n-input__input {
					height: 52px;
					min-height: 52px;
				}
			}
			:deep(.n-input__input-el) {
				height: 52px;
			}

			.n-form-item:first-of-type .n-input {
				// 针对手机号输入框
				border: 1px solid #e8e8f3 !important;
				box-shadow: none !important;
				background-color: #fafbff !important;
				border-radius: 3px; // 添加圆角
			}

			.verification-code-input {
				display: flex;
				width: 100%;
				border: 1px solid #e8e8f3 !important;
				box-shadow: none !important;
				background-color: #fafbff !important;
				border-radius: 3px; // 添加圆角

				.n-input {
					// 验证码输入框内部的NInput
					flex-grow: 1;
					border: none !important; // 移除内部NInput边框
					box-shadow: none !important;
					background-color: transparent !important; // 内部NInput透明背景
				}

				.n-button {
					margin-left: 10px;
					white-space: nowrap;
					font-family: "Microsoft YaHei";
					font-size: 14px;
					color: #0e69ff; // 假设获取验证码按钮颜色
					height: 52px;
					padding: 0 16px;
				}
			}

			.n-button[type="primary"] {
				background: linear-gradient(
					90deg,
					#0044ff 0%,
					#9e51f7 94%
				); // 登录按钮渐变
				border: none;
				margin-top: 30px; // 调整位置
			}
		}

		.agreement-text {
			font-size: 12px;
			color: #3d3d3d;
			text-align: center;
			margin-top: 84px;

			.n-text {
				color: #0e69ff;
			}
		}
	}
}
:deep(.n-tabs-capsule) {
	height: 35px !important;
}
:deep(.n-tabs-tab-wrapper) {
	height: 35px !important;
}
// 覆盖Naive UI默认样式，使其更贴合设计图
:deep(.n-modal) {
	border-radius: 10px !important;
	overflow: hidden;
}

:deep(.n-modal-body) {
	padding: 0 !important;
}

:deep(.n-form-item-label),
:deep(.n-form-item-feedback-wrapper) {
	opacity: 0;
	display: none !important;
}

:deep(.n-form-item.n-form-item--top-labelled) {
	grid-template-areas: "blank";
}

:deep(.n-input__border),
:deep(.n-input__state-border) {
	display: none;
}
</style>
