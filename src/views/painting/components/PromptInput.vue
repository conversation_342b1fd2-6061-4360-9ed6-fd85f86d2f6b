<template>
  <div class="h-[140px] mt-[24px] sm:h-[160px]">
    <div class="absolute z-10 bottom-[40px] left-[40px] right-[40px] border-[1px] border-[#0E69FF] border-solid rounded-[10px] bg-white p-[16px_20px] sm:bottom-[65px] sm:left-4 sm:right-4">
      <div>
        <input v-if="['IMAGINE', 'SHORTEN'].includes(promptType)" type="text" placeholder="请输入提示词" v-model="promptContent" :maxlength="2000" class="h-[40px] w-full p-[12px] outline-none" @keypress.enter="submit" />

        <SwapfaceImg v-if="['SWAP_FACE'].includes(promptType)" v-model:sourceBase64="settings.sourceBase64" v-model:targetBase64="settings.targetBase64" />

        <div v-if="['DESCRIBE', 'BLE<PERSON>'].includes(promptType)">
          <div class="flex flex-wrap gap-[12px]">
            <div v-for="(f, index) in fileList" :key="f.id" class="w-[90px] h-[90px] rounded-[8px] overflow-hidden bg-[#cccc] relative">
              <img :src="f.url" class="w-full h-full object-cover" alt="" />

              <div class="absolute right-[4px] top-[4px] cursor-pointer" @click="delImg(index)">
                <NIcon color="#F45B1E" size="20">
                  <TrashOutline></TrashOutline>
                </NIcon>
              </div>
            </div>
            <div v-if="fileList.length < maxImgLength" class="w-[90px] h-[90px] rounded-[8px] overflow-hidden border-dashed border-[1px] border-[#0E69FF] flex justify-center relative">
              <div class="text-center pt-[18px]">
                <p class="inline-block text-[20px] pb-[8px]">
                  <Add class="w-[20px] h-[20px]"></Add>
                </p>
                <p class="text-[10px]">将图片拖到此处或点击上传</p>
              </div>
              <input type="file" accept="image/png,image/jpeg" :multiple="promptType == 'BLEND'" class="absolute left-0 right-0 top-0 bottom-0 opacity-0 cursor-pointer" @change="fileChange" />
            </div>
          </div>
          <div class="text-[#999999] text-[14px] pt-[16px]">只支持.jpg .png格式，最大10M{{ promptType == 'BLEND' ? '，最多上传5张' : '' }}</div>
        </div>
      </div>
      <div class="flex justify-between border-t-[1px] border-[rgb(223,223,223)] border-solid pt-[12px] mt-[12px]">
        <div class="flex gap-[10px] text-[#696969] sm:flex-wrap sm:justify-between">
          <n-popover :trigger="isMobile ? 'click' : 'focus'">
            <template #trigger>
              <n-button type="tertiary" size="small">
                <span class="flex gap-[4px] items-center">
                  <Change />
                  {{ settings.speedMode == 'RELAX' ? '普通模式' : '快速模式' }}
                </span>
              </n-button>
            </template>
            <div>
              <div class="flex items-center gap-[32px] mb-[10px]">
                <span>绘图模式</span>
                <n-popover :trigger="!isMobile ? 'hover' : 'click'" placement="right">
                  <template #trigger>
                    <NIcon color="#0E69FF" @click.stop.prevent>
                      <HelpCircleSharp />
                    </NIcon>
                  </template>
                  <div>普通模式：高峰期预计需要10分钟出图；快速模式：会员专享模式，平均1-2分钟出图</div>
                </n-popover>
              </div>
              <div class="flex items-center gap-[32px] mb-[10px] cursor-pointer" @click="settings.speedMode = 'FAST'">
                <span>快速模式</span>
                <NIcon>
                  <ChevronForward />
                </NIcon>
              </div>
              <div class="flex items-center gap-[32px] mb-[10px] cursor-pointer" @click="settings.speedMode = 'RELAX'">
                <span>普通模式</span>
                <NIcon>
                  <ChevronForward />
                </NIcon>
              </div>
            </div>
          </n-popover>

          <template v-if="['IMAGINE'].includes(promptType)">
            <n-popover :trigger="!isMobile ? 'hover' : 'click'">
              <template #trigger>
                <n-button type="tertiary" size="small">
                  <span class="flex gap-[4px] items-center">
                    <Pan />
                    {{ settings.botType == 'MID_JOURNEY' ? '写实风格' : '动画风格' }}
                  </span>
                </n-button>
              </template>
              <div>
                <div class="flex items-center gap-[32px] mb-[10px]">
                  <span>风格设置</span>
                  <n-popover trigger="hover" placement="right">
                    <template #trigger>
                      <NIcon color="#0E69FF">
                        <HelpCircleSharp />
                      </NIcon>
                    </template>
                    <div>切换图片风格</div>
                  </n-popover>
                </div>
                <div class="flex items-center gap-[32px] mb-[10px] cursor-pointer" @click="settings.botType = 'MID_JOURNEY'">
                  <span>写实风格</span>
                  <NIcon>
                    <ChevronForward />
                  </NIcon>
                </div>
                <div class="flex items-center gap-[32px] mb-[10px] cursor-pointer" @click="settings.botType = 'NIJI_JOURNEY'">
                  <span>动画风格</span>
                  <NIcon>
                    <ChevronForward />
                  </NIcon>
                </div>
              </div>
            </n-popover>
          </template>

          <template v-if="['BLEND'].includes(promptType)">
            <ImageSize v-model="settings.dimensions" />
          </template>

          <template v-if="['IMAGINE'].includes(promptType)">
            <CustomSettings ref="customSettingsRef" @settingChange="settingChange"></CustomSettings>
            <AiPrompt ref="aiPromptRef" @useAiPrompt="useAiPrompt"></AiPrompt>
          </template>

          <n-button type="primary" @click="submit" size="small" class="min-w-[50px] !hidden sm:!inline-flex" :loading="onGoingtaskCount >= maxTaskCount">
            <n-icon v-if="onGoingtaskCount < maxTaskCount" size="18">
              <PaperPlaneOutline />
            </n-icon>
          </n-button>
        </div>
        <div class="sm:hidden">
          <n-button type="primary" @click="submit" size="small" class="min-w-[50px]" :loading="onGoingtaskCount >= maxTaskCount">
            <n-icon v-if="onGoingtaskCount < maxTaskCount" size="18">
              <PaperPlaneOutline />
            </n-icon>
          </n-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'

import { NButton, NPopover, NIcon, NInput, useMessage } from 'naive-ui'
import { PaperPlaneOutline, ChevronForward, HelpCircleSharp, TrashOutline } from '@vicons/ionicons5'
import Add from '@/assets/aiwork/svg/add.svg'
import Change from '@/assets/ change.svg'
import Pan from '@/assets/pan.svg'

import AiPrompt from './AiPrompt.vue'
import CustomSettings from './CustomSettings.vue'
import ImageSize from './ImageSize.vue'
import SwapfaceImg from './SwapfaceImg.vue'

import { usePaintingStore } from '@/store'
import { storeToRefs } from 'pinia'
import { useBasicLayout } from '@/hooks/useBasicLayout'
const customSettingsRef = ref()
const $route = useRoute()
const $message = useMessage()
const { isMobile } = useBasicLayout()

const $emit = defineEmits(['submitPrompt'])

const { promptType } = defineProps({
  promptType: {
    type: String, // 任务类型 IMAGINE、DESCRIBE、BLEND、SWAP_FACE、SHORTEN
    default: 'IMAGINE',
  },
})

const maxImgLength = computed(() => {
  if (promptType == 'DESCRIBE') return 1
  if (promptType == 'BLEND') return 5
  return 0
})

const PaintingStore = usePaintingStore()

const { onGoingtaskCount, maxTaskCount } = storeToRefs(PaintingStore)

// 设置项集合
const settings = reactive<any>({
  speedMode: 'FAST',
  botType: 'MID_JOURNEY',
  customParams: '',
  dimensions: 'SQUARE', //2:3 PORTRAIT  1:1SQUARE 3:2LANDSCAPE
  sourceBase64: '',
  targetBase64: '',
})

let fileList = reactive<any[]>([])

const promptContent = ref('')

const useAiPrompt = (text) => {
  promptContent.value = text
}

const settingChange = ({ base64ImageList, customParams }) => {
  settings.customParams = customParams
  settings.base64ImageList = base64ImageList
}

const submit = () => {
  // scrollPageTo(10000000000000);

  if (onGoingtaskCount.value >= maxTaskCount.value) {
    return $message.warning(`当前用户最大并发数量为${maxTaskCount.value}`)
  }

  const { sourceBase64, targetBase64 } = settings

  const params = {
    prompt: promptContent.value,
    promptType,
    ...settings,
  }

  if (!promptContent.value && !(fileList && fileList.length) && (!sourceBase64 || !targetBase64)) {
    return $message.error('提示信息不能为空')
  }

  if (promptType == 'BLEND' && fileList.length < 2) {
    return $message.error('混图至少需要两张图片')
  }

  if (promptType == 'BLEND') {
    params.base64ImageList = fileList.map(({ url }) => url)
  } else if (promptType == 'SWAP_FACE') {
    params.dimensions = ''
  } else if (fileList && fileList.length) {
    params.base64Image = fileList[0].url
  }

  $emit('submitPrompt', params)

  promptContent.value = ''
  fileList.splice(0)
  settings.sourceBase64 = ''
  settings.targetBase64 = ''
  settings.customParams = ''
  settings.base64ImageList = null
  customSettingsRef?.value.initSettings()
}

// 选中图片
const fileChange = (e) => {
  let selectedFiles = Array.from(e.target.files)

  selectedFiles = selectedFiles.filter((file) => file.size / 1024 / 1024 <= 10)

  if (selectedFiles.length < e.target.files.length) {
    $message.warning('文件不得超过10M')
  }

  if (!selectedFiles.length) return

  selectedFiles.forEach((file) => {
    const reader = new FileReader()

    reader.onload = (e) => {
      const base64 = e.target.result
      fileList.push({
        id: Date.now().toString() + '-' + Math.random().toString().slice(-5),
        url: base64,
      })
    }

    reader.readAsDataURL(file)
  })
}
// 删除图片
const delImg = (index) => {
  fileList.splice(index, 1)
}

// 画同款参数
onMounted(() => {
  const { prompt } = $route.query
  promptContent.value = prompt || ''
})
</script>

<style lang="less" scoped></style>
