<template>
	<div class="h-full relative">
		<Right />
	</div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import Right from './layouts/Right.vue';
import { useCtx } from './ctx';
import { onMounted } from 'vue';

const route = useRoute()
const router = useRouter()
const ctx = useCtx()
const { type } = route.query as any

const useEditorType = localStorage.getItem('useEditorType') || 'v2'
if(useEditorType === 'v1') {
	router.replace({name: 'AppDetailV1', params: {id: route.params.id}, query: route.query})
}

onMounted(() => {
  ctx.$reset()
})
</script>
