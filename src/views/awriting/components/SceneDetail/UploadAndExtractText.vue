<script lang="ts" setup>
import { NUpload, NUploadDragger, UploadFileInfo, NSpin, useMessage } from 'naive-ui'
import mammoth from 'mammoth';

//@ts-ignore
import { SettledFileInfo } from 'naive-ui/es/upload/src/interface';
import { fileTypeFromBlob } from 'file-type';
import getFileType from '@/utils/fileType';
import { readPdfContent } from '@/utils/utils'
import { ref } from 'vue';

interface Props {
  placeHolder: string;
}
interface Emit {
  (e: 'file-parsed', content: string): void
}
const message = useMessage();
const props = defineProps<Props>()
const emit = defineEmits<Emit>()
const loading = ref(false);
const handleDocxFileExtract = async (arrayBuffer: ArrayBuffer) => {
  const text = await mammoth.extractRawText({ arrayBuffer });
  emit('file-parsed', text.value)
}
const handlePDFFileExtract = async (arrayBuffer: ArrayBuffer) => {
  const text = await readPdfContent(arrayBuffer);
  emit('file-parsed', text)
}
const handleTxtFileExtract = async (arrayBuffer: ArrayBuffer) => {
  const text = new TextDecoder().decode(arrayBuffer);
  emit('file-parsed', text)
}
const handleFileUpdateList = async ({ fileList }: { fileList: SettledFileInfo[] }) => {
  if (fileList && fileList.length > 0) {
    const file = fileList[0].file as File;
    loading.value = true;
    const arrayBuffer = await readFileBuffer(file);
    const type = await getFileType(file);

    try {
      if (type?.ext === 'pdf') {
        await handlePDFFileExtract(arrayBuffer);
      } else if (type?.ext === 'docx') {
        await handleDocxFileExtract(arrayBuffer);
      } else {
        await handleTxtFileExtract(arrayBuffer);
      }
    } catch (error) {
      console.error(error)
    } finally {
      loading.value = false;
    }
  }
}

const readFileBuffer = (file: File): Promise<ArrayBuffer> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => {
      if (reader.result) {
        resolve(reader.result as ArrayBuffer);
      } else {
        reject(new Error('Failed to read file buffer'));
      }
    };
    reader.onerror = () => {
      reject(new Error('Error reading file'));
    };
    reader.readAsArrayBuffer(file);
  });
}
// 5MB
const astrictFileSize = 5;
const handleBeforeUpload = ({ file }: { file: UploadFileInfo; fileList: UploadFileInfo[] }) => {
  const fileSize = parseFloat(((file.file?.size ?? 0) / 1024 / 1024).toFixed(1));
  if (fileSize > astrictFileSize) {
    message.error(`文件大小不能超过${astrictFileSize}MB`);
    return false;
  }
  return true;
};
</script>

<template>
  <n-spin :show="loading" class=" w-full">
    <n-upload multiple directory-dnd :max="1" class=" upload-and-extract-text-container"
      accept=".pdf, .docx, .txt, .md, .MD" @change="handleFileUpdateList!" @before-upload="handleBeforeUpload">
      <n-upload-dragger>
        <div class=" text-[16px] leading-[18px] text-[#3D3D3D]">
          将文件拖拽到此区域上传
        </div>
        <div
          class=" border-[1px] border-[#dcdcdc] bg-[#F9FAFF] w-[32%] min-w-[150px] text-[#818181] text-[12px] py-[4px] mx-[auto] mt-[8px] mb-[12px]">
          从本地文件夹上传
        </div>
        <div class=" text-[#818181] text-[10px] font-normal">
          仅支持.PDF/.DOCX/Markdown类文件，文件大小应小于5MB
        </div>
      </n-upload-dragger>
    </n-upload>
  </n-spin>
</template>

<style lang="less">
.upload-and-extract-text-container {
  .n-upload-dragger {
    padding-top: 15px;
    padding-bottom: 20px;
    background-color: #FFFFFF;
  }
}
</style>
