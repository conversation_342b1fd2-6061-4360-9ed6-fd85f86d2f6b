<template>
	<div ref="treeRef"
		class=" w-full h-[80%] border-[1px] border-[#d8d8d8] rounded-[4px] relative p-[15px] mt-[42px] overflow-y-scroll HideScrollbar"
		v-if="props.outline">
		<n-tree block-line :data="outline" :default-expand-all="true" expand-on-click show-line :render-label="renderLabel"
			:render-suffix="renderSuffix" />

	</div>
	<div v-else
		class="w-full h-[80%] border-[1px] border-[#d8d8d8] rounded-[4px] relative mt-[42px] flex flex-col gap-y-[9px] items-center justify-center">
		<span class="loader1"></span>
		<span class=" text-[#7C7C7C] text-[14px] leading-[18px] ">正在生成中...</span>
	</div>
</template>

<script lang="ts" setup>
import { markdown2TreeOutline, outline2markdown } from '@/utils/markdown/paper';
import { Edit } from '@icon-park/vue-next';
import { NButton, NTree, TreeOption } from 'naive-ui';
import { h, ref, watch } from 'vue';
import { useCtx } from '../../ctx';
interface Props {
	outline?: string
}
const props = withDefaults(defineProps<Props>(), {
	outline: ''
})
const ctx = useCtx()

const outline = ref<any>([])
const _propsOutlineMd = ref(props.outline)
const treeRef = ref<HTMLDivElement | null>(null)

watch(() => props.outline, (newVal) => {
	if (!newVal || newVal === _propsOutlineMd.value) return
	try {
		outline.value = outlineAddIndex(markdown2TreeOutline(newVal) as any[])
		_propsOutlineMd.value = newVal
		// 滚动到滚动条底部
		treeRef.value?.scrollTo(0, treeRef.value.scrollHeight)
	} catch (error) {
		console.error(error)
	}
}, {
	immediate: true
})

const renderLabel = ({ option }: { option: TreeOption }) => {
	return h('div', {
		contenteditable: true,
		id: option.key,
		// @ts-ignore
		'data-index': option.index?.join?.('.'),
		onClick: (e) => e.stopPropagation(),
		onBlur: (e) => handleChange(e)
		// onFocus: (e) => onChange(e.target as HTMLElement,),
		// onBlur: (e) => removeChange(e.target as HTMLElement)
	}, [
		h('span', { class: 'label' }, option.label),
		// h('label', { class: 'title', for: option.key }, [
		// 	h(Edit, { size: '14',  })
		// ])
	])
}

function renderSuffix({ option }: { option: TreeOption }) {
	return h(NButton, {
		quaternary: true,
		size: 'small',
		onClick: (e) => document.getElementById(option.key as string)?.focus()
	}, {
		icon: () => h(Edit, { size: '14', })
	})
}

const outlineAddIndex = (nodes: any[], indexArr = []) => {
	return nodes.map((item, index) => {
		item.index = [...indexArr, index]
		if (item.children?.length) {
			item.children = outlineAddIndex(item.children, item.index)
		}
		return item
	})
}

function handleChange(e) {
	const index = e.target.dataset.index
	const indexArr = index.split('.').map(Number)
	const value = e.target.innerText
	let node = outline.value[indexArr.shift()]
	while (indexArr.length) {
		node = node.children[indexArr.shift()]
	}
	node.label = value
	ctx.outline = outline2markdown(outline.value[0])
}

// const onChange = (ele: HTMLElement) => ele.addEventListener('input', handleChange)
// const removeChange = (ele: HTMLElement) => ele.removeEventListener('input', handleChange)
</script>



<style lang="less" scoped>
/* HTML: <div class="loader"></div> */
.loader1 {
	width: 40px;
	/* Increased the size for a larger center circle */
	aspect-ratio: 1;
	display: grid;
	border-radius: 50%;
	background:
		linear-gradient(0deg, rgb(14 105 255/50%) 20%, #0000 0 80%, rgb(14 105 255/100%) 0) 50%/8% 100%,
		/* Made the strips thinner */
		linear-gradient(90deg, rgb(14 105 255/20%) 20%, #0000 0 80%, rgb(14 105 255/75%) 0) 50%/100% 8%;
	/* Made the strips thinner */
	background-repeat: no-repeat;
	animation: l23 1s infinite steps(8);
}

.loader1::before,
.loader1::after {
	content: "";
	grid-area: 1/1;
	border-radius: 50%;
	background: inherit;
	opacity: 0.915;
	transform: rotate(45deg);
}

.loader1::after {
	opacity: 0.83;
	transform: rotate(90deg);
}

@keyframes l23 {
	100% {
		transform: rotate(1turn)
	}
}

[contenteditable]:focus {
	// border: 1px solid #4a90e2;
	padding: 8px;
	outline: 1px solid #4a90e2;
}

:deep(.n-tree-node-wrapper .n-tree-node-content__suffix) {
	opacity: 0;
}
:deep(.n-tree-node-wrapper:hover .n-tree-node-content__suffix) {
	opacity: 1;
}
</style>
