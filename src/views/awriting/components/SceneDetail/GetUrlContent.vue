<template>
  <div class=" w-full">
    <n-input v-model:value="urlRef" :placeholder="placeholder" class=" w-full"  style="--n-color:#F9FAFF" />
    <n-button type="primary" round @click="handleRun" style=" margin-top: 9px;" class=" mt-[9px]"
      :loading="loading">获取</n-button>
  </div>
</template>

<script lang="ts" setup>
import { getContentByUrl } from '@/chatgpt';
import { NSpin, NInput, NButton } from 'naive-ui'
import { ref } from 'vue';
import { useRequest } from 'vue-hooks-plus';


interface Props {
  placeholder: string;
}
interface Emit {
  (e: 'url-parsed', content: string): void
}
const props = defineProps<Props>()
const emit = defineEmits<Emit>()

const { run, loading } = useRequest(() => getContentByUrl({
  url: urlRef.value
}), {
  manual: true,
  onSuccess: (data) => {
    emit('url-parsed', data?.content)
  }

})
const urlRef = ref('')

const handleRun = () => {
  if (!urlRef.value) {
    return
  }
  run()
}
</script>



<style lang="less" scoped></style>
