.font-size-inherit {
	font-size: inherit;
}
.prose-base :where(p):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.25em;
	margin-bottom: 1.25em
}

.prose-base :where([class~=lead]):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: 1.25em;
	line-height: 1.6;
	margin-top: 1.2em;
	margin-bottom: 1.2em
}

.prose-base :where(blockquote):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.6em;
	margin-bottom: 1.6em;
	padding-inline-start:1em}

.prose-base :where(h1):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: 2.25em;
	margin-top: 0;
	margin-bottom: .8888889em;
	line-height: 1.1111111
}

.prose-base :where(h2):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: 1.5em;
	margin-top: 2em;
	margin-bottom: 1em;
	line-height: 1.3333333
}

.prose-base :where(h3):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: 1.25em;
	margin-top: 1.6em;
	margin-bottom: .6em;
	line-height: 1.6
}

.prose-base :where(h4):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.5em;
	margin-bottom: .5em;
	line-height: 1.5
}

.prose-base :where(img):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 2em;
	margin-bottom: 2em
}

.prose-base :where(picture):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 2em;
	margin-bottom: 2em
}

.prose-base :where(picture>img):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0;
	margin-bottom: 0
}

.prose-base :where(video):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 2em;
	margin-bottom: 2em
}

.prose-base :where(kbd):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .875em;
	border-radius: .3125rem;
	padding-top: .1875em;
	padding-inline-end:.375em;padding-bottom: .1875em;
	padding-inline-start:.375em}

.prose-base :where(code):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .875em
}

.prose-base :where(h2 code):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .875em
}

.prose-base :where(h3 code):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .9em
}

.prose-base :where(pre):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .875em;
	line-height: 1.7142857;
	margin-top: 1.7142857em;
	margin-bottom: 1.7142857em;
	border-radius: .375rem;
	padding-top: .8571429em;
	padding-inline-end:1.1428571em;padding-bottom: .8571429em;
	padding-inline-start:1.1428571em}

.prose-base :where(ol):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.25em;
	margin-bottom: 1.25em;
	padding-inline-start:1.625em}

.prose-base :where(ul):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.25em;
	margin-bottom: 1.25em;
	padding-inline-start:1.625em}

.prose-base :where(li):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: .5em;
	margin-bottom: .5em
}

.prose-base :where(ol>li):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-start:.375em}

.prose-base :where(ul>li):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-start:.375em}

.prose-base :where(.prose-base>ul>li p):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: .75em;
	margin-bottom: .75em
}

.prose-base :where(.prose-base>ul>li>p:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.25em
}

.prose-base :where(.prose-base>ul>li>p:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-bottom: 1.25em
}

.prose-base :where(.prose-base>ol>li>p:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.25em
}

.prose-base :where(.prose-base>ol>li>p:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-bottom: 1.25em
}

.prose-base :where(ul ul,ul ol,ol ul,ol ol):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: .75em;
	margin-bottom: .75em
}

.prose-base :where(dl):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.25em;
	margin-bottom: 1.25em
}

.prose-base :where(dt):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.25em
}

.prose-base :where(dd):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: .5em;
	padding-inline-start:1.625em}

.prose-base :where(hr):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 3em;
	margin-bottom: 3em
}

.prose-base :where(hr+*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-base :where(h2+*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-base :where(h3+*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-base :where(h4+*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-base :where(table):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .875em;
	line-height: 1.7142857
}

.prose-base :where(thead th):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-end:.5714286em;padding-bottom: .5714286em;
	padding-inline-start:.5714286em}

.prose-base :where(thead th:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-start:0}

.prose-base :where(thead th:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-end:0}

.prose-base :where(tbody td,tfoot td):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-top: .5714286em;
	padding-inline-end:.5714286em;padding-bottom: .5714286em;
	padding-inline-start:.5714286em}

.prose-base :where(tbody td:first-child,tfoot td:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-start:0}

.prose-base :where(tbody td:last-child,tfoot td:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-end:0}

.prose-base :where(figure):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 2em;
	margin-bottom: 2em
}

.prose-base :where(figure>*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0;
	margin-bottom: 0
}

.prose-base :where(figcaption):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .875em;
	line-height: 1.4285714;
	margin-top: .8571429em
}

.prose-base :where(.prose-base>:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-base :where(.prose-base>:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-bottom: 0
}

.prose-lg {
	font-size: 1.125rem;
	line-height: 1.7777778
}

.prose-lg :where(p):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.3333333em;
	margin-bottom: 1.3333333em
}

.prose-lg :where([class~=lead]):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: 1.2222222em;
	line-height: 1.4545455;
	margin-top: 1.0909091em;
	margin-bottom: 1.0909091em
}

.prose-lg :where(blockquote):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.6666667em;
	margin-bottom: 1.6666667em;
	padding-inline-start:1em}

.prose-lg :where(h1):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: 2.6666667em;
	margin-top: 0;
	margin-bottom: .8333333em;
	line-height: 1
}

.prose-lg :where(h2):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: 1.6666667em;
	margin-top: 1.8666667em;
	margin-bottom: 1.0666667em;
	line-height: 1.3333333
}

.prose-lg :where(h3):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: 1.3333333em;
	margin-top: 1.6666667em;
	margin-bottom: .6666667em;
	line-height: 1.5
}

.prose-lg :where(h4):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.7777778em;
	margin-bottom: .4444444em;
	line-height: 1.5555556
}

.prose-lg :where(img):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.7777778em;
	margin-bottom: 1.7777778em
}

.prose-lg :where(picture):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.7777778em;
	margin-bottom: 1.7777778em
}

.prose-lg :where(picture>img):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0;
	margin-bottom: 0
}

.prose-lg :where(video):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.7777778em;
	margin-bottom: 1.7777778em
}

.prose-lg :where(kbd):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .8888889em;
	border-radius: .3125rem;
	padding-top: .2222222em;
	padding-inline-end:.4444444em;padding-bottom: .2222222em;
	padding-inline-start:.4444444em}

.prose-lg :where(code):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .8888889em
}

.prose-lg :where(h2 code):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .8666667em
}

.prose-lg :where(h3 code):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .875em
}

.prose-lg :where(pre):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .8888889em;
	line-height: 1.75;
	margin-top: 2em;
	margin-bottom: 2em;
	border-radius: .375rem;
	padding-top: 1em;
	padding-inline-end:1.5em;padding-bottom: 1em;
	padding-inline-start:1.5em}

.prose-lg :where(ol):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.3333333em;
	margin-bottom: 1.3333333em;
	padding-inline-start:1.5555556em}

.prose-lg :where(ul):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.3333333em;
	margin-bottom: 1.3333333em;
	padding-inline-start:1.5555556em}

.prose-lg :where(li):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: .6666667em;
	margin-bottom: .6666667em
}

.prose-lg :where(ol>li):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-start:.4444444em}

.prose-lg :where(ul>li):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-start:.4444444em}

.prose-lg :where(.prose-lg>ul>li p):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: .8888889em;
	margin-bottom: .8888889em
}

.prose-lg :where(.prose-lg>ul>li>p:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.3333333em
}

.prose-lg :where(.prose-lg>ul>li>p:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-bottom: 1.3333333em
}

.prose-lg :where(.prose-lg>ol>li>p:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.3333333em
}

.prose-lg :where(.prose-lg>ol>li>p:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-bottom: 1.3333333em
}

.prose-lg :where(ul ul,ul ol,ol ul,ol ol):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: .8888889em;
	margin-bottom: .8888889em
}

.prose-lg :where(dl):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.3333333em;
	margin-bottom: 1.3333333em
}

.prose-lg :where(dt):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.3333333em
}

.prose-lg :where(dd):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: .6666667em;
	padding-inline-start:1.5555556em}

.prose-lg :where(hr):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 3.1111111em;
	margin-bottom: 3.1111111em
}

.prose-lg :where(hr+*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-lg :where(h2+*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-lg :where(h3+*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-lg :where(h4+*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-lg :where(table):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .8888889em;
	line-height: 1.5
}

.prose-lg :where(thead th):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-end:.75em;padding-bottom: .75em;
	padding-inline-start:.75em}

.prose-lg :where(thead th:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-start:0}

.prose-lg :where(thead th:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-end:0}

.prose-lg :where(tbody td,tfoot td):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-top: .75em;
	padding-inline-end:.75em;padding-bottom: .75em;
	padding-inline-start:.75em}

.prose-lg :where(tbody td:first-child,tfoot td:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-start:0}

.prose-lg :where(tbody td:last-child,tfoot td:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-end:0}

.prose-lg :where(figure):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.7777778em;
	margin-bottom: 1.7777778em
}

.prose-lg :where(figure>*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0;
	margin-bottom: 0
}

.prose-lg :where(figcaption):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .8888889em;
	line-height: 1.5;
	margin-top: 1em
}

.prose-lg :where(.prose-lg>:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-lg :where(.prose-lg>:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-bottom: 0
}

.prose-xl {
	font-size: 1.25rem;
	line-height: 1.8
}

.prose-xl :where(p):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.2em;
	margin-bottom: 1.2em
}

.prose-xl :where([class~=lead]):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: 1.2em;
	line-height: 1.5;
	margin-top: 1em;
	margin-bottom: 1em
}

.prose-xl :where(blockquote):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.6em;
	margin-bottom: 1.6em;
	padding-inline-start:1.0666667em}

.prose-xl :where(h1):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: 2.8em;
	margin-top: 0;
	margin-bottom: .8571429em;
	line-height: 1
}

.prose-xl :where(h2):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: 1.8em;
	margin-top: 1.5555556em;
	margin-bottom: .8888889em;
	line-height: 1.1111111
}

.prose-xl :where(h3):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: 1.5em;
	margin-top: 1.6em;
	margin-bottom: .6666667em;
	line-height: 1.3333333
}

.prose-xl :where(h4):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.8em;
	margin-bottom: .6em;
	line-height: 1.6
}

.prose-xl :where(img):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 2em;
	margin-bottom: 2em
}

.prose-xl :where(picture):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 2em;
	margin-bottom: 2em
}

.prose-xl :where(picture>img):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0;
	margin-bottom: 0
}

.prose-xl :where(video):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 2em;
	margin-bottom: 2em
}

.prose-xl :where(kbd):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .9em;
	border-radius: .3125rem;
	padding-top: .25em;
	padding-inline-end:.4em;padding-bottom: .25em;
	padding-inline-start:.4em}

.prose-xl :where(code):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .9em
}

.prose-xl :where(h2 code):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .8611111em
}

.prose-xl :where(h3 code):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .9em
}

.prose-xl :where(pre):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .9em;
	line-height: 1.7777778;
	margin-top: 2em;
	margin-bottom: 2em;
	border-radius: .5rem;
	padding-top: 1.1111111em;
	padding-inline-end:1.3333333em;padding-bottom: 1.1111111em;
	padding-inline-start:1.3333333em}

.prose-xl :where(ol):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.2em;
	margin-bottom: 1.2em;
	padding-inline-start:1.6em}

.prose-xl :where(ul):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.2em;
	margin-bottom: 1.2em;
	padding-inline-start:1.6em}

.prose-xl :where(li):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: .6em;
	margin-bottom: .6em
}

.prose-xl :where(ol>li):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-start:.4em}

.prose-xl :where(ul>li):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-start:.4em}

.prose-xl :where(.prose-xl>ul>li p):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: .8em;
	margin-bottom: .8em
}

.prose-xl :where(.prose-xl>ul>li>p:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.2em
}

.prose-xl :where(.prose-xl>ul>li>p:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-bottom: 1.2em
}

.prose-xl :where(.prose-xl>ol>li>p:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.2em
}

.prose-xl :where(.prose-xl>ol>li>p:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-bottom: 1.2em
}

.prose-xl :where(ul ul,ul ol,ol ul,ol ol):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: .8em;
	margin-bottom: .8em
}

.prose-xl :where(dl):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.2em;
	margin-bottom: 1.2em
}

.prose-xl :where(dt):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.2em
}

.prose-xl :where(dd):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: .6em;
	padding-inline-start:1.6em}

.prose-xl :where(hr):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 2.8em;
	margin-bottom: 2.8em
}

.prose-xl :where(hr+*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-xl :where(h2+*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-xl :where(h3+*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-xl :where(h4+*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-xl :where(table):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .9em;
	line-height: 1.5555556
}

.prose-xl :where(thead th):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-end:.6666667em;padding-bottom: .8888889em;
	padding-inline-start:.6666667em}

.prose-xl :where(thead th:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-start:0}

.prose-xl :where(thead th:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-end:0}

.prose-xl :where(tbody td,tfoot td):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-top: .8888889em;
	padding-inline-end:.6666667em;padding-bottom: .8888889em;
	padding-inline-start:.6666667em}

.prose-xl :where(tbody td:first-child,tfoot td:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-start:0}

.prose-xl :where(tbody td:last-child,tfoot td:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-end:0}

.prose-xl :where(figure):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 2em;
	margin-bottom: 2em
}

.prose-xl :where(figure>*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0;
	margin-bottom: 0
}

.prose-xl :where(figcaption):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .9em;
	line-height: 1.5555556;
	margin-top: 1em
}

.prose-xl :where(.prose-xl>:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-xl :where(.prose-xl>:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-bottom: 0
}

.prose-2xl {
	font-size: 1.5rem;
	line-height: 1.6666667
}

.prose-2xl :where(p):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.3333333em;
	margin-bottom: 1.3333333em
}

.prose-2xl :where([class~=lead]):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: 1.25em;
	line-height: 1.4666667;
	margin-top: 1.0666667em;
	margin-bottom: 1.0666667em
}

.prose-2xl :where(blockquote):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.7777778em;
	margin-bottom: 1.7777778em;
	padding-inline-start:1.1111111em}

.prose-2xl :where(h1):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: 2.6666667em;
	margin-top: 0;
	margin-bottom: .875em;
	line-height: 1
}

.prose-2xl :where(h2):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: 2em;
	margin-top: 1.5em;
	margin-bottom: .8333333em;
	line-height: 1.0833333
}

.prose-2xl :where(h3):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: 1.5em;
	margin-top: 1.5555556em;
	margin-bottom: .6666667em;
	line-height: 1.2222222
}

.prose-2xl :where(h4):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.6666667em;
	margin-bottom: .6666667em;
	line-height: 1.5
}

.prose-2xl :where(img):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 2em;
	margin-bottom: 2em
}

.prose-2xl :where(picture):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 2em;
	margin-bottom: 2em
}

.prose-2xl :where(picture>img):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0;
	margin-bottom: 0
}

.prose-2xl :where(video):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 2em;
	margin-bottom: 2em
}

.prose-2xl :where(kbd):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .8333333em;
	border-radius: .375rem;
	padding-top: .25em;
	padding-inline-end:.3333333em;padding-bottom: .25em;
	padding-inline-start:.3333333em}

.prose-2xl :where(code):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .8333333em
}

.prose-2xl :where(h2 code):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .875em
}

.prose-2xl :where(h3 code):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .8888889em
}

.prose-2xl :where(pre):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .8333333em;
	line-height: 1.8;
	margin-top: 2em;
	margin-bottom: 2em;
	border-radius: .5rem;
	padding-top: 1.2em;
	padding-inline-end:1.6em;padding-bottom: 1.2em;
	padding-inline-start:1.6em}

.prose-2xl :where(ol):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.3333333em;
	margin-bottom: 1.3333333em;
	padding-inline-start:1.5833333em}

.prose-2xl :where(ul):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.3333333em;
	margin-bottom: 1.3333333em;
	padding-inline-start:1.5833333em}

.prose-2xl :where(li):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: .5em;
	margin-bottom: .5em
}

.prose-2xl :where(ol>li):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-start:.4166667em}

.prose-2xl :where(ul>li):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-start:.4166667em}

.prose-2xl :where(.prose-2xl>ul>li p):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: .8333333em;
	margin-bottom: .8333333em
}

.prose-2xl :where(.prose-2xl>ul>li>p:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.3333333em
}

.prose-2xl :where(.prose-2xl>ul>li>p:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-bottom: 1.3333333em
}

.prose-2xl :where(.prose-2xl>ol>li>p:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.3333333em
}

.prose-2xl :where(.prose-2xl>ol>li>p:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-bottom: 1.3333333em
}

.prose-2xl :where(ul ul,ul ol,ol ul,ol ol):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: .6666667em;
	margin-bottom: .6666667em
}

.prose-2xl :where(dl):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.3333333em;
	margin-bottom: 1.3333333em
}

.prose-2xl :where(dt):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 1.3333333em
}

.prose-2xl :where(dd):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: .5em;
	padding-inline-start:1.5833333em}

.prose-2xl :where(hr):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 3em;
	margin-bottom: 3em
}

.prose-2xl :where(hr+*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-2xl :where(h2+*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-2xl :where(h3+*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-2xl :where(h4+*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-2xl :where(table):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .8333333em;
	line-height: 1.4
}

.prose-2xl :where(thead th):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-end:.6em;padding-bottom: .8em;
	padding-inline-start:.6em}

.prose-2xl :where(thead th:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-start:0}

.prose-2xl :where(thead th:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-end:0}

.prose-2xl :where(tbody td,tfoot td):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-top: .8em;
	padding-inline-end:.6em;padding-bottom: .8em;
	padding-inline-start:.6em}

.prose-2xl :where(tbody td:first-child,tfoot td:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-start:0}

.prose-2xl :where(tbody td:last-child,tfoot td:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	padding-inline-end:0}

.prose-2xl :where(figure):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 2em;
	margin-bottom: 2em
}

.prose-2xl :where(figure>*):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0;
	margin-bottom: 0
}

.prose-2xl :where(figcaption):not(:where([class~=not-prose],[class~=not-prose] *)) {
	font-size: .8333333em;
	line-height: 1.6;
	margin-top: 1em
}

.prose-2xl :where(.prose-2xl>:first-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-top: 0
}

.prose-2xl :where(.prose-2xl>:last-child):not(:where([class~=not-prose],[class~=not-prose] *)) {
	margin-bottom: 0
}
