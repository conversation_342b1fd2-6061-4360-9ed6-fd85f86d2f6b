<template>
	<div class="flex flex-col h-full gap-4">
		<NSpin v-if="loading || collectData.length" :show="loading">
			<div class="apps flex-1 min-h-0 overflow-y-auto grid grid-cols-2 row-auto auto-rows-max gap-2">
				<template v-for="item in collectData" :key="item.id">
					<div class="border border-neutral-200 hover:border-primary rounded-lg p-4 cursor-pointer"
						@click="handleBotClick(item)">
						<div class="flex items-center gap-2 text-sm">
							<NImage :src="item.profile" width="24px" height="24px" preview-disabled />
							<span class="flex-1">{{ item.name }}</span>
							<NTooltip>
								<template #trigger>
									<Like class=" cursor-pointer" :class="isCollected(item.id) ? 'text-pink-600' : ''" :theme="isCollected(item.id) ? 'filled' : 'outline'"
										@click.stop="() => isCollected(item.id) ? runUnCollectApp(item.id) : runCollectApp(item.id)" />
								</template>
								收藏
							</NTooltip>
						</div>
						<div class="text-xs text-neutral-600 mt-2">{{ item.welcome_message }}</div>
					</div>
				</template>
			</div>
		</NSpin>
		<div v-if="!loading && !collectData.length" class="flex-1 flex justify-center items-center">
			<NEmpty description="暂无数据" />
		</div>
	</div>
</template>

<script setup lang="ts">
import { useRequest } from 'vue-hooks-plus';
import { CollectApp } from '../types';
import { collectApp, getCollectedCreatebotList, unCollectApp } from '@/chatgpt';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import { NImage, NSpin, NTooltip, useMessage, NEmpty } from 'naive-ui';
import { getUser } from '@/store/modules/auth/helper';
import { Like } from '@icon-park/vue-next';

const emit = defineEmits<{
	close: []
}>()

const router = useRouter()
const message = useMessage()
const user = getUser()

const collectData = ref<any>([])


const { run: runGetCollectedCreatebotList, loading } = useRequest<CollectApp[]>(() => getCollectedCreatebotList({}), {
	manual: true,
	onSuccess: (data) => {
		collectData.value = data
	},
	onError(e, params) {
		console.error(e, params)
	},
})

onMounted(() => runGetCollectedCreatebotList())

const handleBotClick = (item) => {
	if (item.href) return window.open(item.href)
	router.push(`/apps/${item.categoryId}?appid=${item.id}&type=${item.type}`)
	emit('close')
}

const isCollected = (appid) => {
	return collectData.value?.some(item => item.id == Number(appid))
}

const { run: runUnCollectApp } = useRequest<{ status: Boolean }>((appid) => unCollectApp({
	id: appid
}), {
	manual: true,
	onSuccess: (data) => {
		if (data?.status) {
			message.success('取消收藏成功')
			runGetCollectedCreatebotList()
		}
	}
})
const { run: runCollectApp } = useRequest<{ status: Boolean }>((appid) => collectApp({
	id: appid
}), {
	ready: user.type !== 'temp',
	manual: true,
	onSuccess: (data) => {
		if (data?.status) {
			message.success('收藏成功')
			runGetCollectedCreatebotList()
		}
	}
})
</script>
