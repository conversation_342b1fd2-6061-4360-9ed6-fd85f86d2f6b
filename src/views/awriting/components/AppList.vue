<template>
	<div class="flex flex-col h-full gap-4">
		<!-- <div>搜索</div> -->
		<NSpin v-if="loading || filterBots.length" :show="loading">
			<div class="categories relative flex items-end">
				<div class="flex flex-wrap gap-1 gap-y-1 flex-1 overflow-hidden transition-all duration-300"
					:class="showAllCategories ? 'h-fit' : 'h-[34px]'">
					<NButton :quaternary="filterCategoryId !== null" :secondary="filterCategoryId === null"
						@click="filterCategoryId = null">全部</NButton>
					<NButton :quaternary="filterCategoryId !== item.id" :secondary="filterCategoryId === item.id"
						v-for="item in categories" :key="item.id" @click="filterCategoryId = item.id">{{ item.name }}</NButton>
				</div>
				<div class="w-[34px] h-[34px] cursor-pointer rounded-md hover:bg-neutral-300 flex justify-center items-center"
					:class="{ 'rotate-180': showAllCategories }" @click="showAllCategories = !showAllCategories">
					<DoubleDown />
				</div>
			</div>
			<div class="apps flex-1 min-h-0 overflow-y-auto grid grid-cols-2  auto-rows-max gap-2 mt-4">
				<template v-for="item in filterBots" :key="item.categoryId+ ''+ item.id">
					<div class="border border-neutral-200 hover:border-primary rounded-lg p-4 cursor-pointer"
						@click="handleBotClick(item)">
						<div class="flex items-center gap-2 text-sm">
							<NImage :src="item.profile" width="24px" height="24px" preview-disabled />
							<span class="flex-1">{{ item.name }}</span>
							<NTooltip>
								<template #trigger>
									<Like class=" cursor-pointer" :class="isCollected(item.id) ? 'text-pink-600' : ''" :theme="isCollected(item.id) ? 'filled' : 'outline'"
										@click.stop="handleToggleCollect(item)" />
								</template>
								收藏
							</NTooltip>
						</div>
						<div class="text-xs text-neutral-600 mt-2">{{ item.welcome_message }}</div>
					</div>
				</template>
			</div>
		</NSpin>
		<div v-if="!loading && !filterBots.length" class="flex-1 flex justify-center items-center">
			<NEmpty description="暂无数据"></NEmpty>
		</div>
	</div>
</template>

<script setup lang="ts">
import { useRequest } from 'vue-hooks-plus';
import { CollectApp, CreateCategory } from '../types';
import { collectApp, getCategoryAndCreatesByCategoryId, getCollectedCreatebotList, unCollectApp } from '@/chatgpt';
import { computed, onMounted, ref } from 'vue';
import { NButton, NEmpty, NImage, NSpin, NTooltip, useMessage } from 'naive-ui';
import { useRouter } from 'vue-router';
import { DoubleDown, Like } from '@icon-park/vue-next';
import { useUserStore } from '@/store';
import { getUser } from '@/store/modules/auth/helper';

const emit = defineEmits<{
	close: []
}>()

const router = useRouter()
const user = getUser()

const categories = ref<any>([])
const filterCategoryId = ref<any>(null)
const filterKeyword = ref('')
const showAllCategories = ref(false)
const collectData = ref<any>([])
const message = useMessage()
// const bots = ref<any>([])

const { run, loading } = useRequest<CreateCategory[]>(getCategoryAndCreatesByCategoryId, {
	// ready: !!id,
	manual: true,
	onSuccess: data => {
		data.forEach(item => {
			item.Createbots.forEach(i => {
				i.categoryId = item.id
			})
		})
		categories.value = data
		// bots.value = data.flatMap(category => category.Createbots)
	}
})

onMounted(() => {
	run()
})

const filterBots = computed(() => {
	if (!filterCategoryId.value) {
		return categories.value.flatMap(category => category.Createbots)
	} else {
		return categories.value.find(category => category.id === filterCategoryId.value)?.Createbots || []
	}
})

const handleBotClick = (item) => {
	if (item.href) return window.open(item.href)
	router.push(`/apps/${item.categoryId}?appid=${item.id}&type=${item.type}`)
	emit('close')
}

const { run: runGetCollectedCreatebotList } = useRequest<CollectApp[]>(() => getCollectedCreatebotList({}), {
	manual: true,
	onSuccess: (data) => {
		collectData.value = data
	},
	onError(e, params) {
		console.error(e, params)
	},
})

onMounted(() => runGetCollectedCreatebotList())

const isCollected = (appid) => {
	return collectData.value?.some(item => item.id == Number(appid))
}

const { run: runUnCollectApp } = useRequest<{ status: Boolean }>((appid) => unCollectApp({
	id: appid
}), {
	manual: true,
	onSuccess: (data) => {
		if (data?.status) {
			message.success('取消收藏成功')
			runGetCollectedCreatebotList()
		}
	}
})
const { run: runCollectApp } = useRequest<{ status: Boolean }>((appid) => collectApp({
	id: appid
}), {
	// ready: user.type !== 'temp',
	manual: true,
	onSuccess: (data) => {
		if (data?.status) {
			message.success('收藏成功')
			runGetCollectedCreatebotList()
		}
	}
})
const handleToggleCollect = async (item) => {
	if (isCollected(item.id)) {
		runUnCollectApp(item.id)
	} else {
		if(getUser().type == 'temp') await window.$aiwork.openLogin()
		runCollectApp(item.id)
	}
}
</script>

<style lang="less" scoped>
.categories {
	interpolate-size: allow-keywords;
}
</style>
