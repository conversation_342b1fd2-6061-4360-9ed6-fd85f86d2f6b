import { Editor } from "@tiptap/vue-3";
import { Injection<PERSON>ey, Ref } from "vue";

export interface WritingContext {
  editor?: Editor
  step?: Ref<number>
  updateStep?: (step:number) => void
  contentLoading?: Ref<boolean>
  cancelHandler?: () => void
}


export const WRITING_CONTEXT:InjectionKey<Ref<WritingContext>> = Symbol('WRITING_CONTEXT')

export const WRITING_CONTEXT_UPDATE = Symbol() as InjectionKey<(param: Partial<WritingContext>) => void>
