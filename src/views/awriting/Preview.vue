<template>
	<div class=" flex-1 min-h-0 h-full overflow-y-auto">
		<div class="w-4/5 max-w-[1000px] mx-auto">
			<div class=" sticky top-0 w-full p-4 flex justify-between items-center">
				<div></div>
				<div class="flex items-center">
					<NButton type="primary" @click="toEditor">
						<template #icon>
							<Share />
						</template>
						去创作
					</NButton>
				</div>
			</div>
			<div class="mt-4">
				<template v-if="examples?.images?.length" v-for="item in examples?.images" :key="item">
					<img :src="item" alt="" class="w-full block" />
				</template>
				<div v-if="content" v-html="content" class="ProseMirror text-[17px] !pt-0"></div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import post from '@/utils/request';
import { Share } from '@icon-park/vue-next';
import { marked } from 'marked';
import { NButton } from 'naive-ui';
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const content = ref('')
const examples = ref<any>()

const router = useRouter()
const route = useRoute()

onMounted(() => {
	if(route.query.shareId) loadContent()
	if(route.query.id) loadExample()
})

const loadContent = async () => {
	if (!route.query.shareId) return
	try {
		const result = await post({
			url: '/api3/aiwork/document/detail',
			data: {
				id: route.query.shareId
			}
		})
		content.value = await marked.parse(result.content, { breaks: true })
	} catch (error) {
		console.error(error)
	}
}

const loadExample = async () => {
	if (!route.query.id) return
	try {
		const result = await post({
			url: '/api3/aiwork/chatbots/example',
			data: {
				id: route.query.id
			}
		})
		examples.value = result
	} catch (error) {
		console.error(error)
	}
}

const toEditor = () => {
	if(content) router.push({name: 'AppEditor'})
	if(examples.value.images?.length) router.push(examples.value.href)
}
</script>

<style lang="less">
@import "@/components/AIEditor/index.css";

</style>
