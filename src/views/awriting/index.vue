<template>
	<div class="h-full">
		<div v-if="type == 'long'" class="h-full relative">
			<div
				class="w-full h-full bg-white absolute left-0 top-0 rounded overflow-auto transition-all duration-300 ease-in-out origin-[0%_50%] "
				:class="ctx.step == 1 ? ' z-10' : 'translate-x-0 w-[400px]'"
				>
				<Left />
			</div>
			<div
				class="w-full h-full bg-white absolute right-0 top-0 rounded transition-all duration-300 ease-in-out origin-[100%_50%]"
				:class="ctx.step == 2 ? ' z-10 w-[calc(100%-80px)]' : 'translate-x-0 cursor-pointer'"
				@click="ctx.changeStep(2)">
				<Right />
			</div>
		</div>
		<div v-else  class="h-full relative">
			<Left>
				<Right />
			</Left>
		</div>
	</div>
</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import Left from './layouts/Left.vue';
import Right from './layouts/Right.vue';
import { useCtx } from './ctx';
import { onMounted } from 'vue';

const route = useRoute()
const router = useRouter()
const ctx = useCtx()
const { type } = route.query as any

const useEditorType = localStorage.getItem('useEditorType') || 'v2'
if(useEditorType === 'v1') {
	router.replace({name: 'AppDetailV1', params: {id: route.params.id}, query: route.query})
}

onMounted(() => {
  ctx.$reset()
	if(route.query.step) ctx.step = Number(route.query.step)
})
</script>
