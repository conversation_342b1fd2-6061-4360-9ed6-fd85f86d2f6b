<template>
	<div class=" flex items-stretch h-full w-full bg-[#fff] px-0 gap-y-[30px] relative sm:flex-col ipad:flex-col">
		<div class=" pt-[30px] border-r border-neutral-200 transition-all duration-300 relative sm:w-full"
			:class="openConfig ? 'w-[640px] ipad:w-full' : 'w-0'">
			<div class="w-full h-full overflow-x-hidden">
				<div class="flex flex-col gap-6 h-full w-[640px] px-[60px] ipad:w-full">
					<div class="create-header-info flex flex-row min-h-[47px] gap-x-[13px]">
						<NImage preview-disabled :src="currentApps.profile" class="rounded-[50%] w-[47px] h-[47px]" />
						<div class="flex flex-col gap-y-[8px] flex-1">
							<div class="text-[16px] text-[#3d3d3d] leading-[21px] flex items-center gap-2">
								<span>{{ replaceText(currentApps.name) }}</span>
								<NButton secondary strong size="tiny" type="info" round>
									<template #icon>
										<DeepSeekSvg />
									</template>
									已接入DeepSeek-R1满血版
								</NButton>
							</div>
							<div class=" text-[14px] text-[#676767] leading-[18px] flex justify-between items-center">
								<span>{{ replaceText(currentApps.welcome_message) }}</span>
								<div v-if="showHelpInputContent"
									class="flex px-3 gap-1 h-[30px] items-center justify-center text-white font-medium bg-gradient-to-r from-[#3456FF] to-[#AB8FFF] cursor-pointer text-sm rounded-3xl"
									@click="handleHelpMeWrite">
									<span>帮我写</span>
									<PenSvg class="w-3 h-3" />
								</div>
							</div>
						</div>
					</div>
					<DynamicForm ref="formRef" :list="currentApps.formList" :create-bot-id="appid" :type="type"
						:loading="contentLoading!" :is-network="currentApps.isNetwork" :is-knowledge="currentApps.isKnowledge"
						:outline-content="outline" :outline-id="outlineId" :outline-loading="outlineLoading"
						:content-loading="generateLongArticleContentLoading" @create="handleCreateForm"
						@generate-content="handleGenerateLongContent" @generate-outline="runGenerateLongArticleOutline"
						@re-generate-content="handleGenerateLongContent" />
				</div>
			</div>
			<div v-if="type !== 'long'" class=" bg-primary w-[1px] h-full absolute right-0 top-0 z-20 ipad:hidden">
				<div
					class="w-[20px] h-[40px] border bg-white border-primary absolute right-0 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-xl flex justify-center items-center cursor-pointer text-primary"
					:class="openConfig ? '' : 'left-0 translate-x-0 rounded-l-none'" @click="openConfig = !openConfig">
					<DoubleLeft :class="openConfig ? '' : 'rotate-180'" />
				</div>
			</div>
		</div>
		<slot>
			<div class="flex-1 flex justify-center items-center flex-col overflow-x-hidden sm:min-h-screen">
				<div>
					<!-- <h2 class="text-primary italic text-[24px] leading-[30px] text-center mb-4 font-medium">仅需3步 内容创作+办公提效轻松搞定</h2> -->
					<img src="@/assets/aiwork/images/new-create-demo-title.png" class="w-[421px] mx-auto" />
					<div class="mt-8 mx-20 flex justify-center items-center gap-4">
						<div
							class="w-[240px] rounded-md bg-gradient-to-b from-[#F0F6FF] to-[#fff] p-6 flex flex-col items-center justify-center gap-4 font-medium">
							<img src="@/assets/aiwork/images/new-create-demo.1.png" class="w-full" />
							<div>01. 输入主要信息</div>
						</div>
						<div
							class="w-[240px] rounded-md bg-gradient-to-b from-[#F0F6FF] to-[#fff] p-6 flex flex-col items-center justify-center gap-4 font-medium">
							<img src="@/assets/aiwork/images/new-create-demo.2.png" class="w-full" />
							<div>02. AI生成大纲</div>
						</div>
						<div
							class="w-[240px] rounded-md bg-gradient-to-b from-[#F0F6FF] to-[#fff] p-6 flex flex-col items-center justify-center gap-4 font-medium">
							<img src="@/assets/aiwork/images/new-create-demo.3.png" class="w-full" />
							<div>03. AI生成文章</div>
						</div>
					</div>
				</div>
				<div class="mt-10 w-full">
					<div class=" w-[300px] mx-auto">
						<NDivider><span class="text-xs text-neutral-500 font-normal">万字长文秒出炉 创作效率飞跃10倍</span></NDivider>
					</div>
					<div class=" overflow-hidden mx-auto w-[740px] mt-4 pb-8">
						<NCarousel slides-per-view="auto" :space-between="20" :loop="false" draggable
							style="--n-dot-color:rgba(0,0,0,.3);--n-dot-color-focus: rgba(0,0,0,.5); --n-dot-color-active: rgba(0,0,0,.8);"
							dot-type="line">
							<template v-for="(item, index) in examples" :key="item.title">
								<NCarouselItem class="p-4 rounded-md flex flex-col gap-2 justify-center items-center"
									:class="index % 2 ? 'bg-pink-200/20' : 'bg-purple-300/20'" style="width: 200px;"
									@click="router.push(`/apps/preview?id=${item.id}`)">
									<NImage class="w-full border rounded overflow-hidden"
										:class="index % 2 ? 'border-pink-200' : 'border-purple-200'" preview-disabled
										:src="item.cover" />
									<span class="text-[10px] whitespace-nowrap overflow-hidden overflow-ellipsis text-neutral-600">{{
										item.title }}</span>
								</NCarouselItem>
							</template>
						</NCarousel>
					</div>
				</div>
				<div class="!absolute top-4 right-4 flex items-center gap-1">
					<NButton @click="handleUseEditorV1">使用v1编辑器</NButton>
					<NTooltip>
						<template #trigger>
							<NButton quaternary @click="ctx.changeStep(2)">
								<template #icon>
									<RightSmall />
								</template>
							</NButton>
						</template>
						前往编辑器
					</NTooltip>
				</div>
			</div>
		</slot>
	</div>

</template>

<script setup lang="ts">
import { useRoute, useRouter } from 'vue-router';
import DynamicForm from '../components/SceneDetail/DynamicForm.vue'
import { computed, nextTick, provide, ref, watch } from 'vue';
import { ChatProps, CreateBot } from '../types';
import { useRequest } from 'vue-hooks-plus';
import { fetchChatAPIProcess, fetchChatAPIProcess2, generateLongArticleOutline2, getAppDetailByAppId, riskControl } from '@/chatgpt';
import { replaceText } from '@/plugins/directive';
import { idID, NButton, NCarousel, NCarouselItem, NDivider, NImage, NTooltip, useMessage } from 'naive-ui';
import { editorHandles } from '@/components/AIEditor/tools/handle';
import { captchaInit } from '@/utils/functions';
import { useAuthStore } from '@/store';
import { CtxEventType, eventBus, useCtx } from '../ctx';
import { Editor } from '@tiptap/vue-3';
import { useGpt, useLongContentGpt } from '@/hooks/useGpt';
import { SelfRequestCode } from '@/utils/request';
import { storeToRefs } from 'pinia';
import { useEditorCtx } from '@/components/AIEditor/editorCtx';
import { DoubleLeft, RightSmall } from '@icon-park/vue-next';
import DeepSeekSvg from '@/assets/aiwork/svg/deepseek.svg'
import PenSvg from '@/assets/aiwork/svg/pen.svg'

const route = useRoute()
const { appid, type = "app" } = route.query as unknown as { appid: string, type: string }
const router = useRouter()
const message = useMessage()
const authStore = useAuthStore()
const ctx = useCtx()
const { contentLoading } = storeToRefs(ctx)
const editorCtx = useEditorCtx()
const openConfig = ref(true)

const currentApps = ref<Partial<CreateBot>>({})
let controller = new AbortController()
let uuid: number;
const dataSources = ref<any[]>([])
const deepseek = ref(false)
const curId = ref(Number(appid))
const outlineId = ref<number | null>(null)
const formRef = ref<any>(null)
const examples = ref<any[]>([])

// watch(() => ctx.step, (v) => {
// 	if(v== 2) openConfig.value = false
// })

useRequest(() => getAppDetailByAppId<CreateBot>({ id: appid }), {
	onSuccess: (data) => {
		if (data.type == 'long' && type != 'long') {
			if (editorCtx.content) sessionStorage.setItem('writing_history_detail', editorCtx.content)
			nextTick(() => router.replace({ name: 'AppDetail', params: { id: route.params.id }, query: { ...route.query, type: 'long' } }))
			return
		}
		currentApps.value = data
		examples.value = data.examples || []
	}
})

const handleCreateForm = async (values: any[], { openThink, isDeepseek, knowledgeId }: any = {}) => {
	editorHandles.clearContent({ editor: editorCtx.editor as Editor })
	editorCtx.reset()
	await createForm(values, { openThink, isDeepseek, knowledgeId })
}

const createForm = async (values: any[], { openThink, isDeepseek, knowledgeId }: any = {}) => {
	if (ctx.contentLoading) return;
	controller = new AbortController()
	uuid = Date.now()
	dataSources.value = []
	ctx.changeContentLoading(true)
	try {
		deepseek.value = isDeepseek
		const fetchChatAPIOnce = async (captchaData = {}) => {
			ctx.changeContentLoading(true)
			ctx.changeStep(2)
			await fetchChatAPIProcess<any>({
				prompt: '',
				...captchaData,
				createbotId: Number(curId.value),
				formData: values,
				signal: controller.signal,
				isDeepseek,
				knowledgeId,
				openThink,
				// responseType: 'stream',
				onDownloadProgress: ({ event }) => {
					const xhr = event.target
					const { responseText } = xhr
					let isLimit = false
					try {
						if (responseText) {
							isLimit = JSON.parse(responseText)?.data?.isLimit
						}
					} catch (error) { }

					try {
						const chunkArr = responseText.split('\n') as any[]
						const arrs = chunkArr.map(item => JSON.parse(item) || {})
						const content = arrs.map(item => item.text).join('')
						const thinks = arrs.map(item => item.reasoning_content).join('')
						ctx.setContent(content, thinks)
						const lastItem = JSON.parse(chunkArr.at(-1))
						const isCompute = lastItem?.detail?.choices?.[0]?.finish_reason;
						if (isCompute === 'stop') {
							ctx.changeContentLoading(false)
							controller.abort()
						}
						if(lastItem.chatId) ctx.chatId = lastItem.chatId
					} catch (error) {
						console.error(error, responseText);
					}
				},
			})
		}
		const isCaptcha: any = await riskControl('apps');
		if (isCaptcha.captcha) {
			captchaInit(authStore.captchaAppId, async function (captchaData: any) {
				if (captchaData && captchaData.randstr) {
					try {
						await fetchChatAPIOnce(captchaData)
					} catch (error) {
						failHandler(error)
					}
				} else {
					failHandler(captchaData)
				}
			})
		} else {
			try {
				await fetchChatAPIOnce()
			} catch (error) {
				if ((error as unknown as Error).message === 'canceled') {
					return cancelHandler()
				}
				failHandler(error)
			}
		}
	} catch (error: any) {
		failHandler(error)
	}
	finally {
		// contentLoading.value = false
		ctx.changeContentLoading(false)
	}
}

const cancelHandler = () => {
	controller?.abort?.()
	// contentLoading.value = false
	ctx.changeContentLoading(false)
}
eventBus.on(CtxEventType.STOP_CONTENT_GENERATING, cancelHandler)

const failHandler = (error: any) => {
	const errorMessage = error?.message
	const isLimit = error.data?.isLimit ? error.data?.isLimit : false
	updateChat({
		text: errorMessage,
		inversion: false,
		loading: false,
		uuid,
		error: true,
		isLimit,
		isCompute: false
	})
}

const updateChat = ({ loading, ...rest }: ChatProps) => {
	const index = dataSources.value.findIndex(item => item.uuid === uuid)
	if (index === -1) {
		dataSources.value.push(rest)
	} else {
		dataSources.value[index] = rest
	}
	if (!loading) {
		// contentLoading.value = false
		ctx.changeContentLoading(false)
	}
}

const { run: runGenerateLongArticleContent, streamingText: longContent, thinkingText: longThinkingContent, loading: generateLongArticleContentLoading, cancel: generateLongArticleContentCancel } = useLongContentGpt({
	api: fetchChatAPIProcess2,
	onSuccess: () => {
	},
	onError: () => {
		message.error('生成失败')
	}
})
eventBus.on(CtxEventType.STOP_CONTENT_GENERATING, generateLongArticleContentCancel)

watch([
	() => longContent.value,
	() => longThinkingContent.value,
	() => generateLongArticleContentLoading.value
], ([content, thinkingContent, loading]) => {
	if (loading) ctx.changeStep(2)
	// if(v) ctx.editor?.commands.setContent(v)
	if (content || thinkingContent) {
		ctx.setContent(content, thinkingContent)
	}
	editorCtx.disabled = loading
})
watch(() => generateLongArticleContentLoading.value, (v) => {
	ctx.contentLoading = v
})

const { run: runGenerateLongArticleOutline, loading: outlineLoading, streamingText: outline, cancel: outlineCancel } = useGpt({
	api: generateLongArticleOutline2,
	onSuccess: (data: string) => {
		try {
			const lines = data.trim().split('\n');
			const { outlineId: otid } = JSON.parse(lines[0]);
			outlineId.value = otid
			ctx.outlineId = otid
			ctx.chatId = otid // 长文的聊天ID用大纲id
			outlineCancel();
		} catch (error) {
			console.error(error)
		} finally {
			outlineCancel();
		}
	},
	onError: (error) => {
		if (error.errcode === SelfRequestCode.NeedPay) {
			window.$aiwork.openRecharge?.({ type: "ai" });
			message.error('余额不足，请充值')
			return
		}
		outlineCancel();
		message.error('生成失败')
	}
})

watch(() => outline.value, (v) => {
	ctx.outline = v
})
watch(() => outlineLoading.value, (v) => {
	ctx.outlineLoading = v
})

const handleGenerateLongContent = () => {
	// markdownAreaRef.value.handleCreateNewFile()
	// ctx.editor?.commands.setContent('')
	setTimeout(() => {
		editorCtx.reset()
		runGenerateLongArticleContent({
			outlineId: outlineId.value as number,
			outline: ctx.outline
		})
	}, 300)
}

const showHelpInputContent = computed(() => {
	return !!currentApps.value.formList?.some(item => item.defaultContent) && currentApps.value.type == 'long'
})

const handleHelpMeWrite = () => {
	formRef.value?.handleHelpMeWrite()
}

const handleUseEditorV1 = () => {
	localStorage.setItem('useEditorType', 'v1')
	router.replace({ name: 'AppDetailV1', params: { id: route.params.id }, query: route.query })
	// window.location.reload()
}

</script>

<style scoped>
/* :deep(.n-carousel__dots) {
	bottom: -16px !important;
} */
</style>
