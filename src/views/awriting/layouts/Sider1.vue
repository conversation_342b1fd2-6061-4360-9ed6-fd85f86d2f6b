<template>
	<div class="h-full relative bg-[#FAFCFF] flex">
		<div v-if="step == 1 || type != 'long'" class="w-[85px] h-full flex flex-col gap-3 items-center justify-start p-4 text-[#3d3d3d] grow-0 shrink-0">
			<div class="flex flex-col gap-1 text-xs items-center cursor-pointer py-2 hover:text-primary"
				@click="openSub('app')">
				<ApplicationTwo size="20" />
				<span>应用</span>
			</div>
			<div class="flex flex-col gap-1 text-xs items-center cursor-pointer py-2 hover:text-primary" @click="openSub('favorite')">
				<FolderClose size="20" />
				<span>收藏夹</span>
			</div>
			<!-- <div class="flex flex-col gap-1 text-xs items-center cursor-pointer py-2 hover:text-primary"
				@click="handleGotoHistory">
				<DocDetail size="20" />
				<span>创作历史</span>
			</div> -->
			<div v-if="route.name !== 'WritingDocList'" class="flex flex-col gap-1 text-xs items-center cursor-pointer py-2 hover:text-primary"
				@click="handleGotoDocList">
				<DocDetail size="20" />
				<span>创作历史</span>
			</div>
		</div>
		<div
			class="overflow-x-hidden overflow-y-auto transition-all duration-300 ease-out bg-white relative grow-0 shrink-0"
			:class="showSub ? 'w-[460px] p-4' : 'w-0 p-0'">
			<AppList v-if="showSub && subType === 'app'" @close="closeSub" />
			<Favorite v-if="showSub && subType === 'favorite'" @close="closeSub" />
		</div>
		<div v-if="showSub" class=" bg-primary w-[1px] h-full absolute right-0 top-0 z-20">
			<div class="w-[20px] h-[40px] border bg-white border-primary absolute right-0 top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 rounded-xl flex justify-center items-center cursor-pointer text-primary" @click="closeSub">
				<DoubleLeft />
			</div>
		</div>
	</div>

</template>

<script setup lang="ts">
import { getUser } from '@/store/modules/auth/helper';
import { AiworkType } from '@/typings';
import { ApplicationTwo, DocDetail, DoubleLeft, FolderClose } from '@icon-park/vue-next';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import AppList from '../components/AppList.vue';
import Favorite from '../components/Favorite.vue';
import { useCtx } from '../ctx';
import { storeToRefs } from 'pinia';

const router = useRouter()
const route = useRoute()
const { id } = route.params
const {type} = route.query
const showSub = ref(false)
const subType = ref<'app' | 'favorite' | null>()
const ctx = useCtx()
const {step} = storeToRefs(ctx)

const handleGotoHistory = async () => {
	const u = getUser()
	if (u.type === 'temp') await (window.$aiwork as AiworkType).openLogin?.()
	sessionStorage.setItem('writing_history_redirect_url', route.fullPath.replace('&step=2', '&step=1'))
	router.push({ name: 'WritingHistory' })
}

const handleGotoDocList = async () => {
	const u = getUser()
	if (u.type === 'temp') await (window.$aiwork as AiworkType).openLogin?.()
	// sessionStorage.setItem('writing_history_redirect_url', route.fullPath.replace('&step=2', '&step=1'))
	router.push({ name: 'WritingDocList' })
}

const openSub = async (type) => {

	if(type === 'favorite' && getUser().type === 'temp') {
		await window.$aiwork.openLogin()
	}

	if (showSub.value) {
		if (subType.value === type) return closeSub()
		return subType.value = type
	}
	else {
		showSub.value = true
		subType.value = type
	}
}

const closeSub = () => {
	showSub.value = false
	subType.value = null
}

</script>
