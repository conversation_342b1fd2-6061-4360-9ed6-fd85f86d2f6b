<template>
	<div class="flex flex-col h-full relative flex-1 sm:min-h-screen">
		<div class="border-b border-neutral-50 px-4 py-2 flex justify-between">
			<div class="flex gap-1 items-center flex-nowrap flex-1 min-w-0">
				<NTooltip v-if="type == 'long'">
					<template #trigger>
						<NButton quaternary @click.stop="goBack">
							<template #icon>
								<Return />
							</template>
							返回
						</NButton>
					</template>
					返回上一步
				</NTooltip>
				<h1 class="flex-1 ml-2 font-bold whitespace-nowrap overflow-hidden text-ellipsis max-w-[80%]">{{ title }}</h1>
			</div>
			<div class="flex items-center">
				<NButton v-if="showType === 'editor'" strong secondary class="!mx-1" @click="showType = 'poster'"
					:disabled="editorCtx.disabled">
					<template #icon>
						<PosterSvg />
					</template>
					海报
				</NButton>
				<NButton v-if="showType === 'poster'" strong secondary class="!mx-1" @click="showType = 'editor'">
					<template #icon>
						<EditTwo />
					</template>
					编辑
				</NButton>
				<NButton v-if="outline" strong secondary class="!mx-1" @click="handleExportPPT" :disabled="editorCtx.disabled">
					<template #icon>
						<IconPPt />
					</template>
					{{ ispptGenerated ? '我的PPT' : '生成PPT' }}
				</NButton>
				<NPopover placement="bottom" :disabled="editorCtx.disabled">
					<template #trigger>
						<NButton strong secondary :disabled="editorCtx.disabled">
							<template #icon>
								<Export />
							</template>
							导出
						</NButton>
					</template>
					<div class="block-full-button">
						<NButton quaternary block @click="handleExport('txt')">
							Txt
						</NButton>
						<NButton quaternary block @click="handleExport('markdown')">
							Markdown
						</NButton>
						<NButton quaternary block @click="handleExport('word')">
							Word
						</NButton>
					</div>
				</NPopover>
				<!-- <NPopover trigger="hover" :show="menuPopVisible" @update:show="menuPopVisible = $event">
					<template #trigger>
						<NButton quaternary>
							<template #icon>
								<HamburgerButton />
							</template>
						</NButton>
					</template>
					<div class="block-full-button">
						<NButton quaternary block @click="handleGotoHistory">
							<template #icon>
								<History />
							</template>
							历史记录
						</NButton>
						<NButton quaternary block @click="handleGotoHistory">
							<template #icon>
								<ImportAndExport />
							</template>
							导入文档
						</NButton>

						<NPopover placement="left-start">
							<template #trigger>
								<NButton quaternary block>
									<template #icon>
										<Export />
									</template>
									导出文档
								</NButton>
							</template>
							<div class="block-full-button">
								<NButton quaternary block @click="handleExport('txt')">
									Txt
								</NButton>
								<NButton quaternary block @click="handleExport('markdown')">
									Markdown
								</NButton>
								<NButton quaternary block @click="handleExport('word')">
									Word
								</NButton>
							</div>
						</NPopover>
					</div>
				</NPopover> -->
			</div>
		</div>
		<div class="flex-1 min-h-0 h-full">

			<AIEditor v-show="showType === 'editor'" :key="route.query.appid as string"
				:placeholder="contentLoading ? '' : `请先完善左侧区域表单信息, 然后点击'开始创作'来进行创作吧`" :bubble-tools="bubbleToolsDefault"
				:toolbars="toolbarsDefault">
				<template #contentHeader>
					<div v-if="contentLoading && !editorCtx.content && !editorCtx.thinkContent"
						class="max-w-[80%] mt-4 mx-auto text-neutral-500">
						<NButton quaternary loading style="--n-text-color: #ccc;">生成中...</NButton>
					</div>
				</template>
				<template #footer>
					<div v-if="!contentLoading && !editorCtx.editor?.isEmpty"
						class="flex justify-between items-center gap-1 text-[12px] text-neutral-400 p-2 mx-10">
						<div class="flex items-center gap-1">
							<Info />
							<span>本内容由AI生成，仅供参考，不代表我方立场或观点，甄别使用</span>
						</div>
						<div class="flex items-center gap-2">
							<span v-if="autoSaveInfo">最近保存: {{ autoSaveInfo }}</span>
							<span>字数: {{ editorCtx.editor?.$doc.textContent.length || 0 }}</span>
						</div>
					</div>
				</template>
			</AIEditor>
			<Poster v-if="showType === 'poster'" :markdown-content="handleSwitchToPoster()"
				@onBack="() => showType = 'editor'" />
		</div>
		<div v-if="contentLoading" class="absolute bottom-10 left-1/2 -translate-x-1/2 flex justify-center">
			<NButton class="" type="info" size="small" ghost @click="ctx.stopContentGenerating()" style="--n-color:#fff;">
				停止
				<NImage :src="Dot" preview-disabled width="14" height="14" class=" ml-3" />
			</NButton>
		</div>
		<PPTTemplate v-model:visible="pptTemplateVisible" @select="handleSelectPPTTemplate" />
	</div>
</template>

<script setup lang="ts">
import AIEditor from '@/components/AIEditor/index.vue'
import { computed, nextTick, onMounted, onUnmounted, provide, ref, watch } from 'vue';
import { NButton, NImage, NPopover, NTooltip, useDialog, useMessage } from 'naive-ui';
import { Return, Add, Star, History, Undo, Redo, Info, Clear, HamburgerButton, ImportAndExport, Export, EditTwo } from '@icon-park/vue-next';
import { editorHandles } from '@/components/AIEditor/tools/handle';
import Dot from '@/assets/images/dot.png'
import { useCtx } from '../ctx';
import { Editor } from '@tiptap/vue-3';
import { bubbleToolsDefault } from '@/components/AIEditor/BubbleTool/bubbleToolDefault';
import { getUser } from '@/store/modules/auth/helper';
import { AiworkType } from '@/typings';
import { useRoute, useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
import { marked } from 'marked';
import { useEditorCtx } from '@/components/AIEditor/editorCtx';
import { toolbarsDefault } from '@/components/AIEditor/Toolbar/toolbarsDefault';
import PosterSvg from '@/assets/aiwork/svg/poster.svg'
import Poster from '../components/Poster.vue';
import { useRequest } from 'vue-hooks-plus';
import { generatePPT } from '@/chatgpt';
import PPTTemplate from '../components/PPTTemplate.vue';
import { unified } from 'unified';
import markdown from "remark-parse";
import docx from "remark-docx";
import { saveAs } from "file-saver";
import post from '@/utils/request';
import { Timeout } from 'vue-hooks-plus/lib/useRequest/types';
import dayjs from 'dayjs';
import { useUserStore } from '@/store';

const ctx = useCtx()
const { contentLoading } = storeToRefs(ctx)
const editorCtx = useEditorCtx()
const { title } = storeToRefs(editorCtx)
const route = useRoute()
const router = useRouter()
const { id } = route.params
const { type } = route.query
const dialog = useDialog()
const message = useMessage()

const showType = ref<'editor' | 'poster'>('editor')
const pptTemplateVisible = ref(false)
const ispptGenerated = ref(false)
const { outline, outlineId, outlineLoading } = storeToRefs(ctx)
const autoSaveInfo = ref('')


const menuPopVisible = ref(false)
const userStore = useUserStore()

onMounted(() => {
	if (route.query.docId) loadDoc()
	const text = sessionStorage.getItem('writing_history_detail')
	if (text && Number(route.query.step) == 2) {
		editorCtx.setContent(text)
		sessionStorage.removeItem('writing_history_detail')
	}
	editorCtx.saveDocument = saveChat
	// if(route.query.docId) {
	// 	ctx.id = Number(route.query.docId)
	// 	editorCtx.id = Number(route.query.docId)
	// }
})

provide('onEditorUpdate', (content, editorCtx) => {
	handleAutoSave()
})

const loadDoc = async () => {
	try {
		const result = await post({
			url: '/api3/aiwork/document/detail',
			data: {
				id: route.query.docId
			}
		})
		editorCtx.setContent(result.content)
		title.value = result.title
		ctx.id = result.id
		ctx.chatId = result.chatId
		editorCtx.id = result.id
	} catch (error) {
		console.error(error)
	}
}

const handleGotoHistory = async () => {
	const u = getUser()
	if (u.type === 'temp') await (window.$aiwork as AiworkType).openLogin?.()
	sessionStorage.setItem('writing_history_redirect_url', route.fullPath.replace('&step=2', '&step=1'))
	router.push({ name: 'WritingHistory', query: { id, appid: route.query.appid } })
}

const goBack = () => {
	if (ctx.contentLoading) {
		return dialog.warning({
			title: '内容正在生成中',
			content: '内容正在生成中，确定要返回吗？',
			positiveText: '确定',
			negativeText: '取消',
			// draggable: true,
			onPositiveClick: () => {
				ctx.stopContentGenerating?.()
				setTimeout(() => {
					ctx.changeStep(1)
				}, 300)
				// message.success('确定')
			},
			onNegativeClick: () => {
				// message.error('不确定')
			}
		})
	}

	ctx.changeStep(1)
}

const handleSwitchToPoster = () => {
	// @ts-ignore
	const markdownContent = editorCtx.editor?.storage.markdown.getMarkdown()
	return markdownContent
}

const autoSaveTimer = ref<Timeout | null>(null)
const handleAutoSave = () => {

	if (autoSaveTimer.value) clearTimeout(autoSaveTimer.value)
	autoSaveTimer.value = setTimeout(() => {
		saveChat()
	}, 3000)
}

onUnmounted(() => {
	if (autoSaveTimer.value) clearTimeout(autoSaveTimer.value)
	saveChat({leave: true})
})

const saveChat = async ({ leave = false }: { leave?: boolean } = {}) => {
	try {
		if(!userStore.userInfo?.uid) return
		if (editorCtx.editor?.isEmpty) return
		const result = await post({
			url: '/api3/aiwork/document/save',
			data: {
				id: ctx.id,
				// @ts-ignore
				content: editorCtx.editor?.storage.markdown.getMarkdown(),
				title: editorCtx.title || editorCtx.editor?.$pos(1).textContent?.slice(0, 30) || '未命名文档',
				chatId: ctx.chatId
			}
		}, { level: 0 })
		autoSaveInfo.value = dayjs().format('YYYY.MM.DD HH:mm')
		ctx.id = result.id
		editorCtx.id = result.id
		if (result.id && !route.query.docId && !leave) {
			const url = window.location.href + (window.location.href.includes('?') ? '&' : '?') + `docId=${result.id}`
			window.history.replaceState({}, '', url)
		}
	} catch (error) {
		console.error(error)
	}
}

const handleExportPPT = () => {
	if (ispptGenerated.value) {
		router.push('/ppt/workspace/home')
	} else {
		pptTemplateVisible.value = true
	}
}

const { run: runGeneratePPT } = useRequest(generatePPT, {
	manual: true,
	onSuccess: (res, params) => {
		const { href } = res
		// window.location.href = href
		window.open(href, '_blank')
		ispptGenerated.value = true
	},
	onError: (error) => {
		// @ts-ignore
		message.error(error.message || error?.errmsg || '生成失败')
	}
})

const handleGeneratePPT = (id: string) => {
	if (!outlineId!.value) {
		message.error('请先生成大纲')
		return
	}
	if (!ispptGenerated.value) {
		runGeneratePPT({
			chatId: String(outlineId!.value || ''),
			templateId: id
		})
	} else {
		// 已生成PPT的情况下 直接跳转workspace home
		router.push('/ppt/workspace/home')
	}
}

const handleSelectPPTTemplate = (id: string) => {
	// emit('generate-ppt', id)
	handleGeneratePPT(id)
	pptTemplateVisible.value = false
}

const handleExport = (type) => {
	switch (type) {
		case 'word':
			// editorCtx.exportWord()
			handleExportDocx()
			break
		case 'markdown':
			handleExportMarkdown()
			break
		case 'txt':
			handleExportTxt()
			break
	}
}

const handleExportDocx = async () => {
	if (!editorCtx.editor?.isEmpty) {
		// richtextRef.value.handlerExportDocx()
		const processor = unified().use(markdown).use(docx, { output: "blob" });
		// @ts-ignore
		const doc = await processor.process(editorCtx.editor?.storage.markdown.getMarkdown());
		const blob = await doc.result;
		saveAs(blob, "content.docx");
		message.success('导出成功')
	} else {
		message.error('请先输入内容')
	}
}

const handleExportMarkdown = () => {
	if (!editorCtx.editor?.isEmpty) {
		// @ts-ignore
		const markdownContent = editorCtx.editor?.storage.markdown.getMarkdown()
		const blob = new Blob([markdownContent], { type: "text/plain;charset=utf-8" });
		saveAs(blob, "content.md");
		message.success('导出成功')
	} else {
		message.error('请先输入内容')
	}
}

const handleExportTxt = () => {
	if (!editorCtx.editor?.isEmpty) {
		const txtContent = editorCtx.editor?.getText()!
		const blob = new Blob([txtContent], { type: "text/plain;charset=utf-8" });
		saveAs(blob, "content.txt");
		message.success('导出成功')
	} else {
		message.error('请先输入内容')
	}
}

</script>
