<template>
  <div class="w-[250px] shrink-0 grow-0 h-full border-r border-neutral-100 relative flex flex-col p-4">
    <NInput placeholder="请输入" class="w-full mb-2" :value="searchWord"
      @input="(v) => searchWord = v">
      <template #prefix>
        <IconSearch />
      </template>
    </NInput>

    <div class="flex-1 min-h-0 overflow-y-auto py-4 pt-2">
      <RouterLink :to="`/apps/history`">
        <NButton :quaternary="!!route.query.appid" :secondary="!route.query.appid" block size="large"
          :type="!route.query.appid ? 'primary' : 'default'" class="!justify-start group/item"
          style="--n-icon-size: 28px;" @click="searchWord = ''">
          <template #icon>
            <ApplicationTwo size="20" theme="outline" />
          </template>
          全部
        </NButton>
      </RouterLink>
      <template v-for="(category, categoryIndex) in filterCategories" :key="category.id">
        <div class="sider-group group" :class="{ 'sider-group_expand': category.expand }">
          <NButton v-if="category?.Createbots?.find(item => item.show)" :key="category.id" quaternary block size="large"
            class="!justify-start mb-2" @click="() => handleChangeExpand(category)">
            <template #icon>
              <NImage :src="category.icon" width="24px" height="24px" preview-disabled />
            </template>
            <div class="flex-1 flex justify-between w-full">
              <span>{{ category.name }}</span>
              <Down theme="outline" class="text-primary transition-all duration-300"
                :class="{ ' rotate-180': !category.expand }" />
            </div>
          </NButton>
          <div class="flex flex-col gap-2 transition-all duration-300 overflow-hidden"
            :class="category.expand ? 'max-h-[5000px]' : 'max-h-0'">
            <template v-for="(item, index) in category.Createbots" :key="item.id">
              <RouterLink v-if="(item.type === 'app' || item.type === 'long') && item.show"
                :to="`/apps/history?id=${category.id}&appid=${item.id}`">
                <NButton :quaternary="!item.active" :secondary="item.active" block size="large"
                  :type="item.active ? 'primary' : 'default'" class="!justify-start group/item"
                  style="--n-icon-size: 32px;">
                  <template #icon>
                    <NImage :src="item.profile" width="24px" height="24px" preview-disabled />
                  </template>
                  <div class="flex-1 flex justify-between w-full items-center">
                    <span>{{ item.name }}</span>
                  </div>
                </NButton>
              </RouterLink>
            </template>
          </div>
        </div>
      </template>

    </div>
    <NDivider />
    <div class="">
      <NButton quaternary block size="large" class="!justify-start" @click="backToWriting">
        <template #icon>
          <Return />
        </template>
        返回创作
      </NButton>
    </div>
  </div>
</template>

<script setup lang='ts'>
import { getCategoryAndCreatesByCategoryId } from '@/chatgpt';
import { NImage, NInput, NButton, NDivider } from 'naive-ui';
import { computed, onMounted, ref, watch } from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { RouterLink, useRoute, useRouter } from 'vue-router';
import type { CreateCategory } from '../types';
import { ApplicationTwo, Down, Return } from '@icon-park/vue-next';

const route = useRoute()
const router = useRouter()
const { id, appid } = route.query
const categories = ref<any>([])
const searchWord = ref('')

const { run } = useRequest<CreateCategory[]>(getCategoryAndCreatesByCategoryId, {
  manual: true,
  onSuccess: data => {
    categories.value = data
    // bots.value = data.flatMap(category => category.Createbots)
  }
})
onMounted(() => {
  run({})
})

const filterCategories = computed(() => {
  return categories.value.map((category) => {
    return {
      ...category,
      expand: category.id == route.query.id ? true : (category.expand ?? false),
      Createbots: category.Createbots.map(bot => {
        return {
          ...bot,
          show: searchWord ? bot.name.indexOf(searchWord.value) !== -1 : true,
          active: bot.id == route.query.appid
        }
      })
    }
  })
})

const backToWriting = () => {
  const redirectUrl = sessionStorage.getItem('writing_history_redirect_url')
  if (redirectUrl) {
    sessionStorage.removeItem('writing_history_redirect_url')
    router.push(redirectUrl)
  } else {
		router.back()
    // router.push('/apps')
  }
}

const handleChangeExpand = (item) => {
  const index = categories.value.findIndex(_item => item.id == _item.id)
  categories.value[index] = {
    ...categories.value[index],
    expand: categories.value[index].expand == undefined ? true : !categories.value[index].expand
  }
}

</script>




<style scoped lang="less">
@media screen and (max-width: 767px) {
  :deep(.trigger.trigger) {
    top: 25px;
  }

  :deep(.collapsed-trigger.collapsed-trigger) {
    top: 25px;
    right: -25px;
    width: 30px;
    height: 30px;
    transition: all 0.3s;
  }
}

:deep(.n-button__content) {
  flex: 1;
}
</style>
