<template>
  <div class="w-[250px] shrink-0 grow-0 h-full border-r border-neutral-100 relative flex flex-col p-4">
    <div class="flex justify-center items-center gap-2 text-neutral-400 mb-2">
      <NTooltip>
        <template #trigger>
          <NButton quaternary @click="tab=0">
            <template #icon>
              <ApplicationTwo theme="outline" :class="tab==0 ? 'text-primary': 'text-neutral-400'" />
            </template>
          </NButton>
        </template>
        全部应用
      </NTooltip>
      <span>|</span>
      <NTooltip>
        <template #trigger>
          <NButton quaternary @click="tab=1">
            <template #icon>
              <Star theme="outline"  :class="tab==1 ? 'text-primary': 'text-neutral-400'" />
            </template>
          </NButton>
        </template>
        收藏夹
      </NTooltip>
    </div>
    <NInput placeholder="请输入" class="w-full mb-2" @input="(v) => searchWord = v">
      <template #prefix>
        <IconSearch />
      </template>
    </NInput>

    <div class="flex-1 min-h-0 overflow-y-auto py-4 pt-2">
      <!-- <template v-for="(category, categoryIndex) in filterCategories" :key="categoryIndex">
        <div class="sider-group group"
          :class="{ 'sider-group_expand': category.expand }"> -->
      <!-- 折叠面板, 感觉不需要, 先注释 -->
      <!-- <NButton v-if="category?.Createbots?.find(item => item.show)" quaternary block size="large"
            class="!justify-start mb-2" @click="handleChangeExpand(category)">
            <template #icon>
              <NImage :src="category.icon" width="24px" height="24px" preview-disabled />
            </template>
            <div class="flex-1 flex justify-between w-full">
              <span>{{ category.name }}</span>
              <Down theme="outline" class="text-primary transition-all duration-300"
                :class="{ ' rotate-180': !category.expand }" />
            </div>
          </NButton> -->
      <div class="flex flex-col gap-2 transition-all duration-300 overflow-hidden">
        <template v-for="(item, index) in filterCategories" :key="item.id">
          <RouterLink v-if="(item.type === 'app' || item.type === 'long') && item.show"
            :to="`/apps/${id}?appid=${item.id}&type=${item.type}`">
            <NButton :quaternary="!item.active" :secondary="item.active" block size="large"
              :type="item.active ? 'primary' : 'default'" class="!justify-start group/item"
              style="--n-icon-size: 32px;">
              <template #icon>
                <NImage :src="item.profile" width="24px" height="24px" preview-disabled />
              </template>
              <div class="flex-1 flex justify-between w-full items-center">
                <span>{{ item.name }}</span>
                <NPopover placement="right-start">
                  <template #trigger>
                    <NButton quaternary class="-mr-[18px] opacity-0 group-hover/item:opacity-100 !bg-transparent">
                      <template #icon>
                        <More />
                      </template>
                    </NButton>
                  </template>
                  <div class="flex flex-col">
                    <NButton v-if="!isCollected(item.id)" quaternary block @click="runCollectApp(item.id)">
                      <template #icon>
                        <Star theme="outline" />
                      </template>
                      收藏
                    </NButton>
                    <NButton v-if="isCollected(item.id)" quaternary block @click="runUnCollectApp(item.id)">
                      <template #icon>
                        <Star theme="filled" />
                      </template>
                      取消收藏
                    </NButton>
                    <NButton v-if="!isPin(item.id)" quaternary block @click="pin(item.id)">
                      <template #icon>
                        <Pin theme="outline" />
                      </template>
                      置顶
                    </NButton>
                    <NButton v-if="isPin(item.id)" quaternary block @click="unPin(item.id)">
                      <template #icon>
                        <Pushpin theme="filled" />
                      </template>
                      取消置顶
                    </NButton>
                  </div>
                </NPopover>
              </div>
            </NButton>
          </RouterLink>
        </template>
      </div>
      <!-- </div>
      </template> -->

    </div>
    <NDivider />
    <div class="">
      <!-- <NButton quaternary block size="large" class="!justify-start" @click="handleGotoCollection">
        <template #icon>
          <NImage :src="CollectionImg" width="24px" height="24px" preview-disabled />
        </template>
        收藏夹
      </NButton> -->
      <NButton quaternary block size="large" class="!justify-start" @click="handleGotoHistory">
        <template #icon>
          <NImage :src="HistoryImg" width="26px" height="26px" preview-disabled />
        </template>
        创作历史
      </NButton>
    </div>
  </div>
</template>

<script setup lang='ts'>
import { collectApp, getCategoryAndCreatesByCategoryId, getCollectedCreatebotList, unCollectApp } from '@/chatgpt';
import { NImage, NInput, NButton, NDivider, NPopover, useMessage, NTooltip } from 'naive-ui';
import { computed, ComputedRef, onMounted, ref, watch } from 'vue';
import { useRequest } from 'vue-hooks-plus';
import { RouterLink, useRoute, useRouter } from 'vue-router';
import type { CollectApp, CreateCategory } from '../types';
import { AiworkType } from '@/typings';
import { getUser } from '@/store/modules/auth/helper';
import HistoryImg from '@/assets/History.png';
import CollectionImg from '@/assets/collection.png';
import { ApplicationTwo, Down, More, Pin, Pushpin, Star } from '@icon-park/vue-next';
import { useUserStore } from '@/store';
import { ss } from '@/utils/storage';

const route = useRoute()
const router = useRouter()
const message = useMessage()
const { id } = route.params
const user = getUser()
const userStore = useUserStore()
// const categories = ref<any>([])
const searchWord = ref('')
const collectData = ref<any>([])
const APP_PIN_LIST = 'APP_PIN_LIST'
const pinList = ref(ss.get(APP_PIN_LIST) ?? [])
const bots = ref<any>([])
const tab = ref<number>(0)

const { run } = useRequest<CreateCategory[]>(getCategoryAndCreatesByCategoryId, {
  ready: !!id,
  manual: true,
  onSuccess: data => {
    // categories.value = data
    bots.value = data.flatMap(category => category.Createbots)
  }
})
onMounted(() => {
  if (id) {
    run({ categoryId: id })
  }
})

const filterCategories = computed(() => {
  // return categories.value.map((category) => {
  //   return {
  //     ...category,
  //     expand: category.expand ?? true,
  //     Createbots: category.Createbots.map(bot => {
  //       return {
  //         ...bot,
  //         show: searchWord ? bot.name.indexOf(searchWord.value) !== -1 : true,
  //         active: bot.id == route.query.appid
  //       }
  //     })
  //   }
  // })
  // const bots = categories.value.flatMap(category => category.Createbots)
  const _bots = [bots, collectData][tab.value]
  let _arr: any[] = []
  pinList.value.map(appid => {
    const find = _bots.value.find(bot => bot.id == appid)
    find && _arr.push(find)
  })
  _bots.value.map(item => {
    if (!_arr.find(t => t.id == item.id)) {
      _arr.push(item)
    }
  })
  _arr = _arr.map(item => ({
    ...item,
    show: searchWord ? item.name.indexOf(searchWord.value) !== -1 : true,
    active: item.id == route.query.appid
  }))
  return _arr
})

const handleGotoHistory = async () => {
  const u = getUser()
  if (u.type === 'temp') await (window.$aiwork as AiworkType).openLogin?.()
  sessionStorage.setItem('writing_history_redirect_url', route.fullPath)
  router.push({ name: 'WritingHistory' })
}
const handleGotoCollection = async () => {
  const u = getUser()
  if (u.type === 'temp') await (window.$aiwork as AiworkType).openLogin?.()
  router.push({ name: 'Collection' })
}

// const handleChangeExpand = (item) => {
//   const index = categories.value.findIndex(_item => item.id = _item.id)
//   categories.value[index] = {
//     ...categories.value[index],
//     expand: categories.value[index].expand == undefined ? false : !categories.value[index].expand
//   }
// }

const { run: runUnCollectApp } = useRequest<{ status: Boolean }>((appid) => unCollectApp({
  id: appid
}), {
  manual: true,
  onSuccess: (data) => {
    if (data?.status) {
      message.success('取消收藏成功')
      runGetCollectedCreatebotList()
    }
  }
})
const { run: runCollectApp } = useRequest<{ status: Boolean }>((appid) => collectApp({
  id: appid
}), {
  ready: user.type !== 'temp',
  manual: true,
  onSuccess: (data) => {
    if (data?.status) {
      message.success('收藏成功')
      runGetCollectedCreatebotList()
    }
  }
})

const { run: runGetCollectedCreatebotList } = useRequest<CollectApp[]>(() => getCollectedCreatebotList({}), {
  manual: true,
  onSuccess: (data) => {
    collectData.value = data
  },
  onError(e, params) {
    console.error(e, params)
  },
})

watch(() => userStore.userInfo, (user) => {
  if (user.uid) runGetCollectedCreatebotList()
}, {
  immediate: true
})

const isCollected = (appid) => {
  return collectData.value?.some(item => item.id == Number(appid))
}

const pin = (appid) => {
  pinList.value.unshift(appid)
  ss.set(APP_PIN_LIST, pinList.value)
}
const unPin = appid => {
  pinList.value = pinList.value.filter(item => item != appid)
  ss.set(APP_PIN_LIST, pinList.value)
}
const isPin = appid => pinList.value.includes(appid)
</script>




<style scoped lang="less">
@media screen and (max-width: 767px) {
  :deep(.trigger.trigger) {
    top: 25px;
  }

  :deep(.collapsed-trigger.collapsed-trigger) {
    top: 25px;
    right: -25px;
    width: 30px;
    height: 30px;
    transition: all 0.3s;
  }
}

:deep(.n-button__content) {
  flex: 1;
}
</style>
