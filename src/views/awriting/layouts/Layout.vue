<script setup lang='ts'>
// import { computed } from 'vue'
// import { NLayout, NLayoutHeader, NLayoutContent, NLayoutFooter } from 'naive-ui'
// import { Header, CopyRight } from '@/components/common'
import Header1 from '@/components/common/Header1/index.vue'
import Sider from './Sider1.vue'
import { useBasicLayout } from '@/hooks/useBasicLayout'
import { useRoute } from 'vue-router'
import SiderHistory from './SiderHistory.vue'
// import { useAppStore } from '@/store'
// import { useRoute } from 'vue-router'
// const appStore = useAppStore()

// const route = useRoute()

const { isMobile, isIpad } = useBasicLayout()
const route = useRoute()
</script>

<template>
  <div class="h-full dark:bg-[#24272e] transition-all" :class="[isMobile || isIpad ? 'p-0 overflow-x-hidden' : 'p-0']">
    <div class="h-full flex flex-col">
      <Header1 />
      <div class="flex-1 min-h-0 mt-[64px] flex items-stretch w-full">
				<SiderHistory v-if="route.name == 'WritingHistory'" />
				<div v-else-if="route.name == 'WritingPreview'"></div>
        <Sider v-else />

        <div class="flex-1 bg-[#f5f7ff] w-full h-full">
          <RouterView v-slot="{ Component, route }">
            <component :is="Component" :key="route.fullPath" />
          </RouterView>
        </div>
      </div>
    </div>
  </div>
</template>
