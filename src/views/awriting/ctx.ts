import { useEditorCtx } from '@/components/AIEditor/editorCtx'
import { Editor } from '@tiptap/vue-3'
import { defineStore } from 'pinia'

import { reactive } from 'vue'
import { Timeout } from 'vue-hooks-plus/lib/useRequest/types'

export enum CtxEventType {
  STOP_CONTENT_GENERATING = 'stopContentGenerating',
}

export const eventBus = reactive({
  listeners: {},
  on(event: CtxEventType, callback) {
    if (!this.listeners[event]) this.listeners[event] = []
    this.listeners[event].push(callback)
  },
  emit(event: CtxEventType, payload) {
    this.listeners[event]?.forEach((callback) => callback(payload))
  },
})

export interface WritingContext {
  // editor?: Editor | null
  prompt?: string
  step?: number
  updateStep?: (step: number) => void
  contentLoading?: boolean
  cancelHandler?: () => void
  content?: string
  outline?: string
  outlineId?: number | null
  outlineLoading?: boolean
  id?: number // 文档id
  chatId?: number // 应用id
}

let setContentTimer: Timeout | null = null

export const useCtx = defineStore('writingContext', {
  state: (): WritingContext => ({
    // editor: null,
    prompt: '',
    step: 1,
    contentLoading: false,
    content: '', // 注意这里的只是生产的内容，不是编辑器内容
    outline: '',
    outlineId: null,
    outlineLoading: false,
    id: undefined,
    chatId: undefined,
  }),
  actions: {
    reset() {
      this.$reset()
    },
    add(obj) {
      this.$patch((state) => (state = { ...state, ...obj }))
    },

    changeStep(num) {
      this.step = num
    },
    changeContentLoading(bool) {
      useEditorCtx().disabled = bool
      this.$patch((state) => (state.contentLoading = bool))
    },
    stopContentGenerating() {
      eventBus.emit(CtxEventType.STOP_CONTENT_GENERATING, false)
    },
    setContent(content: string, thinkContent?: string) {
      content = content?.replace(/^```/g, '').replace(/```$/, '')
      const editorCtx = useEditorCtx()
      editorCtx.setContent(content, thinkContent)
      if (this.contentLoading && editorCtx.editorScrollElement) editorCtx.editorScrollElement.scrollTop = editorCtx.editorScrollElement?.scrollHeight

      if (setContentTimer) clearTimeout(setContentTimer)
      setContentTimer = setTimeout(() => {
        editorCtx.id = undefined
				this.id = undefined
        // 移除url中的docId参数
        const urlObj = new URL(window.location.href)
        urlObj.searchParams.delete('docId')
        const url = urlObj.href

        window.history.replaceState({}, '', url)
      }, 300)
    },
  },
})
