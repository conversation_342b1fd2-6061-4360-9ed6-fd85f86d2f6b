<template>
	<div class="p-4 flex-1 h-full min-h-0 flex flex-col">
		<div class="py-4 flex justify-between">
			<div>
				<NButton quaternary block size="small" class="!justify-start" @click="backToWriting">
					<template #icon>
						<Return />
					</template>
					返回创作
				</NButton>
			</div>
			<div>
				<NInput placeholder="搜索名称" v-model:value="searchParams.searchWord" class=" !w-[240px]"
					@input="v => searchParams.searchWord = v" @keydown.enter="run">
					<template #suffix>
						<Search class="cursor-pointer" @click="run" />
					</template>
				</NInput>
			</div>
		</div>
		<NSpin :show="loading">
			<div v-if="data.length || loading" class="flex-1 h-full overflow-y-auto">

				<NCard v-for="(item, index) in data" :key="index" class="mt-[10px] first-of-type:mt-0">
					<div class=" mb-3 flex items-center gap-2">
						<NTag v-if="item.Createbots.name" size="small" type="info">{{ item.Createbots.name }}</NTag>
						<span class="text-[#999]">{{ dayjs(item.createdAt).format('YYYY-MM-DD HH:mm') }}</span>
					</div>
					<div>
						<NEllipsis :line-clamp="4" :tooltip="false">{{ item.text }}</NEllipsis>
					</div>
					<div class="flex justify-between items-center mt-2">
						<div class="flex gap-2">
							<NTooltip>
								<template #trigger>
									<NButton secondary @click="handleEditor(item)">
										<template #icon>
											<ViewGridDetail theme="outline" />
										</template>
									</NButton>
								</template>
								查看
							</NTooltip>
							<NTooltip>
								<template #trigger>
									<NButton secondary @click="handleEditor(item)">
										<template #icon>
											<Editor theme="outline" />
										</template>
									</NButton>
								</template>
								编辑
							</NTooltip>
							<NTooltip>
								<template #trigger>
									<NButton secondary @click="handleDel(item)">
										<template #icon>
											<DeleteFive theme="outline" />
										</template>
									</NButton>
								</template>
								删除
							</NTooltip>
							<NPopover trigger="hover" placement="bottom" style="--n-padding: 0;">
								<template #trigger>
									<NButton secondary>
										<template #icon>
											<Copy theme="outline" />
										</template>
										复制
									</NButton>
								</template>
								<div class="flex flex-col p-1">
									<NButton quaternary @click="handleCopy(item.text, 'text')">Txt</NButton>
									<NButton quaternary @click="handleCopy(item.text, 'markdown')">Markdown</NButton>
									<NButton quaternary @click="handleCopy(item.text, 'html')">HTML</NButton>
								</div>
							</NPopover>
							<NPopover trigger="hover" placement="bottom" style="--n-padding: 0;">
								<template #trigger>
									<NButton secondary>
										<template #icon>
											<NImage :src="FileUpload" class=" w-[16px] h-[13px]" preview-disabled />
										</template>
										导出
									</NButton>
								</template>
								<div class="flex flex-col p-1">
									<NButton quaternary @click="handleDownload(item, 'word')">Word</NButton>
									<NButton quaternary @click="handleDownload(item, 'markdown')">Markdown</NButton>
								</div>
							</NPopover>
						</div>
						<div>
							<!-- <NButton bordered size="small" @click="handleDownload(item)" type="info" ghost>
              <template #icon>
                <NImage :src="FileUpload" class=" w-[16px] h-[13px]" preview-disabled />
              </template>
              导出Word
            </NButton> -->
						</div>
					</div>
				</NCard>
			</div>
		</NSpin>
		<div v-if="!data.length && !loading" class="flex-1 h-full flex justify-center items-center">
			<NEmpty size="large" description="暂无数据"></NEmpty>
		</div>
		<div class="py-4 flex justify-end">
			<NPagination v-if="searchParams.itemCount >= searchParams.pageSize" :item-count="searchParams.itemCount"
				:page="searchParams.page" :page-size="searchParams.pageSize" @update:page-size="handlePaginationSizeChange"
				@update:page="handlePaginationChange">
				<template #prefix="{ itemCount }">
					共 {{ itemCount }} 条
				</template>
			</NPagination>
		</div>
	</div>
</template>
<script setup lang="ts">
import { delCreationHistory, getHistoryList, markdownToDocx } from '@/chatgpt'
import { onMounted, reactive, ref } from 'vue'
import { useRequest } from 'vue-hooks-plus'
import { useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'
import FileUpload from '@/assets/fileUpload.png';
import { NButton, NCard, NEllipsis, NEmpty, NImage, NInput, NPagination, NPopover, NSpin, NTag, NTooltip, useMessage } from 'naive-ui'
import { Copy, DeleteFive, Editor, Return, Search, ViewGridDetail } from '@icon-park/vue-next'
import { copyToClipboard } from '@/utils/clipboard'
import { marked } from 'marked'

const route = useRoute()
const router = useRouter()
const message = useMessage()

const searchParams = reactive({
	searchWord: '',
	page: 1,
	pageCount: 1,
	pageSize: 20,
	orderBy: [['updatedAt', 'DESC']],
	itemCount: 0,
	createbotId: route.query.appid
})
const data = ref<any[]>([])

const { run, loading } = useRequest(() => getHistoryList({
	content: searchParams.searchWord,
	page: searchParams.page,
	pageSize: searchParams.pageSize,
	orderBy: searchParams.orderBy,
	createbotId: searchParams.createbotId
}), {
	manual: true,
	onSuccess: (res: any) => {
		data.value = res.rows
		searchParams.itemCount = res.count
		searchParams.pageCount = Math.ceil(res.count / searchParams.pageSize)
		if (!res.rows?.length && searchParams.page > 1) {
			searchParams.page -= 1
			run()
		}
	}
})

onMounted(() => {
	run()
})

const handleCopy = (text, type) => {
	switch (type) {
		case 'text':
			// 将Markdown转换为纯文本（简单移除Markdown标记）
			const plainText = text.replace(/#+\s+/g, '').replace(/\*\*/g, '').replace(/\*/g, '').replace(/\[([^\]]+)\]\([^)]+\)/g, '$1')
			copyToClipboard(plainText).then((success) => success ? message.success('复制Txt成功') : message.error('复制失败, 请稍后重试'))
			break
		case "markdown":
			copyToClipboard(text).then((success) => success ? message.success('复制Markdown成功') : message.error('复制失败, 请稍后重试'))
			break
		case "html":
			copyToClipboard(marked.parse(text) as string).then((success) => success ? message.success('复制HTML成功') : message.error('复制失败, 请稍后重试'))
	}
}
const handleDownload = (row, type) => {
	switch (type) {
		case "markdown":
			handleDownloadMarkdown(row)
			break
		case "word":
			runMarkdownToDocx({ chatId: row.id, name: row.Createbots.name })
			break
	}
}
const handleEditor = (item) => {
	const { Createbots } = item
	const id = Createbots.Category?.[0]?.id
	sessionStorage.setItem('writing_history_detail', item.text)
	router.push(`/apps/${id}?appid=${item.createbotId}&step=2`)
}

const handlePaginationChange = (page: number) => {
	searchParams.page = page
	run()
}
const handlePaginationSizeChange = (pageSize: number) => {
	searchParams.pageSize = pageSize
	searchParams.page = 1
	run()
}

const backToWriting = () => {
	const redirectUrl = sessionStorage.getItem('writing_history_redirect_url')
	if (redirectUrl) {
		sessionStorage.removeItem('writing_history_redirect_url')
		router.push(redirectUrl)
	} else {
		router.back()
		// router.push('/apps')
	}
}

const { run: runMarkdownToDocx } = useRequest(markdownToDocx, {
	manual: true,
	onSuccess: (fileBlob: any, params) => {
		const blob = new Blob([fileBlob], {
			type: "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
		})
		const downloadElement = document.createElement('a') //创建a标签
		const href = window.URL.createObjectURL(blob) //创建DOMString
		const filename = params?.[0]?.name || '文件' //文件名
		downloadElement.style.display = 'none' //隐藏a标签
		downloadElement.href = href //赋值a标签的href
		downloadElement.download = filename //下载后文件名
		document.body.appendChild(downloadElement) //插入a标签
		downloadElement.click() //点击下载
		document.body.removeChild(downloadElement) //下载完成移除元素
		window.URL.revokeObjectURL(href) //释放掉blob对象
	},
	onError: e => {
		console.log('e', e);

	}
})

const handleDownloadMarkdown = (row: any) => {
	const blob = new Blob([row.text], {  // 将row.content改为row.text
		type: "text/markdown;charset=utf-8"
	})
	const downloadElement = document.createElement('a')
	const href = window.URL.createObjectURL(blob)
	const filename = `${row?.Createbots?.name || '创作'}.md`
	downloadElement.style.display = 'none'
	downloadElement.href = href
	downloadElement.download = filename
	document.body.appendChild(downloadElement)
	downloadElement.click()
	document.body.removeChild(downloadElement)
}

const { run: runDelCreationHistory } = useRequest(delCreationHistory, {
	manual: true,
	onSuccess: (data) => {
		message.success('删除成功')
		run()
	},
	onError: (err) => {
		message.error(err.message)
	}
})

const handleDel = (row) => {
	runDelCreationHistory({ chatId: row.id })
}

</script>


<style scoped>
:deep(.n-spin-content), :deep(.n-spin-container) {
	height: 100%;
}
</style>
