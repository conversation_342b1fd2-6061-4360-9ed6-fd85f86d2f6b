import { createApp } from "vue";
import App from "./App.vue";
import { setupI18n } from "./locales";
import { setupAssets, setupScrollbarStyle, useDirective } from "./plugins";
import { setupStore } from "./store";
import { setupRouter } from "./router";
import { ss } from "@/utils/storage";
import Icon from '@/plugins/icons'

const at = Array.prototype.at;
if ([0].at(-1) === undefined) {
	Array.prototype.at = function (index) {
		if (index < 0) {
			return this[this.length + index];
		}
		return at.call(this, index);
	};
}

if (!Object.hasOwn) {
	Object.defineProperty(Object, "hasOwn", {
		value: function (object, property) {
			if (object == null) {
				throw new TypeError("Cannot convert undefined or null to object");
			}
			return Object.prototype.hasOwnProperty.call(Object(object), property);
		},
		configurable: true,
		enumerable: false,
		writable: true,
	});
}
// @ts-ignore
if (typeof Promise?.withResolvers === "undefined") {
	// @ts-ignore
	Promise.withResolvers = function () {
		let resolve, reject;
		const promise = new Promise((res, rej) => {
			resolve = res;
			reject = rej;
		});
		return { promise, resolve, reject };
	};
}

async function bootstrap() {
	const searchParams = new URLSearchParams(window.location.search);
	const token = searchParams.get("access_token"); // 获取param1的值,即"value1"
	const token2 = searchParams.get("token")
	// alert(window.location.search);

	console.log(">>>location search", location.search);

	if (token && /MicroMessenger/.test(navigator.userAgent)) {
		ss.set("SECRET_TOKEN", token);
		searchParams.delete("access_token");
		const parsedUrl = new URL(window.location.href);
		const newUrl = searchParams.toString()
			? parsedUrl.origin + parsedUrl.pathname + "?" + searchParams.toString()
			: parsedUrl.origin + parsedUrl.pathname;
		window.location.replace(newUrl);
	}
	if (token2) {
		ss.set("SECRET_TOKEN", token2);
		searchParams.delete("token");
		const parsedUrl = new URL(window.location.href);
		const newUrl = searchParams.toString()
			? parsedUrl.origin + parsedUrl.pathname + "?" + searchParams.toString()
			: parsedUrl.origin + parsedUrl.pathname;
		window.location.replace(newUrl);
	}

	if (!ss.get("SECRET_TOKEN") && /MicroMessenger/.test(navigator.userAgent)) {
		window.location.replace(
			`${location.origin}/api3/aiwork/sign/getGzhAuthUrl`
		);
		// window.location.replace(
		// 	"https://data.aiwork365.cn/aiwork/sign/getGzhAuthUrl"
		// );
	}

	window.$aiwork = {
		openLogin: null,
		// @ts-ignore
		openRecharge: null,
	};
	// window.$aiwork = {
	// 	openLogin: null
	// }
	const app = createApp(App);
	app.use(Icon)
	setupAssets();

	setupScrollbarStyle();
	useDirective(app);
	setupStore(app);
	setupI18n(app);

	// @ts-ignore
	// const { message, notification, dialog, loadingBar, modal } = createDiscreteApi(
	// 	// @ts-ignore
	// 	['message', 'dialog', 'notification', 'loadingBar', 'modal'],
	// )

	// window.$message = message
	// window.$notification = notification

	await setupRouter(app);
	app.mount("#app");
}

bootstrap();
