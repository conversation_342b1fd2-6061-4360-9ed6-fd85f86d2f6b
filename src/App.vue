<script setup lang="ts">
import { onMounted, provide, ref, watchEffect } from "vue";
import { useRoute } from "vue-router";
import { NConfigProvider } from "naive-ui";
import { NaiveProvider } from "@/components/common";
import { useTheme } from "@/hooks/useTheme";
import { useLanguage } from "@/hooks/useLanguage";
import { ss } from "@/utils/storage";
import GlobalFloat from "@/components/common/GlobalFloat/index.vue";
import { useAuthStore } from "@/store";
import { setToken } from "./store/modules/auth/helper";
const { theme, themeOverrides } = useTheme();
const { language } = useLanguage();

if (
	window.location.href.includes("bd_vid") ||
	window.location.href.includes("?")
) {
	ss.set("LANDING_PAGE", window.location.href);
}
ss.set("LANDING_NOCHANNEL_PAGE", window.location.href);

if (location.pathname === "/paper2") {
	ss.set("FROM_BING", true);
}

const route = useRoute();
const authStore = useAuthStore();

// 监听路由变化检查shareId
watchEffect(() => {
	if (typeof window !== 'undefined') {
		const shareId = route.query.shareId as string;
		if (shareId) {
			authStore.updateShareId(shareId);
		}
	}
});

onMounted(() => {
	window.landingUrl = window.location.href;
});
</script>

<script lang="ts">
export default {
	mounted() {
		// 使用原生 JavaScript 获取 div 元素并设置样式
		// var _hmt: any = _hmt || [];
		// (function () {
		//   const hm = document.createElement('script')
		//   hm.src = 'https://hm.baidu.com/hm.js?18471596a52a68c18bf5a770bd7a43d1'
		//   const s: any = document.getElementsByTagName('script')[0]
		//   s.parentNode.insertBefore(hm, s)
		// })()
	},
};
</script>

<template>
	<NConfigProvider
		class="h-full relative"
		:theme="theme"
		:theme-overrides="themeOverrides"
		:locale="language"
	>
		<NaiveProvider>
			<RouterView />
		</NaiveProvider>
	</NConfigProvider>
</template>

<style lang="less" setup>
:deep(.n-button .n-button__content) {
	justify-content: center;
}

.loading {
	position: relative;
}
.loading::before {
	content: "";
	position: absolute;
	z-index: 10;
	top: 0;
	left: 0;
	bottom: 0;
	right: 0;
	background-color: rgba(255, 255, 255, 0.4);
}

.loading::after {
	content: "";
	position: absolute;
	z-index: 11;
	top: 50%;
	left: 50%;
	width: 20px;
	height: 20px;
	border-radius: 50%;
	border: 3px solid #3498db;
	border-top-color: transparent;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	to {
		transform: rotate(360deg);
	}
}
</style>
