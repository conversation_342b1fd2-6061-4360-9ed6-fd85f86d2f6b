<script setup lang="ts">
import { computed, ref, provide } from "vue";
import {
	NLayout,
	NLayoutHeader,
	NLayoutContent,
	NLayoutFooter,
} from "naive-ui";
import { CopyRight } from "@/components/common";
import Header1 from "@/components/common/Header1/index.vue";
import { useBasicLayout } from "@/hooks/useBasicLayout";
import { useAppStore } from "@/store";
import { useRoute } from "vue-router";
const appStore = useAppStore();
const { isMobile } = useBasicLayout();
const route = useRoute();

const props = withDefaults(
	defineProps<{
		extend?: boolean;
	}>(),
	{
		extend: false,
	}
);
// console.log(">>>>route", route);

const collapsed = computed(() => appStore.siderCollapsed);

const getMobileClass = computed(() => {
	if (isMobile.value) return ["rounded-none", "shadow-none"];
	return ["dark:border-neutral-800"];
});

const getContainerClass = computed(() => {
	return ["h-full", { "": !isMobile.value && !collapsed.value }];
});

// 添加 scrollPageTo 功能
const scrollPageTo = (top = 0) => {
	window.scrollTo({ top, behavior: 'smooth' });
};

provide("scrollPageTo", scrollPageTo);
</script>

<template>
	<div
		class="h-full dark:bg-[#24272e] transition-all"
		:class="[isMobile ? 'p-0' : 'p-0']"
	>
		<div class="min-h-full flex flex-col sm:pb-[50px]" :class="getMobileClass">
			<NLayoutHeader
				v-if="!route.meta.hideHeader && route.query.fromapp !== 'true'"
				class="h-[64px] box-border flex-shrink-0 flex-grow-0"
			>
				<Header1 />
			</NLayoutHeader>
			<!-- <NLayout class="z-40 transition flex-1 flex flex-col" :class="getContainerClass" has-sider> -->
			<!-- <NLayoutContent class="flex-1 flex flex-col"> -->
			<template v-if="props.extend"><slot></slot></template>
			<RouterView
				v-if="!props.extend"
				class="flex-1 flex flex-col"
				v-slot="{ Component, route }"
			>
				<component :is="Component" :key="route.fullPath" />
			</RouterView>
			<!-- </NLayoutContent> -->
			<!-- </NLayout> -->
			<NLayoutFooter
				v-if="!route.meta.hideFooter && route.query.fromapp !== 'true'"
				class="flex-shrink-0 flex-grow-0"
			>
				<CopyRight />
			</NLayoutFooter>
		</div>
	</div>
</template>
