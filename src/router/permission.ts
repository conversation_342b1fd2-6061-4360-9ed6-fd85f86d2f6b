import type { Router } from "vue-router";
import { useAuthStoreWithout } from "@/store/modules/auth";

export function setupPageGuard(router: Router) {
	router.beforeEach(async (to, from, next) => {
		if (to.meta.content) {
			const head = document.getElementsByTagName("head");
			let meta = document.createElement("meta");
			document
				.querySelector('meta[name="keywords"]')
				?.setAttribute(
					"content",
					(to.meta.content as unknown as any).keywords as string
				);
			document
				.querySelector('meta[name="description"]')
				?.setAttribute(
					"content",
					(to.meta.content as unknown as any).description as string
				);
			if (document?.title) {
				document.title = (
					(to.meta.content as unknown as any).title || ""
				).toString();
			}
			meta.content = String(to.meta.content);
			head[0].appendChild(meta);
		}
		const authStore = useAuthStoreWithout();
		if (!authStore.session) {
			try {
				const data = await authStore.getSession();
				// console.log(authStore.token, data.auth, "authStore.token");
				if (String(data.auth) === "false" && authStore.token)
					authStore.removeToken();
				if (to.path === "/500")
					// next({ name: 'Root' })
					next();
				else {
					next();
				}
			} catch (error) {
				if (to.path !== "/500") next({ name: "500" });
				else next();
			}
		} else {
			next();
		}
	});
}
