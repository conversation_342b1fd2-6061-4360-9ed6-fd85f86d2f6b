declare namespace Chat {
	enum Step {
		Loading = 0, // 默认
		Searching = 1, // 开始请求
		Reading = 2, // 阅读中
		Sumup = 3, // 总结
		Searched = 4, // 完成搜索
		Sending = 5, // 发送中
		Completed = 99, // 完成
		Fail = 100 // 失败
	}
	enum Tips {
		Recharge = 1 // 充值
	}
	interface Chat {
		dateTime: string; // 日期时间
		text: string; // 文本内容
		showThink?: boolean; // 是否展示深度思考
		thinkType?: ThinkType; // 思考类型
		thinkInfo?: string; // 思考内容
		messageList?: string[]; // 消息
		step?: Step;
		inversion?: boolean; // 是否反转
		error?: boolean; // 是否错误
		tips?: Tips;
		imgObj?: {
			imageUrl: string; // 图片URL
			hasTag?: boolean; // 是否有标签
			progress?: string; // 进度
			msgID?: string; // 消息ID
			msgHash?: string; // 消息哈希
			isQueue?: boolean; // 是否队列
			tags?: [
				{
					msgId: string; // 消息ID
					tag: string; // 标签
				}
			];
		};
		fileObj?: {
			name?: string; // 文件名
			size?: number; // 文件大小
			type?: string; // 文件类型
		};
		loading?: boolean; // 是否加载中
		conversationOptions?: ConversationRequest | null; // 对话选项
		requestOptions?: { prompt: string; options?: ConversationRequest | null }; // 请求选项
		isLimit?: boolean; // 是否限制
		messageId?: string; // 消息ID
		isCompute?: boolean; // 是否计算
		searchInfo?: any; // 搜索信息
		questionLoading?: boolean; // 问题加载中
		questionList?: string[];
	}

	interface History {
		title: string // 标题
		isEdit: boolean // 是否编辑
		uuid: number // UUID
		type?: string // 类型
		icon?: string
		updateTime?: number // 更新时间
		createTime?: number // 创建时间
		agentId?: string // 智能体ID
		teamId?: string // 团队ID
	}

	interface ChatState {
		active: number | null // 活跃状态
		mjActive?: number | null // MJ活跃状态
		usingContext: boolean; // 是否使用上下文
		history: History[] // 历史记录
		chat: { uuid: number; data: Chat[] }[] // 聊天数据
	}

	interface ChatTask {
		uuid: number | string, // UUID
		type: string, // 类型
		messageId: string, // 消息ID
		time: number, // 时间
		prompt?: string, // 提示
		size?: number, // 大小
		url?: string // URL
	}
	interface ChatTasks {
		task: [{
			uuid: number | string, // UUID
			type: string, // 类型
			messageId: string, // 消息ID
			time: number, // 时间
			prompt?: string, // 提示
			size?: number, // 大小
			url?: string // URL
		}]
	}

	interface ConversationRequest {
		conversationId?: string // 对话ID
		parentMessageId?: string // 父消息ID
	}

	interface ConversationResponse {
		conversationId: string // 对话ID
		detail: {
			choices: { finish_reason: string; index: number; logprobs: any; text: string }[] // 选择项
			created: number // 创建时间
			id: string // ID
			model: string // 模型
			object: string // 对象
			usage: { completion_tokens: number; prompt_tokens: number; total_tokens: number } // 使用情况
		}
		id: string // ID
		parentMessageId: string // 父消息ID
		role: string // 角色
		text: string // 文本内容
	}
	interface GptMessage {
		// GPT 消息的角色
		role: string;
		// 消息类型
		type?: string;
		// 消息的唯一标识符
		id: string;
		// 父消息的唯一标识符
		parentMessageId: string;
		// 消息的文本内容
		text: string;
		// 消息的增量
		delta: string;
		// 问题信息
		quesInfo: QuesInfo;
		// 消息内容
		message?: string;
		// 搜索信息
		searchInfo?: SearchInfo[];
		// 详细信息
		detail?: Detail;
		// 长文思考
		reasoning_content?: string
	}

	interface Detail {
		// 选项列表
		choices: Choice[];
		// 创建时间
		created: number;
		// 唯一标识符
		id: string;
		// 对象
		object: string;
		// 使用情况
		usage: null;
	}

	interface Choice {
		// 增量
		delta: Delta;
		// 索引
		index: number;
		// 结束原因
		finish_reason?: string;
	}

	interface Delta {
		// 内容
		content: string;
		// 角色
		role: string;
	}

	interface SearchInfo {
		// URL
		url: string;
		// 标题
		title: string;
		// 内容
		content: string;
		// 缩略图
		thumbnail: null;
		// 引擎
		engine: string;
		// 解析后的 URL
		parsed_url: string[];
		// 模板
		template: string;
		// 位置
		positions: number[];
		// 分数
		score: number;
		// 类别
		category: string;
	}
	interface QuesInfo {
		id: number;
	}
}
