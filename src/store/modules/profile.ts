import { defineStore } from 'pinia';
import { fetchPersonalDetail2, fetchBindPay, PersonalDetail } from '@/views/profile/api';
import { getUser, setUser } from '@/store/modules/auth/helper';
import { useMessage } from 'naive-ui';

// 建议为 memberData, user 等创建详细的 interface/type
interface UserProfile {
  nickname: string;
  phone: string;
  openId: string;
  uid: string;
  avatar: string;
}

// 使用从 api.ts 导入的类型
type MemberData = PersonalDetail;

interface ProfileState {
  user: UserProfile | null;
  memberData: MemberData | null;
  loading: boolean;
  error: any;
}

export const useProfileStore = defineStore('profile', {
  state: (): ProfileState => ({
    user: getUser(),
    memberData: null,
    loading: false,
    error: null,
  }),

  getters: {
    // 微信是否绑定
    wechatBind: (state): boolean => !!state.user?.openId,
    // 手机是否绑定
    phoneBind: (state): boolean => !!state.user?.phone,

    // 免费会员权益
    freeMemberStats: (state) => {
      if (!state.memberData?.freeMembers) return [];
      return state.memberData.freeMembers;
    },
    // 付费会员权益
    paidMemberStats: (state) => {
      if (!state.memberData?.member) return null;
      return state.memberData.member;
    },
    // 论文算力包权益
    paperMemberStats: (state) => {
      if (!state.memberData?.paperMember) return null;
      return state.memberData.paperMember;
    },
    // 会员积分
    memberPoints: (state) => {
      // 假设积分信息在 user 对象中，如果不在，需要调整
      return (state.user as any)?.points ?? 0;
    },
  },

  actions: {
    // 获取个人详细信息
    async fetchPersonalDetail() {
      this.loading = true;
      try {
        const data = await fetchPersonalDetail2();
        this.memberData = data;
        this.error = null;
      } catch (error) {
        this.error = error;
        const message = useMessage();
        message.error('获取用户信息失败');
      } finally {
        this.loading = false;
      }
    },

    // 绑定支付订单
    async bindPayOrder(orderNumber: string) {
      const message = useMessage();
      if (!orderNumber.trim()) {
        message.warning('请输入订单编号');
        return false;
      }
      try {
        await fetchBindPay({ payOrderNo: orderNumber });
        message.success('绑定成功');
        // 绑定成功后刷新用户信息
        await this.fetchPersonalDetail();
        return true;
      } catch (err) {
        message.error('绑定失败，请检查订单号是否正确');
        return false;
      }
    },

    // 更新用户信息
    updateUser(userInfo: Partial<UserProfile>) {
      if (this.user) {
        const newUser = { ...this.user, ...userInfo };
        this.user = newUser;
        setUser(newUser);
      }
    }
  },
});
