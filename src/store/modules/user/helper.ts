import { ss } from "@/utils/storage";

const LOCAL_NAME = "userStorage";

export interface UserInfo {
	avatar: string;
	name: string;
	description: string;
	member?: object;
	mjmember?: object;
	uid?: number;
	nickname?: string;
	openId?: string;
	phone?: string;
}
export interface Team {
	id: string;
	name: string;
	avatar?: string;
	TeamsUsers: {
		role: 'superadmin' | 'admin' | 'member';
	}
}

export interface UserState {
	userInfo: UserInfo;
	curTeam?: Team | null;
	teamList?: Team[]
	teamListLoading?: boolean;
	canCreateTeam?: boolean;
}

export function defaultSetting(): UserState {
	return {
		userInfo: {
			avatar: "",
			name: "chatRobot",
			description: "chatRobot",
			uid: 0,
		},
		curTeam: undefined,
		teamList: [],
		teamListLoading: false,
		canCreateTeam: true,
	};
}

export function getLocalState(): UserState {
	const localSetting: UserState | undefined = ss.get(LOCAL_NAME);
	// console.log(1111, localSetting?.userInfo?.uid)
	rprm.public({user_id: localSetting?.userInfo?.uid}, 'new')
	return { ...defaultSetting(), ...localSetting };
}

export function setLocalState(setting: UserState): void {
	ss.set(LOCAL_NAME, setting);
	setting.userInfo.uid && rprm.public({user_id: setting.userInfo.uid}, 'new')
}
