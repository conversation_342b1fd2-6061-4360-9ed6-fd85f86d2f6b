import { defineStore } from 'pinia'
import type { TreeNodes, OutlineData, OutlineNode } from '@/views/ppt/types'
// import { OutlineState } from '@/types/chat'
import { transformNodes } from '@/utils/utils'

export const useOutlineStore = defineStore('ppt-outline-store', {
    state: (): any => ({
        // 标题和副标题
        outlineTitleAndSubTitle: [],
        // 标题和副标题以外的节点
        outline: [],
        isChange: false
    }),
    getters: {

    },
    actions: {
        initOutline(outline: OutlineData, nodes: OutlineNode[]) {
            const [titleNodes, contentNodes] = transformNodes(outline, nodes)
            this.outlineTitleAndSubTitle = titleNodes
            this.outline = contentNodes
        },
        updateOutline(outline: TreeNodes[], isTitle: boolean = false) {
            this.isChange = true
            if (isTitle) {
                this.outlineTitleAndSubTitle = outline
                console.log('outlineTitleAndSubTitle', this.outlineTitleAndSubTitle)
            } else {
                this.outline = outline
                console.log('outline', this.outline)
            }
        }
    }
})
