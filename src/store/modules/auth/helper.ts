import { ss } from "@/utils/storage";

const LOCAL_NAME = "SECRET_TOKEN";
const CUR_USER = "CUR_USER";
const ACTIVITY = "activity";
const ACTIVITY_IMG_SHOW = "activity_img_showed";

export function getToken() {
	return ss.get(LOCAL_NAME);
}

export function getUser() {
	return ss.get(CUR_USER);
}

export function setToken(token: string) {
	return ss.set(LOCAL_NAME, token);
}

export function removeToken() {
	return ss.remove(LOCAL_NAME);
}

export function setUser(user: any) {
	return ss.set(CUR_USER, user);
}
export function setActivity(activity: any) {
	return ss.set(ACTIVITY, activity, 60 * 60 * 24); // 24 hours
}
export function getActivity() {
	return ss.get(ACTIVITY);
}
export function setActivityImageShow(activity: boolean) {
	return ss.set(ACTIVITY_IMG_SHOW, activity, 60 * 60 * 24); // 24 hours
}
export function getActivityImageShow() {
	return ss.get(ACTIVITY_IMG_SHOW);
}

export function getCredit(type: 'long' | 'chat' | 'app' | 'poster' | 'ppt', show = true) {
	const { points = {} } = ss.get(CUR_USER);
	if (type in points) {
		if (!show) {
			return `${points[type]}积分`
		}
		return `( ${points[type]}积分 )`
	}
	return
}

