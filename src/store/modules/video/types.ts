/**
 * 视频模型类型枚举
 */
/** 定义视频模型类型的枚举，包含不同的视频生成服务提供商 */
export enum VideoModelType {
	/** Kling模型，值为1 */
	Kling = 1,
	/** 百度模型，值为6 */
	Baidu = 6,
	/** Pika模型，值为2 */
	Pika = 2,
	/** Runway模型，值为4 */
	Runway = 4,
	/** 通义模型，值为5 */
	Tongyi = 5,
	/** 极梦模型，值为3 */
	Jimeng = 3,
	/** Vidu模型，值为7 */
	Vidu = 7
}

/**
 * 视频生成API类型
 */
/** 定义视频生成API的类型，接受参数并返回Promise */
export type VideoApi = (params: any, type?: 'text' | 'image' | 'creative') => Promise<any>

/**
 * 瀑布流图片项类型
 */
/** 定义瀑布流布局中单个图片项的接口 */
export interface WaterfallImage {
	/** 图片URL地址 */
	url: string
	/** 图片高度 */
	height: number
	/** 图片标签 */
	tag: string
	/** 生成图片的提示词 */
	prompt: string
	/** 图片来源，可选 */
	source?: string
}

/**
 * 表单状态类型
 */
/** 定义通用表单状态接口，使用索引签名允许任意键值对 */
export interface FormState {
	/** 允许任意键值对的索引签名 */
	[key: string]: any
}

/**
 * 视频生成状态类型
 */
/** 定义视频生成的可能状态 */
export type GenerateStatus = 'idle' | 'generating' | 'completed' | 'error'
/** 定义特效示例的接口 */
export interface EffectExample {
	/** 案例视频ID */
	exampleId: number;
	/** 特效名称 */
	effect: string;
	/** 左上角图标 */
	icon: string;
	/** 视频地址 */
	url: string;
	/** 视频封面图 */
	poster?: string;
	/** 特效描述 */
	effectText: string;
	/** 特效文案 */
	describe: string;
	/** 封面图地址 */
	coverUrl?: string;
	/** 模型ID */
	modelId: number;
}

/** 定义视频示例的接口 */
export interface VideoExample {
	/** 案例视频ID */
	exampleId: number;
	/** 特效名称 */
	effect: string;
	/** 视频地址 */
	url: string;
	/** 视频高度 */
	height: number;
	/** 特效文案 */
	describe: string;
	/** 模型名称 */
	modelName: string;
	/** 模型ID */
	modelId: number;
	/** 封面图地址 */
	coverUrl?: string;
}

/** 定义视频示例响应的接口 */
export interface VideoExamplesResponse {
	/** 特效视频列表 */
	effectExamples: EffectExample[];
	/** 普通视频列表 */
	videoExampleInfos: VideoExample[];
}

/**
 * 视频模型接口
 */
/** 定义视频模型的基本信息接口 */
export interface VideoModel {
	/** 模型id */
	modelId?: number
	/** 模型名称 */
	modelName?: string
	/** 模型描述 */
	desc?: string
	/** 模型图片 */
	image?: string
}

/** 定义选项接口，用于下拉菜单等 */
export interface Option {
	/** 选项图片 */
	image: string;
	/** 选项键值 */
	key: string;
	/** 选项描述 */
	desc: string;
}

/** 定义下拉选择框选项接口 */
export interface SelectOption {
	/** 选项显示文本 */
	label: string
	/** 选项值 */
	value: string
}

/** 定义百度表单信息接口 */
interface BaiduFormInfo {
	/** API请求URL */
	url: string;
	/** 表单配置数组 */
	form: Form2[];
}

/** 定义二级表单项接口 */
interface Form2 {
	/** 表单项标签 */
	label: string;
	/** 表单项类型 */
	formType: string;
	/** 表单项键名 */
	key: null | string;
	/** 表单项占位符 */
	placeholder: null | string;
	/** 选项数组 */
	options: string[] | null;
	/** 默认值 */
	defaultValue: null;
	/** 子表单项 */
	form: Form[] | null;
	/** 是否必填 */
	required: boolean;
}

/** 定义三级表单项接口 */
interface Form {
	/** 表单项标签 */
	label: string;
	/** 表单项类型 */
	formType: string;
	/** 表单项键名 */
	key: string;
	/** 表单项占位符 */
	placeholder: null;
	/** 选项数组 */
	options: string[];
	/** 默认值数组 */
	defaultValue: string[];
	/** 子表单项，无子项为null */
	form: null;
	/** 是否必填 */
	required: boolean;
}

/**
 * @remarks 文本生成视频表单格式, 图片生成视频表单格式
 */
/** 定义基础表单项接口 */
interface BaseForm {
	/** 表单项标签 */
	label?: string;
	/** 表单项类型 */
	formType?: string;
	/** 表单项键名 */
	key?: string;
	/** 是否必填 */
	required?: boolean;
	/** 最大长度限制 */
	maxlength?: number;
	/** 占位符文本 */
	placeholder?: string;
	/** 选项数组 */
	options?: SelectOption[] | Option[];
	/** 默认值数组 */
	defaultValue?: string[];
	/** 子表单项数组 */
	form?: BaseForm[];
}

/** 定义Kling模型表单接口，继承自BaseForm */
interface KlingForm extends BaseForm {
	/** 子表单项，类型为FormInfo数组 */
	form?: FormInfo[];
}

/** 定义Pika模型表单接口，继承自BaseForm */
interface PikaForm extends BaseForm {
	/** 子表单项，类型为FormInfo数组 */
	form?: FormInfo[];
}

/** 定义火山模型表单接口，继承自BaseForm */
interface HuoshanForm extends BaseForm {
	/** 子表单项，类型为FormInfo数组 */
	form?: FormInfo[];
}

/** 定义Runway模型表单接口，继承自BaseForm */
interface RunwayForm extends BaseForm {
	/** 子表单项，类型为FormInfo数组 */
	form?: FormInfo[];
}

/** 定义通义模型表单接口，继承自BaseForm */
interface TongyiForm extends BaseForm {
	/** 子表单项，类型为FormInfo数组 */
	form?: FormInfo[];
}

/** 定义百度模型表单接口，继承自BaseForm */
interface BaiduForm extends BaseForm {
	/** 图生视频表单列表，为null */
	imageToVideoFormList: null
	/** 文生视频表单列表 */
	textToVideoFormList: BaiduFormInfo
}

/** 定义表单信息的联合类型，包含多种模型的表单类型 */
export type FormInfo = KlingForm | PikaForm | HuoshanForm | RunwayForm | TongyiForm | BaiduForm;

/**
 * @remarks GetModelDetailOut
 */
/** 定义视频模型详情接口 */
export interface VideoModalDetail {
	/** 模型ID */
	id?: number; // mock: @integer
	/** 模型名称 */
	name?: string; // mock: @string
	/** 模型简介 */
	profile?: string; // mock: @string
	/** 模型链接 */
	href?: object; // Object, 备注: Object (Consider Record<string, any> or a more specific type if known)
	/** 欢迎信息 */
	welcomeMessage?: string; // mock: @string
	/** 热度数值 */
	hotNum?: number; // mock: @integer
	/** 模型类型 */
	type?: string; // mock: @string
	/** 目标对象 */
	target?: object; // Object, 备注: Object (Consider Record<string, any> or a more specific type if known)
	/** 是否热门 */
	isHot?: boolean; // mock: @boolean
	/** 是否免费 */
	free?: boolean; // mock: @boolean
	/** 是否联网 */
	isNetwork?: object;
	/** 是否收藏 */
	isFavorite?: boolean;
	/** 表单配置列表 */
	formList?: FormInfo[];
	/** 文生视频表单列表 */
	textToVideoFormList?: {
		/** API请求URL */
		url?: string;
		/** 表单配置 */
		form?: FormInfo[];
	};
	/** 图生视频表单列表 */
	imageToVideoFormList?: {
		/** API请求URL */
		url?: string;
		/** 表单配置 */
		form?: FormInfo[];
	};
	/** 创意视频表单列表 */
	creativeVideoFormList?: {
		/** API请求URL */
		url?: string;
		/** 表单配置 */
		form?: FormInfo[];
	}
}

/**
 * 特效接口
 */
/** 定义特效选项接口 */
export interface SpecialEffect {
	/** 是否热门 */
	hot: boolean
	/** 特效键名 */
	key: string
	/** 特效值 */
	value: string; // mock: @string
	/** 特效描述 */
	desc?: string; // mock: @string
	/** 特效图片 */
	image?: string; // mock: @string
}

/**
 * 视频页面接口
 */
/** 定义视频页面数据接口 */
export interface VideoPage {
	/** 业务类型：文生视频或图生视频 */
	bizType: string;
	/** 任务唯一标识码 */
	taskCode: string;
	/** 使用的模型类型 */
	modelType?: string;
	/** 生成视频的提示词 */
	prompt?: string;
	/** 任务状态：已提交、处理中、成功、失败、等待中 */
	taskStatus?: 'submitted' | 'processing' | 'succeed' | 'failed' | 'waitting';
	/** 生成的视频ID */
	videoId?: string;
	/** 生成视频的URL */
	url?: string;
	/** 视频封面图URL */
	thumbnailUrl?: string
	/** 视频总时长，单位为秒 */
	duration?: string;
	/** 视频创建时间 */
	createTime?: string;
}

/** 定义视频分页列表响应接口 */
export interface VideoPageListResponse {
	/** 当前页码 */
	pageNum?: number;
	/** 每页数量 */
	pageSize?: number;
	/** 总记录数 */
	totalCount?: number;
	/** 视频分页列表 */
	pageList?: VideoPage[];
}

/** 定义视频详情接口 */
export interface VideoDetail {
	/** 模型ID */
	modelId: number;
	/** 任务唯一标识码 */
	taskCode?: string;
	/** 任务状态：已提交、处理中、成功、失败、等待中 */
	taskStatus?: 'submitted' | 'processing' | 'succeed' | 'failed' | 'waitting';
	/** 生成视频的URL */
	url?: string;
	/** 视频总时长，单位为秒 */
	duration?: string;
	/** 提示信息 */
	prompt?: string;
	/** 负面提示信息 */
	negativePrompt?: string;
	/** 图片URL */
	imageUrl?: string;
	/** 图片尾部URL */
	imageTailUrl?: string;
	/** 表单提交参数 */
	inputParameter?: Record<string, any>;
}

/** 定义随机提示词响应接口 */
export interface RandomPromptResponse {
	/** 随机生成的提示语 */
	prompt: string;
}
/** 视频示例详情接口响应类型 */
export interface VideoExampleDetail {
	/** 案例视频ID */
	exampleId?: number;
	/** 特效值 */
	effect?: string;
	/** 特效名称 */
	effectText?: string;
	/** 模型ID */
	modelId?: number;
	/** 左上角图标：特效中是URL，视频中是文字描述 */
	icon?: string;
	/** 视频地址 */
	url?: string;
	/** 视频文案 */
	describe?: string;
	/** 输入参数对象 */
	inputParameter?: Record<string, any>;
}
