import { post } from '@/utils/request'
import {
  VideoExamplesResponse,
  VideoModel,
  VideoApi,
  FormInfo,
  VideoModalDetail,
  SpecialEffect,
  VideoPage,
  VideoPageListResponse,
  VideoDetail,
  RandomPromptResponse,
  Option,
  SelectOption,
	VideoExampleDetail
} from './types'

export function getModelList(): Promise<VideoModel[]> {
	return post({
		url: '/api3/aiwork365/video/getModelList'
	})
}

// /aiwork365/video/getModelDetail
export function getModelDetail(params: { modelId: number }): Promise<VideoModalDetail> {
	return post({
		url: '/api3/aiwork365/video/getModelDetail',
		data: params
	})
}

export function getSpecialEffects(params: { modelId: number }): Promise<{ effects: SpecialEffect[] }> {
	return post({
		url: '/api3/aiwork365/video/getSpecialEffects',
		data: params
	});
}

export function getVideoPageList(params: { pageNum: number; pageSize: number; taskCode?: string }): Promise<VideoPageListResponse> {
	return post({
		url: '/api3/aiwork365/video/getVideoPageList',
		data: params
	});
}

export function genBaiduEffectsVideo(params: { effect: string; image: string }): Promise<VideoPageListResponse> {
	return post({
		url: '/api3/aiwork365/video/baidu/effectsVideo',
		data: params
	});
}

export function getVideoDetail(params: { taskCode: string }): Promise<VideoDetail> {
	return post({
		url: '/api3/aiwork365/video/getVideoDetail',
		data: params
	});
}

export function getRandomPrompt(): Promise<RandomPromptResponse> {
	return post({
		url: '/api3/aiwork365/video/randomPrompt'
	});
}

export function genPikaImgToVideo(params: {
	prompt?: string;
	aspectRatio: "16:9" | "9:16" | "1:1" | "5:2" | "4:5" | "4:3";
	negativePrompt?: string;
	seed?: number;
	image?: string;
}): Promise<{ taskCode: string; userId: number }> {
	return post({
		url: '/api3/aiwork365/video/pika/imgToVideo',
		data: params
	});
}

export function genPikaTextToVideo(params: {
	prompt?: string;
	aspectRatio: "16:9" | "9:16" | "1:1" | "5:2" | "4:5" | "4:3";
	negativePrompt?: string;
	seed?: number;
	image?: string;
}): Promise<{ taskCode: string; userId: number }> {
	return post({
		url: '/api3/aiwork365/video/pika/textToVideo',
		data: params
	});
}

export function genKlImgToVideo(params: {
	imageUrl?: string;
	imageTailUrl?: string;
	prompt?: string;
	negativePrompt?: string;
	cfgScale?: number;
}): Promise<{ taskCode: string; userId: number }> {
	return post({
		url: '/api3/aiwork365/video/kl/imgToVideo',
		data: params
	});
}

export function genKlTextToVideo(params: {
	prompt: string;
	negativePrompt?: string;
	cfgScale?: number;
	mode?: string;
	aspectRatio?: number;
	duration?: string;
}): Promise<{ taskCode: string; userId: number }> {
	return post({
		url: '/api3/aiwork365/video/kl/textToVideo',
		data: params
	});
}

export function genPikaVideo(params: any, type?: 'text' | 'image' | 'creative') {
	if (type === 'image') {
		return genPikaImgToVideo(params)
	}
	return genPikaTextToVideo(params)
}

export function genKlingVideo(params: any, type?: 'text' | 'image' | 'creative') {
	if (type === 'image') {
		return genKlImgToVideo(params)
	}
	return genKlTextToVideo(params)
}

export function genRunwayImgToVideo(params: {
	prompt: string;
	image: string;
	duration?: string;
	seed?: number;
}): Promise<{ taskCode: string; userId: number }> {
	return post({
		url: '/api3/aiwork365/video/runway/imgToVideo',
		data: params
	});
}

export function genTyWanToVideo(params: {
	videoUrl: string;
	effect: number;
	minLen: number;
	duration: number;
}) {
	return post({
		url: '/api3/aiwork365/video/ty/styleTransform',
		data: params
	})
}

export function genSwImgToVideo(params: {
	prompt: string;
	imgUrl?: string;
	duration?: number;
	aspectRatio?: number;
	resolution?: number;
}): Promise<{ taskCode: string; userId: number }> {
	return post({
		url: '/api3/aiwork365/video/sw/imgToVideo',
		data: params,
	});
}

export function genSwTextToVideo(params: {
	prompt: string;
	duration?: number;
	aspectRatio?: number;
	resolution?: number;
}): Promise<{ taskCode: string; userId: number }> {
	return post({
		url: '/api3/aiwork365/video/sw/textToVideo',
		data: params,
	});
}

export function genSwVideo(params: any, type?: 'text' | 'image' | 'creative') {
	if (type === 'image') {
		return genSwImgToVideo(params)
	}
	return genSwTextToVideo(params)
}

export function genViduEffectsVideo(params: { effect: string; image: string }): Promise<{ taskCode: string; userId: number }> {
	return post({
		url: '/api3/aiwork365/video/vidu/effectsVideo',
		data: params
	});
}

export function delVideo(params: { taskCode: string }): Promise<{ succ: boolean }> {
	return post({
		url: '/api3/aiwork365/video/delVideo',
		data: params
	});
}

export function reGenerateVideo(params: { taskCode: string }): Promise<{ taskCode: string; userId: number }> {
	return post({
		url: '/api3/aiwork365/video/reGenerateVideo',
		data: params
	});
}

export function getVideoExamples(): Promise<VideoExamplesResponse> {
	return post({
		url: '/api3/aiwork365/video/videoExamples'
	});
}


export function getVideoExampleDetail(params: { exampleId: number }): Promise<VideoExampleDetail> {
	return post({
		url: '/api3/aiwork365/video/videoExampleDetail',
		data: params
	});
}
