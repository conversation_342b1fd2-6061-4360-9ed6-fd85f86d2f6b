import { defineStore } from 'pinia'
import { ref, computed, watch, nextTick } from 'vue'
import { getModelList, getModelDetail, getSpecialEffects, getVideoPageList, genBaiduEffectsVideo, getVideoDetail, getRandomPrompt, genPikaVideo, genRunwayImgToVideo, genTyWanToVideo, genKlingVideo, genSwVideo, genViduEffectsVideo, delVideo, getVideoExamples, getVideoExampleDetail } from './apis'
import { VideoModelType, FormInfo, SpecialEffect, VideoPage, VideoModel, VideoModalDetail, EffectExample, VideoExample } from './types'

export const useVideoStore = defineStore('home', () => {
	// 全局轮询定时器
	const globalPollingTimer = ref<NodeJS.Timeout | null>(null);
	// 特效创意数据
	const specialEffects = ref<SpecialEffect[]>([]);
	const showEffectSidebar = ref(false);
	const selectedEffect = ref<SpecialEffect | null>(null);

	// 视频列表数据
	const videoList = ref<VideoPage[]>([]);
	const videoListPageNum = ref(1);
	const videoListPageSize = ref(10); // 可以根据需要调整
	const videoListTotalCount = ref(0);
	const videoListLoading = ref(false);
	const selectedVideoTaskCode = ref<string | null>(null);

	// 特效创意数据
	const effectExamples = ref<EffectExample[]>([]);
	// 瀑布流图片数据
	const waterfallImages = ref<VideoExample[]>([]);

	// 分页状态
	const visibleCount = ref(12)
	const loading = ref(false)

	// 计算属性
	const visibleImages = computed(() =>
		waterfallImages.value.slice(0, visibleCount.value)
	)
	// 获取视频示例数据（包含特效创意和瀑布流图片）
	const fetchVideoExamples = async () => {
		try {
			const response = await getVideoExamples();
			console.log('API返回数据:', response); // 添加原始响应日志

			// 处理特效创意数据
			if (response.effectExamples && Array.isArray(response.effectExamples)) {
				effectExamples.value = response.effectExamples.map(item => ({
					describe: item.describe || '',
					url: item.url,
					effect: item.effect,
					exampleId: item.exampleId,
					effectText: item.effectText || item.effect || '', // EffectCard 仍需要
					icon: item.icon, // EffectCard 仍需要
					modelId: item.modelId,
					coverUrl: item.coverUrl
				}));
				console.log('处理后的特效创意数据:', effectExamples.value);
			}

			// 处理瀑布流视频数据
			if (response.videoExampleInfos && Array.isArray(response.videoExampleInfos)) {
				waterfallImages.value = response.videoExampleInfos.map(item => ({
					...item,
					// 使用真实的宽高比，如果没有则使用默认值
					width: item.width || 280,
					height: item.height || 350,
					// 确保合理的宽高比
					aspectRatio: item.width && item.height ? item.width / item.height : 0.8
				}));
			}
		} catch (error) {
			console.error("获取视频示例失败:", error);
		}
	};

	const loadMore = () => {
		if (loading.value || visibleCount.value >= waterfallImages.value.length) return

		loading.value = true
		setTimeout(() => {
			visibleCount.value = Math.min(
				visibleCount.value + 6,
				waterfallImages.value.length
			)
			loading.value = false
		}, 800)
	}

	// 表单状态
	const formValues = ref<Record<string, any>>({})
	const generationType = ref<'text' | 'image' | 'creative'>('creative')
	const validationErrors = ref<Record<string, string>>({})
	const validateTrigger = ref(0)

	const validateForm = async () => {
		validationErrors.value = {}
		validateTrigger.value++
		return new Promise<boolean>((resolve) => {
			nextTick(() => {
				resolve(Object.keys(validationErrors.value).length === 0)
			})
		})
	}

	const setValidationError = (key: string, message: string) => {
		validationErrors.value[key] = message
	}

	const clearValidationError = (key: string) => {
		delete validationErrors.value[key]
	}
	// 视频列表相关
	const hasNextPage = computed(() => videoList.value.length < videoListTotalCount.value);
	const hasPendingVideos = computed(() => {
		return videoList.value.some(v => v.taskStatus === 'submitted' || v.taskStatus === 'waitting' || v.taskStatus === 'processing');
	});
	const computedGenerationType = computed(() => {
		if (currentModalDetail.value.textToVideoFormList && currentModalDetail.value.imageToVideoFormList) {
			return true
		}
		return false
	})
	const computedFormList = computed(() => {
		if (!currentModalDetail.value) return null


		const { imageToVideoFormList, textToVideoFormList, creativeVideoFormList } = currentModalDetail.value

		// 如果三者都不存在，返回null
		if (!imageToVideoFormList && !textToVideoFormList && !creativeVideoFormList) return null

		// 三者都存在时，根据generationType返回对应表单
		switch (generationType.value) {
			case 'text':
				return textToVideoFormList?.form || null
			case 'image':
				return imageToVideoFormList?.form || null
			default:
				return creativeVideoFormList?.form || null
		}
	})

	// 自动轮询控制
	watch(hasPendingVideos, (hasPending) => {
		if (hasPending && !globalPollingTimer.value) {
			console.log('开始轮询列表');
			// 启动轮询
			globalPollingTimer.value = setInterval(async () => {
				try {
					await checkVideoStatus();
				} catch (error) {
					console.error('轮询视频列表失败:', error);
				}
			}, 5000);
		} else if (!hasPending && globalPollingTimer.value) {
			console.log('停止轮询列表');

			// 停止轮询
			clearInterval(globalPollingTimer.value);
			globalPollingTimer.value = null;
		}
	}, { immediate: true });

	watch(generationType, (newType) => {
		if (!currentModalDetail.value) return;

		if (newType === 'text' && currentModalDetail.value.textToVideoFormList?.form) {
			initFormValues(currentModalDetail.value.textToVideoFormList.form);
		} else if (newType === 'image' && currentModalDetail.value.imageToVideoFormList?.form) {
			initFormValues(currentModalDetail.value.imageToVideoFormList.form);
		} else if (newType === 'creative' && currentModalDetail.value.creativeVideoFormList?.form) {
			initFormValues(currentModalDetail.value.creativeVideoFormList.form);
		} else if (currentModalDetail.value.formList) {
			initFormValues(currentModalDetail.value.formList);
		}
	});

	const fetchVideoDetail = async (taskCode: string) => {
		try {
			const detail = await getVideoDetail({ taskCode });
			if (detail) {
				const model = models.value.find(model => model.modelId === detail.modelId)
				if (model) {
					currentModel.value = model
					await setCurrentModal(model)
					formValues.value = detail.inputParameter!
				}
			}
		} catch (error) {
			console.error("Failed to fetch video detail:", error);
		}
	}

	// 表单操作方法
	const setFormValue = (key: string, value: any) => {
		formValues.value[key] = value
		clearValidationError(key)
	}

	const getFormValue = (key: string) => {
		return formValues.value[key]
	}

	// 初始化表单值
	const initFormValues = (formList?: FormInfo[]) => {
		if (!formList) return

		formList.forEach(field => {
			if (field.defaultValue) {
				// 特殊处理cfgScale字段，将数组["0"]转换为数字0
				if (field.key === 'cfgScale' && Array.isArray(field.defaultValue) && field.defaultValue[0] === "0") {
					formValues.value[field.key || ''] = 0
				} else {
					formValues.value[field.key || ''] = field.defaultValue
				}
			}
			if (field.form) {
				initFormValues(field.form)
			}
		})
		// 确保首尾帧 URL 字段存在并初始化
		if (!formValues.value.startFrameUrl) {
			formValues.value.startFrameUrl = '';
		}
		if (!formValues.value.endFrameUrl) {
			formValues.value.endFrameUrl = '';
		}
	}

	// 模型相关状态
	const models = ref<VideoModel[]>([])
	const currentModel = ref<VideoModel>({})
	const currentModalDetail = ref<VideoModalDetail>({})

	// 获取模型列表
	const fetchModels = async () => {
		try {
			const res = await getModelList()
			models.value = res
		} catch (error) {
			console.error('获取模型列表失败:', error)
		}
	}

	const setCurrentModal = async (model: VideoModel) => {
		// 保存需要保留的字段
		const keepPrompt = formValues.value._keepPrompt && formValues.value.prompt;
		const keepImage = formValues.value._keepImage && formValues.value.image;
		// 保存当前的生成类型，避免被覆盖
		const currentGenType = generationType.value;

		// 切换模型时重置表单
		formValues.value = {}

		// 如果需要保留prompt，则恢复它
		if (keepPrompt) {
			formValues.value.prompt = keepPrompt;
		}

		// 如果需要保留image，则恢复它
		if (keepImage) {
			formValues.value.image = keepImage;
		}

		currentModel.value = model
		if (model.modelId) {
			const detail = await getModelDetail({ modelId: model.modelId })
			currentModalDetail.value = detail

			// 只有在没有明确设置generationType的情况下才自动设置
			if (!currentGenType) {
				// 根据模型类型设置 generationType
				if ([VideoModelType.Baidu, VideoModelType.Tongyi, VideoModelType.Vidu].includes(model.modelId)) {
					generationType.value = 'creative';
				} else if (detail.textToVideoFormList?.form) {
					generationType.value = 'text';
				} else if (detail.imageToVideoFormList?.form) {
					generationType.value = 'image';
				} else if (detail.creativeVideoFormList?.form) {
					generationType.value = 'creative';
				} else {
					generationType.value = 'text'; // 默认为文本
				}
			} else {
				// 保持用户选择的生成类型
				generationType.value = currentGenType;
			}

			// 初始化表单值
			if (generationType.value === 'text' && detail.textToVideoFormList?.form) {
				initFormValues(detail.textToVideoFormList.form)
			} else if (generationType.value === 'image' && detail.imageToVideoFormList?.form) {
				initFormValues(detail.imageToVideoFormList.form)
			} else if (generationType.value === 'creative' && detail.creativeVideoFormList?.form) {
				initFormValues(detail.creativeVideoFormList.form)
			} else if (detail.formList) {
				initFormValues(detail.formList)
			}
		}
	}

	// 获取特效列表
	const fetchSpecialEffects = async (modelId: number) => {
		try {
			const { effects } = await getSpecialEffects({ modelId });
			specialEffects.value = effects
		} catch (error) {
			console.error("获取特效列表失败:", error);
		}
	};

	// 设置选中的特效
	const setSelectedEffect = (effect: SpecialEffect | null) => {
		selectedEffect.value = effect;
	};

	// 控制特效侧边栏显示
	const setShowEffectSidebar = (show: boolean) => {
		showEffectSidebar.value = show;
	};



	// 合并视频列表，更新状态变化的视频
	const mergeVideoList = (currentList: VideoPage[], newList: VideoPage[]) => {
		const merged = [...currentList];
		const taskCodeMap = new Map(currentList.map(v => [v.taskCode, v]));

		newList.forEach(newVideo => {
			const existingVideo = taskCodeMap.get(newVideo.taskCode);
			console.log('遍历视频');

			if (existingVideo) {
				// 只更新状态变化的视频
				if (existingVideo.taskStatus !== newVideo.taskStatus) {
					console.log('合并视频:', newVideo);
					Object.assign(existingVideo, newVideo);
				}
			} else {
				// 添加新视频到第一页
				merged.unshift(newVideo);
			}
		});

		return merged;
	};

	// 专门用于轮询检查视频状态
	const checkVideoStatus = async () => {
		try {
			const res = await getVideoPageList({ pageNum: 1, pageSize: videoListPageSize.value });
			if (res.pageList) {
				// 只更新状态变化的视频
				const taskCodeMap = new Map(videoList.value.map(v => [v.taskCode, v]));
				console.log('检查视频状态');
				res.pageList.forEach(newVideo => {
					const existingVideo = taskCodeMap.get(newVideo.taskCode);
					if (existingVideo && existingVideo.taskStatus !== newVideo.taskStatus) {
						console.log('合并视频');
						Object.assign(existingVideo, newVideo);
					}
				});
			}
		} catch (error) {
			console.error('检查视频状态失败:', error);
		}
	};

	const fetchVideoList = async () => {
		if (videoListLoading.value) return;
		videoListLoading.value = true;
		try {
			const res = await getVideoPageList({ pageNum: 1, pageSize: videoListPageSize.value });
			videoList.value = res.pageList || [];
			videoListTotalCount.value = res.totalCount || 0;
			videoListPageNum.value = 1; // Reset page number on initial fetch
		} catch (error) {
			console.error('获取视频列表失败:', error);
			videoList.value = [];
			videoListTotalCount.value = 0;
		} finally {
			videoListLoading.value = false;
		}
	};

	const fetchNextPage = async () => {
		if (videoListLoading.value || !hasNextPage.value) return;
		videoListPageNum.value++;
		videoListLoading.value = true;
		try {
			const res = await getVideoPageList({ pageNum: videoListPageNum.value, pageSize: videoListPageSize.value });
			videoList.value = [...videoList.value, ...(res.pageList || [])];
			videoListTotalCount.value = res.totalCount || videoListTotalCount.value; // Update total count if available
		} catch (error) {
			console.error('获取下一页视频列表失败:', error);
			videoListPageNum.value--; // Revert page number on error
		} finally {
			videoListLoading.value = false;
		}
	};


	// 模型到API的映射表
	type VideoApi = (params: any, type?: 'text' | 'image' | 'creative') => Promise<any>;

	const modelToApiMap: Record<VideoModelType, VideoApi> = {
		[VideoModelType.Kling]: genKlingVideo,
		[VideoModelType.Baidu]: genBaiduEffectsVideo,
		[VideoModelType.Pika]: genPikaVideo,
		[VideoModelType.Runway]: genRunwayImgToVideo,
		[VideoModelType.Tongyi]: genTyWanToVideo,
		[VideoModelType.Jimeng]: genSwVideo,
		[VideoModelType.Vidu]: genViduEffectsVideo
	}

	// 视频生成状态
	const generatingStatus = ref<'idle' | 'generating' | 'completed' | 'error'>('idle')
	const currentTaskCode = ref<string | null>(null)
	const videoListTimer = ref<NodeJS.Timeout | null>(null)

	const clearPollingTimers = () => {
		if (videoListTimer.value) {
			clearInterval(videoListTimer.value)
			videoListTimer.value = null
		}
	}

	// 处理百度模型生成
	const handleBaiduGeneration = async (api: VideoApi) => {
		const { effect, image } = formValues.value
		if (typeof effect !== 'string' || typeof image !== 'string') {
			throw new Error('百度模型需要effect和image字符串参数')
		}
		return await api({ effect, image })
	}

	// 处理Pika模型生成
	const handlePikaGeneration = async (api: VideoApi) => {
		const { prompt, aspectRatio, negativePrompt = "", seed = undefined, preferenceMode = undefined, image, resolution, duration } = formValues.value
		if (typeof prompt !== 'string') {
			throw new Error('Pika模型需要prompt字符串参数')
		}
		return await api({
			prompt,
			aspectRatio,
			negativePrompt,
			seed,
			preferenceMode,
			image, resolution, duration
		}, generationType.value)
	}

	// 处理Runway模型生成
	const handleRunwayGeneration = async (api: VideoApi) => {
		const { prompt: runwayPrompt, image, duration = '10', seed = undefined } = formValues.value
		return await api({
			prompt: runwayPrompt, image, duration, seed
		})
	}

	// 处理Kling模型生成
	const handleKlingGeneration = async (api: VideoApi) => {
		const { prompt, negativePrompt, cfgScale, mode, aspectRatio, duration, imageUrl, imageTailUrl } = formValues.value
		return await api({ prompt, negativePrompt, cfgScale, mode, aspectRatio, duration, imageUrl, imageTailUrl }, generationType.value)
	}

	// 处理Tongyi模型生成
	const handleTongyiGeneration = async (api: VideoApi) => {
		const { videoUrl, effect, minLen, duration } = formValues.value
		return await api({ videoUrl, effect, minLen, duration })
	}

	// 处理Sw模型生成
	const handleSwGeneration = async (api: VideoApi) => {
		const { prompt, duration, aspectRatio, resolution, image: imgUrl } = formValues.value

		return await api({ imgUrl, prompt, duration, aspectRatio, resolution }, generationType.value)
	}
	// 处理Vido模型生成
	const handleViduGeneration = async (api: VideoApi) => {
		const { effect, image } = formValues.value

		return await api({ effect, image })
	}

	const generateVideo = async (model: number) => {
		try {
			generatingStatus.value = 'generating'
			clearPollingTimers()
			const api = modelToApiMap[model as VideoModelType]
			if (!api) {
				throw new Error(`不支持的api: ${model}`)
			}

			let response
			switch (model) {
				case VideoModelType.Baidu:
					response = await handleBaiduGeneration(api)
					break
				case VideoModelType.Pika:
					response = await handlePikaGeneration(api)
					break
				case VideoModelType.Runway:
					response = await handleRunwayGeneration(api)
					break
				case VideoModelType.Jimeng:
					response = await handleSwGeneration(api)
					break;
				case VideoModelType.Kling:
					response = await handleKlingGeneration(api)
					break;
				case VideoModelType.Tongyi:
					response = await handleTongyiGeneration(api)
					break;
				case VideoModelType.Vidu:
					response = await handleViduGeneration(api)
					break;
				default:
					response = await api({ ...formValues.value })
			}

			if (response?.taskCode) {
				currentTaskCode.value = response?.taskCode
				// 立即检查一次视频状态
				await fetchVideoList()
				// 选择刚生成的视频作为第一条
				selectedVideoTaskCode.value = response?.taskCode
			}

			return response
		} catch (error) {
			generatingStatus.value = 'error'
			console.error('Generate video failed:', error)
			throw error
		}
	}

	const generateRandomText = async (key: string) => {
		try {
			const res = await getRandomPrompt();
			if (res && res.prompt) {
				setFormValue(key, res.prompt);
				clearValidationError(key)
			} else {
				console.warn('生成随机文本失败: 返回结果无效');
			}
		} catch (error) {
			console.error('生成随机文本失败:', error);
		}
	}

	const deleteGenVideo = async (params: { taskCode: string }) => {
		try {
			const result = await delVideo(params);
			return result;
		} catch (error) {
			console.error('删除视频失败:', error);
			throw error;
		}
	};

	const removeVideoAndUpdateSelection = (taskCode: string) => {
		const index = videoList.value.findIndex(v => v.taskCode === taskCode);
		if (index === -1) return;

		// 检查是否是当前选中的视频
		const isSelected = selectedVideoTaskCode.value === taskCode;

		// 过滤掉被删除的视频
		videoList.value = videoList.value.filter(v => v.taskCode !== taskCode);

		// 如果删除的是选中的视频
		if (isSelected) {
			if (videoList.value.length > 0) {
				// 选择前一个视频，如果没有前一个则选第一个
				const prevIndex = Math.max(index - 1, 0);
				selectedVideoTaskCode.value = videoList.value[prevIndex].taskCode;
			} else {
				// 没有视频了，清空选择
				selectedVideoTaskCode.value = null;
			}
		}
	};

	// 在 return 语句前定义辅助函数
	const setDefaultModel = async () => {
		// 确保有模型数据
		if (models.value.length === 0) {
			await fetchModels();
		}

		if (models.value.length > 0) {
			await setCurrentModal(models.value[0]);
		}
	};
	const fetchVideoExampleDetail = async (exampleId: any) => {
		try {
			const detail = await getVideoExampleDetail({ exampleId });
			if (detail && detail.inputParameter) {
				formValues.value = detail.inputParameter;
			}
			return detail;
		} catch (error) {
			console.error("Failed to fetch video example detail:", error);
			throw error;
		}
	}
	const initializeGenerationParamsFromUrl = async (routeQuery: Record<string, string | null | (string | null)[]>) => {
		// 只在模型列表为空时才请求
		if (models.value.length === 0) {
			await fetchModels();
		}

		// 设置生成类型：有参数用参数，没有保持默认
		if (routeQuery.generationType) {
			generationType.value = routeQuery.generationType as 'text' | 'image' | 'creative';
		}

		// 设置模型：有参数用参数，没有用默认第一个
		if (routeQuery.modelId) {
			const modelId = Number(routeQuery.modelId);
			const model = models.value.find(m => m.modelId === modelId);

			if (model) {
				await setCurrentModal(model);
			} else {
				await setDefaultModel();
			}
		} else {
			await setDefaultModel();
		}

		// 处理taskCode：有taskCode就获取视频详情并填充表单
		if (routeQuery.taskCode) {
			await fetchVideoExampleDetail(routeQuery.taskCode as string);
		}
	};

	return {
		computedGenerationType,
		generateVideo,
		generatingStatus,
		currentTaskCode,
		specialEffects,
		waterfallImages,
		visibleCount,
		loading,
		visibleImages,
		loadMore,
		models,
		currentModel,
		currentModalDetail,
		computedFormList,
		generationType,
		showEffectSidebar,
		selectedEffect,
		fetchModels,
		setCurrentModal,
		fetchSpecialEffects,
		setSelectedEffect,
		setShowEffectSidebar,
		generateRandomText,
		fetchVideoDetail,
		// 视频列表相关
		videoList,
		videoListPageNum,
		videoListPageSize,
		videoListTotalCount,
		videoListLoading,
		selectedVideoTaskCode,
		fetchVideoList,
		fetchNextPage,
		hasNextPage,
		// 表单相关
		formValues,
		setFormValue,
		getFormValue,
		// 验证相关
		validateForm,
		validateTrigger,
		validationErrors,
		setValidationError,
		clearValidationError,
		setSelectedVideoTaskCode: (taskCode: string | null) => {
			selectedVideoTaskCode.value = taskCode;
		},
		deleteGenVideo,
		removeVideoAndUpdateSelection,
		initializeGenerationParamsFromUrl,
		setDefaultModel,
		effectExamples,
		fetchVideoExamples,

	}
});
