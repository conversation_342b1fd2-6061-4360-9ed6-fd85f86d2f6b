import { Editor } from "@tiptap/vue-3"
import { Component, VNode } from "vue"

export type ToolItemType = {
	render: VNode,
	isActive?: (props: {editor: Editor}) => boolean
	command?: (props: {editor: Editor}) => void
	tip: string
	disabled?: (props: {editor: Editor}) => boolean
}

export type ToolbarsGroupType = (ToolItemType | Component | string)[][]

export type BubbleToolsGroupType = (ToolItemType | Component | string)[][]


