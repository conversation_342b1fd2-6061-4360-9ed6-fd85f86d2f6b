<template>
	<NPopover placement="bottom" trigger="hover">
		<template #trigger>
			<NButton quaternary strong size="small">
				{{ value }}
				<Down />
			</NButton>
		</template>
		<div class="block-full-button flex flex-col">
			<NButton quaternary strong @click="setFontSize('12px')">
				<span class="text-xs">最小号</span>
			</NButton>
			<NButton quaternary strong @click="setFontSize('14px')">
				<span class="text-sm">小号</span>
			</NButton>
			<NButton quaternary strong @click="setFontSize('16px')">
				<span class="text-base">默认</span>
			</NButton>
			<NButton quaternary strong @click="setFontSize('18px')">
				<span class="text-lg">大号</span>
			</NButton>
			<NButton quaternary strong @click="setFontSize('20px')">
				<span class="text-xl">特大号</span>
			</NButton>
		</div>
	</NPopover>
</template>

<script setup lang="ts">
import { H1, H2, H3, LevelFiveTitle, LevelFourTitle, LevelSixTitle, ListCheckbox, ListTwo, MindmapList, OrderedList, Quote, Right, ScatterAlignment, Down, Text } from '@icon-park/vue-next';
import { NButton, NPopover, NTooltip } from 'naive-ui';
import { ref } from 'vue';
import { useEditorCtx } from '../../editorCtx';

const value = ref("默认")
const editorCtx = useEditorCtx()

const setFontSize = (size: string) => {
  editorCtx.editor?.chain().focus().setFontSize(size).run()
}
</script>
