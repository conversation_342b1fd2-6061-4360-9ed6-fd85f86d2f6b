<template>
	<NPopover placement="bottom" trigger="hover">
		<template #trigger>
			<NButton quaternary strong size="small">
				<template #icon>
					<MindmapList />
				</template>
				<Down />
			</NButton>
		</template>
		<div class="block-full-button flex flex-col">
			<NButton quaternary strong @click="editorHandles.setParagraph({editor: editor as Editor})">
				<template #icon>
					<ScatterAlignment />
				</template>
				正文
			</NButton>
			<NButton quaternary strong @click="editorHandles.setHeading({editor: editor as Editor, level: 1})">
				<template #icon>
					<H1 />
				</template>
				一级标题
			</NButton>
			<NButton quaternary strong @click="editorHandles.setHeading({editor: editor as Editor, level: 2})">
				<template #icon>
					<H2 />
				</template>
				二级标题
			</NButton>
			<NButton quaternary strong @click="editorHandles.setHeading({editor: editor as Editor, level: 3})">
				<template #icon>
					<H3 />
				</template>
				三级标题
			</NButton>
			<NButton quaternary strong @click="editorHandles.setHeading({editor: editor as Editor, level: 4})">
				<template #icon>
					<LevelFourTitle />
				</template>
				四级标题
			</NButton>
			<NButton quaternary strong @click="editorHandles.setHeading({editor: editor as Editor, level: 5})">
				<template #icon>
					<LevelFiveTitle />
				</template>
				五级标题
			</NButton>
			<NButton quaternary strong @click="editorHandles.setHeading({editor: editor as Editor, level: 6})">
				<template #icon>
					<LevelSixTitle />
				</template>
				六级标题
			</NButton>
			<NButton quaternary strong @click="editorHandles.setBlockquote({editor: editor as Editor})">
				<template #icon>
					<Quote />
				</template>
				引用
			</NButton>
			<NButton quaternary strong @click="editorHandles.toggleBulletList({editor: editor as Editor})">
				<template #icon>
					<ListTwo />
				</template>
				无序列表
			</NButton>
			<NButton quaternary strong @click="editorHandles.toggleOrderedList({editor: editor as Editor})">
				<template #icon>
					<OrderedList />
				</template>
				有序列表
			</NButton>
			<NButton quaternary strong @click="editorHandles.toggleTaskList({editor: editor as Editor})">
				<template #icon>
					<ListCheckbox />
				</template>
				待办列表
			</NButton>
		</div>
	</NPopover>
</template>

<script setup lang="ts">
import { H1, H2, H3, LevelFiveTitle, LevelFourTitle, LevelSixTitle, ListCheckbox, ListTwo, MindmapList, OrderedList, Quote, Right, ScatterAlignment, Down } from '@icon-park/vue-next';
import { NButton, NPopover, NTooltip } from 'naive-ui';
import { editorHandles } from '../../tools/handle';
import { Editor } from '@tiptap/vue-3';
import { storeToRefs } from 'pinia';
import { useEditorCtx } from '../../editorCtx';

const {editor} = storeToRefs(useEditorCtx())

</script>
