<template>
	<TipButtonItem tip="列表" @click="editorHandles.toggleBulletList({ editor })" :isActive="editorHandles.isBulletListActive({ editor })">
		<template #icon>
			<ListTwo theme="outline" />
		</template>
		
	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3';
import { editorHandles } from '../../tools/handle';
import TipButtonItem from '../TipButtonItem.vue';
import { ListTwo } from '@icon-park/vue-next';

defineProps<{editor: Editor}>()

</script>

