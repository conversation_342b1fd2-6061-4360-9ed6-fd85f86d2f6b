<template>
	<TipButtonItem tip="增加缩进" @click="editorHandles.sink({ editor })">
		<template #icon>
			<IndentRight theme="outline" />
		</template>
		
	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3';
import { editorHandles } from '../../tools/handle';
import TipButtonItem from '../TipButtonItem.vue';
import { IndentRight } from '@icon-park/vue-next';

defineProps<{editor: Editor}>()

</script>

