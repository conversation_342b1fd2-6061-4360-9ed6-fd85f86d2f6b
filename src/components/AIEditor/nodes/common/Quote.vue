<template>
	<TipButtonItem tip="引用" @click="editorHandles.toggleBlockquote({ editor })" :isActive="editorHandles.isBlockquoteActive({ editor })">
		<template #icon>
			<Quote theme="outline" />
		</template>

	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3';
import { editorHandles } from '../../tools/handle';
import TipButtonItem from '../TipButtonItem.vue';
import { Quote } from '@icon-park/vue-next';

defineProps<{editor: Editor}>()

</script>

