<template>
	<TipButtonItem tip="待办事项" @click="editorHandles.toggleTodoList({ editor })" :isActive="editorHandles.isTodoListActive?.({ editor })">
		<template #icon>
			<ListCheckbox theme="outline" />
		</template>

	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3';
import { editorHandles } from '../../tools/handle';
import TipButtonItem from '../TipButtonItem.vue';
import { ListCheckbox } from '@icon-park/vue-next';

defineProps<{editor: Editor}>()

</script>

