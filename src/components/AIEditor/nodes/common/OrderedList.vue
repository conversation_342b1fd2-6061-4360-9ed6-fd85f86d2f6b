<template>
	<TipButtonItem tip="有序列表" @click="editorHandles.toggleOrderedList({ editor })" :isActive="editorHandles.isOrderedListActive({ editor })">
		<template #icon>
			<OrderedList theme="outline" />
		</template>
		
	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3';
import { editorHandles } from '../../tools/handle';
import TipButtonItem from '../TipButtonItem.vue';
import { OrderedList } from '@icon-park/vue-next';

defineProps<{editor: Editor}>()

</script>

