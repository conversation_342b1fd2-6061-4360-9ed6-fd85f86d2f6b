<template>
	<TipButtonItem tip="倾斜" @click="editorHandles.toggleItalic({ editor })" :isActive="editorHandles.isItalicActive({ editor })">
		<template #icon>
			<TextItalic theme="outline" />
		</template>
		
		
	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3';
import { editorHandles } from '../../tools/handle';
import TipButtonItem from '../TipButtonItem.vue';
import { TextItalic } from '@icon-park/vue-next';

defineProps<{editor: Editor}>()

</script>

