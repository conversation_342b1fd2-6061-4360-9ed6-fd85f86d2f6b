<template>
	<TipButtonItem tip="加粗" @click="editorHandles.toggleBold({ editor })" :isActive="editorHandles.isBoldActive({ editor })">
		<template #icon>
			<TextBold theme="outline" />
		</template>
		
	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3';
import { editorHandles } from '../../tools/handle';
import TipButtonItem from '../TipButtonItem.vue';
import { TextBold } from '@icon-park/vue-next';

defineProps<{editor: Editor}>()

</script>

