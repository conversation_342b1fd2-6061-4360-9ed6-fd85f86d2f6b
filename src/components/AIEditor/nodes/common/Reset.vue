<template>
	<TipButtonItem tip="清空" @click="editorCtx.reset()">
		<template #icon>
			<Clear theme="outline" />
		</template>
	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor} from '@tiptap/vue-3';
import TipButtonItem from '../TipButtonItem.vue';
import { Clear } from '@icon-park/vue-next';
import { useEditorCtx } from '../../editorCtx';

defineProps<{editor: Editor}>()

const editorCtx = useEditorCtx()

</script>

