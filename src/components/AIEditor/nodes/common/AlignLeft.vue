<template>
	<TipButtonItem tip="左对齐" @click="editorHandles.setTextAlign({ editor, align: 'left' })" :isActive="editorHandles.isTextAlignActive({ editor, align: 'left'})">
		<template #icon>
			<AlignTextLeft theme="outline" />
		</template>

	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3';
import { editorHandles } from '../../tools/handle';
import TipButtonItem from '../TipButtonItem.vue';
import { AlignTextLeft } from '@icon-park/vue-next';

defineProps<{editor: Editor}>()

</script>

