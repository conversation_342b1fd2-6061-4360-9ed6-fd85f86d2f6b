<template>
	<TipButtonItem tip="重做" @click="editorHandles.redo({ editor })" :disabled="!editorHandles.canRedo({ editor })">
		<template #icon>
			<Redo theme="outline" />
		</template>
		
	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3';
import { editorHandles } from '../../tools/handle';
import TipButtonItem from '../TipButtonItem.vue';
import { Redo } from '@icon-park/vue-next';

defineProps<{editor: Editor}>()

</script>

