<template>
	<TipButtonItem tip="居中" @click="editorHandles.setTextAlign({ editor, align: 'center' })" :isActive="editorHandles.isTextAlignActive({ editor, align: 'center'})">
		<template #icon>
			<AlignTextCenter theme="outline" />
		</template>
	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3';
import { editorHandles } from '../../tools/handle';
import TipButtonItem from '../TipButtonItem.vue';
import { AlignTextCenter } from '@icon-park/vue-next';

defineProps<{editor: Editor}>()

</script>

