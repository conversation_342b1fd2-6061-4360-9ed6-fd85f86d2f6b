<template>
	<TipButtonItem tip="下划线" @click="editorHandles.toggleUnderline({ editor })" :isActive="editorHandles.isUnderlineActive({ editor })">
		<template #icon>
			<TextUnderline theme="outline" />
		</template>
		
	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3';
import { editorHandles } from '../../tools/handle';
import TipButtonItem from '../TipButtonItem.vue';
import { TextUnderline } from '@icon-park/vue-next';

defineProps<{editor: Editor}>()

</script>

