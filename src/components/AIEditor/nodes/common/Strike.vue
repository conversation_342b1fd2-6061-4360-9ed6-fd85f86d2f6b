<template>
	<TipButtonItem tip="删除线" @click="editorHandles.toggleStrike({ editor })" :isActive="editorHandles.isStrikeActive?.({ editor })">
		<template #icon>
			<Strikethrough theme="outline" />
		</template>

	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3';
import { editorHandles } from '../../tools/handle';
import TipButtonItem from '../TipButtonItem.vue';
import { Strikethrough } from '@icon-park/vue-next';

defineProps<{editor: Editor}>()

</script>

