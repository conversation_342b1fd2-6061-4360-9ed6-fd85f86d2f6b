<template>
	<TipButtonItem tip="右对齐" @click="editorHandles.setTextAlign({ editor, align: 'right' })" :isActive="editorHandles.isTextAlignActive({ editor, align: 'right'})">
		<template #icon>
			<AlignTextRight theme="outline" />
		</template>

	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3';
import { editorHandles } from '../../tools/handle';
import TipButtonItem from '../TipButtonItem.vue';
import { AlignTextRight } from '@icon-park/vue-next';

defineProps<{editor: Editor}>()

</script>

