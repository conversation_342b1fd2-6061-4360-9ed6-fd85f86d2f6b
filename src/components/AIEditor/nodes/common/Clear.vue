<template>
	<TipButtonItem tip="清除格式" @click="editorHandles.clearFormat({ editor })">
		<template #icon>
			<ClearFormat theme="outline" />
		</template>
		
	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3';
import TipButtonItem from '../TipButtonItem.vue';
import { ClearFormat } from '@icon-park/vue-next';
import { editorHandles } from '../../tools/handle';

const props = defineProps<{editor: Editor}>()


</script>

