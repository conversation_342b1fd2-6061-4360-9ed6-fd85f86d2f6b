<template>
	<TipButtonItem tip="撤销" @click="editorHandles.undo({ editor })" :disabled="!editorHandles.canUndo({ editor })">
		<template #icon>
			<Undo theme="outline" />
		</template>
		
	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3';
import { editorHandles } from '../../tools/handle';
import TipButtonItem from '../TipButtonItem.vue';
import { Undo } from '@icon-park/vue-next';

defineProps<{editor: Editor}>()

</script>

