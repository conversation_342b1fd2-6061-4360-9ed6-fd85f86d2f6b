<template>
	<TipButtonItem tip="代码块" @click="editorHandles.toggleCode({ editor })" :isActive="editorHandles.isCodeActive({ editor })">
		<template #icon>
			<CodeIcon theme="outline" />
		</template>

	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3';
import { editorHandles } from '../../tools/handle';
import TipButtonItem from '../TipButtonItem.vue';
import {Code as CodeIcon} from "@icon-park/vue-next"

defineProps<{editor: Editor}>()

</script>

