<template>
	<TipButtonItem tip="减小缩进" @click="editorHandles.lift({ editor })">
		<template #icon>
			<IndentLeft theme="outline" />
		</template>
		
	</TipButtonItem>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/vue-3';
import { editorHandles } from '../../tools/handle';
import TipButtonItem from '../TipButtonItem.vue';
import { IndentLeft } from '@icon-park/vue-next';

defineProps<{editor: Editor}>()

</script>

