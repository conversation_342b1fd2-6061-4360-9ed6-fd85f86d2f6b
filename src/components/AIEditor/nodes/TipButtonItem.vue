<template>
	<NTooltip>
		<template #trigger>
			<NButton class="" size="medium" :quaternary="!isActive" :secondary="isActive" @click="emit('click')" style="--n-padding: 12px;--n-font-weight: 600;"
				:disabled="disabled" tabindex="-1">
				<template #icon>
					<slot name="icon" />
				</template>
				<slot />
			</NButton>
		</template>
		{{ tip }}
	</NTooltip>
</template>

<script setup lang="ts">
import { NButton, NTooltip } from 'naive-ui'

defineProps<{
	tip: string
	isActive?: boolean
	disabled?: boolean
}>()

const emit = defineEmits<{
	(e: 'click'): void
}>()
</script>
