import AlignCenter from "./common/AlignCenter.vue";
import AlignLeft from "./common/AlignLeft.vue";
import AlignRight from "./common/AlignRight.vue";
import Bold from "./common/Bold.vue";
import Clear from "./common/Clear.vue";
import Code from "./common/Code.vue";
import IndentLeft from "./common/IndentLeft.vue";
import IndentRight from "./common/IndentRight.vue";
import Italic from "./common/Italic.vue";
import List from "./common/List.vue";
import OrderedList from "./common/OrderedList.vue";
import Quote from "./common/Quote.vue";
import Redo from "./common/Redo.vue";
import Reset from "./common/Reset.vue";
import Strikethrough from "./common/Strike.vue";
import Todo from "./common/Todo.vue";
import Underline from "./common/Underline.vue";
import Undo from "./common/Undo.vue";

export const commonNodes = {undo: Undo,
	reset: Reset,
	redo: Redo,
	headding: null,
	addFontSize: null,
	reduceFontSize: null,
	bold: Bold,
	italic: Italic,
	underline: Underline,
	strikethrough: Strikethrough,
	list: List,
	orderedList: OrderedList,
	todo: Todo,
	indentLeft: IndentLeft,
	indentRight: IndentRight,
	alignLeft: AlignLeft,
	alignCenter: AlignCenter,
	alignRight: AlignRight,
	quote: Quote,
	code: Code,
	clearFormat: Clear,
}
