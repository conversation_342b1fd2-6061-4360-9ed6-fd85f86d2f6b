import { Editor } from "@tiptap/vue-3";

export type Level = 1 | 2 | 3 | 4 | 5 | 6;
export type Align = 'left' | 'right' | 'center' | 'justify';

const _map: Record<string, (options: { editor: Editor, level?: Level, emitUpdate?: boolean, align?: Align }) => void> = {
	/**
	 * 清空编辑器
	 * @param param0
	 * @param param0.emitUpdate 是否触发update
	 * @returns
	 */
	clearContent: ({ editor, emitUpdate = true }) =>
		editor.commands.clearContent(emitUpdate),
	// 撤销
	undo: ({ editor }) => editor?.chain().focus().undo().run(),
	// 是否可以撤销
	canUndo: ({ editor }) => editor?.can().undo() ?? false,
	// 重做
	redo: ({ editor }) => editor?.chain().focus().redo().run(),
	// 是否可以重做
	canRedo: ({ editor }) => editor?.can().redo() ?? false,
	// 设置为标题
	setHeading: ({ editor, level = 1 }) =>
		editor.chain().focus().toggleHeading({ level }).run(),
	// 是否为标题
	isHeadingActive: ({ editor }) => editor?.isActive("heading"),
	// 设置为段落
	setParagraph: ({ editor }) => editor.chain().focus().setParagraph().run(),
	// 设置为标题
	isParagraphActive: ({ editor }) => editor?.isActive("paragraph"),
	// 设置为标题
	toggleBold: ({ editor }) => editor.chain().focus().toggleBold().run(),
	// 是否为粗体
	isBoldActive: ({ editor }) => editor?.isActive("bold"),
	// 设置为斜体
	toggleItalic: ({ editor }) => editor.chain().focus().toggleItalic().run(),
	// 是否为斜体
	isItalicActive: ({ editor }) => editor?.isActive("italic"),
	// 设置为删除线
	toggleStrike: ({ editor }) => editor.chain().focus().toggleStrike().run(),
	// 是否为删除线
	isStrikeActive: ({ editor }) => editor?.isActive("strike"),
	// 设置为无序列表
	toggleBulletList: ({ editor }) =>
		editor.chain().focus().toggleBulletList().run(),
	// setBulletList: ({ editor }) => editor.chain().focus().setBulletList().run(),
	// 是否为无序列表
	isBulletListActive: ({ editor }) => editor?.isActive("bulletList"),
	// 设置为有序列表
	toggleOrderedList: ({ editor }) =>
		editor.chain().focus().toggleOrderedList().run(),
	// setOrderedList: ({ editor }) => editor.chain().focus().setOrderedList().run(),
	// 是否为有序列表
	isOrderedListActive: ({ editor }) => editor?.isActive("orderedList"),
	// 设置为代码块
	toggleCode: ({ editor }) => editor.chain().focus().toggleCode().run(),
	// 是否为代码块
	isCodeActive: ({ editor }) => editor?.isActive("code"),
	// 设置为引用
	toggleBlockquote: ({ editor }) =>
		editor.chain().focus().toggleBlockquote().run(),
	setBlockquote: ({ editor }) => editor.chain().focus().setBlockquote().run(),
	// 是否为引用
	isBlockquoteActive: ({ editor }) => editor?.isActive("blockquote"),
	// 是否为图片
	isImageActive: ({ editor }) => editor?.isActive("image"),
	// 设置为代码块
	toggleCodeBlock: ({ editor }) =>
		editor.chain().focus().toggleCodeBlock().run(),
	setCodeBlock: ({ editor }) => editor.chain().focus().setCodeBlock().run(),
	// 清除格式
	clearFormat: ({ editor }) =>
		editor.chain().focus().clearNodes().unsetAllMarks().run(),
	// 设置为下划线
	toggleUnderline: ({ editor }) => editor.commands.toggleUnderline(),
	// 是否为下划线
	isUnderlineActive: ({ editor }) => editor?.isActive("underline"),
	// 左对齐
	// justifyLeft: ({ editor }) =>
	// 	editor.chain().focus().setTextAlign("left").run(),
	// 是否为左对齐
	isJustifyLeftActive: ({ editor }) => editor?.isActive("textAlign", "left"),
	// 居中
	// justifyCenter: ({ editor }) =>
	// 	editor.chain().focus().setTextAlign("center").run(),
	// 是否为居中
	isJustifyCenterActive: ({ editor }) =>
		editor?.isActive("textAlign", "center"),
	// 右对齐
	// justifyRight: ({ editor }) =>
	// 	editor.chain().focus().setTextAlign("right").run(),
	// 是否为右对齐
	isJustifyRightActive: ({ editor }) => editor?.isActive("textAlign", "right"),
	// 减少缩进
	lift: ({ editor }) => editor.chain().liftListItem("listItem").run(),
	// 增加缩进
	sink: ({ editor }) => editor.chain().sinkListItem("listItem").run(),
	// 待办列表
	toggleTaskList: ({ editor }) => editor.chain().focus().toggleTaskList().run(),
	// setTaskList: ({ editor }) => editor.chain().focus().setTaskList().run(),
	// 是否为待办列表
	isTaskListActive: ({ editor }) => editor?.isActive("taskList"),

	// 对齐
	setTextAlign: ({ editor, align='left' }) => editor.commands.setTextAlign(align),
	// 是否为对齐
	isTextAlignActive: ({ editor, align='left' }) => editor?.isActive("textAlign", align),
};

export const editorHandles: Record<
	keyof typeof _map,
	(options: { editor: Editor; level?: Level, align?: Align, emitUpdate?: boolean }) => any
> = _map;
