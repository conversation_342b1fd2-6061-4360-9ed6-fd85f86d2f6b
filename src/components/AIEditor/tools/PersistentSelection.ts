import { Extension } from '@tiptap/core'
// import { marked } from 'marked'
import { Plugin, PluginKey, TextSelection } from 'prosemirror-state'
import { Decoration, DecorationSet } from 'prosemirror-view'
import '@tiptap/core'

// 定义选择信息的接口
export interface SelectionInfo {
  from: number
  to: number
  text?: string
}

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
    persistentSelection: {
      /**
       * 替换持久化选择的文本
       */
      replaceSelectedText: (text: string) => ReturnType

      /**
       * 在选中文本下方插入内容
       */
      insertBelowSelectedText: (text: string) => ReturnType

      /**
       * 获取当前选择信息
       */
      // getSelectionInfo: () => ReturnType

      /**
       * 清除持久化选择
       */
      clearPersistentSelection: () => ReturnType
    }
  }
}

export const PersistentSelection = Extension.create({
	name: 'persistentSelection',

	addStorage() {
    return {
      selectionInfo: null as SelectionInfo | null,
      savedSelection: null as SelectionInfo | null, // 添加到存储中
    }
  },

	// @ts-ignore
	addCommands() {
		return {
			 // 替换持久化选择的文本
      replaceSelectedText: (text: string) => ({ editor, commands }) => {
        const selectionInfo = this.storage.selectionInfo
        if (!selectionInfo) return false
        // const content = marked.parse(text)

        // 创建一个事务，删除选中的文本并插入新文本
        const tr = editor.state.tr
          .deleteRange(selectionInfo.from, selectionInfo.to)
          .insertText(text, selectionInfo.from)

        // 计算新文本的结束位置
        const newTo = selectionInfo.from + text.length

        // 设置选择范围为新插入的文本
        tr.setSelection(TextSelection.create(tr.doc, selectionInfo.from, newTo))

        // 应用事务
        editor.view.dispatch(tr)

        // 清除持久化选择
        this.storage.selectionInfo = null

        // 重新聚焦编辑器
        editor.commands.focus()
        return true
      },
			// 插入到选中文本下方
			insertBelowSelectedText: (text: string) => ({ editor, commands }) => {
        const selectionInfo = this.storage.selectionInfo
        if (!selectionInfo) return false

        // 创建一个事务，在选中区域下方插入新文本
        const tr = editor.state.tr.insertText(text, selectionInfo.to)

        // 计算新文本的开始和结束位置
        const newFrom = selectionInfo.to
        const newTo = newFrom + text.length

        // 设置选择范围为新插入的文本
        tr.setSelection(TextSelection.create(tr.doc, newFrom, newTo))

        // 应用事务
        editor.view.dispatch(tr)

        // 清除持久化选择
        this.storage.selectionInfo = null

        // 重新聚焦编辑器
        editor.commands.focus()
        return true
      },
			clearPersistentSelection: () => ({ editor, commands }) => {
        this.storage.selectionInfo = null
        this.storage.savedSelection = null // 同时清除savedSelection
        return true
      }
		}
	},

	addProseMirrorPlugins() {
		const extensionThis = this

		return [
			new Plugin({
				key: new PluginKey('persistentSelection'),
				props: {
					decorations(state) {
						if (!extensionThis.storage.savedSelection) return DecorationSet.empty

						// 创建一个装饰，应用到选中区域
						return DecorationSet.create(state.doc, [
							Decoration.inline(extensionThis.storage.savedSelection.from, extensionThis.storage.savedSelection.to, {
								class: 'persistent-selection'
							})
						])
					},
					handleDOMEvents: {
						click: (view, event) =>{
							if((event.target as HTMLElement)?.classList.contains('persistent-selection')){
							// 	extensionThis.storage.savedSelection = null
							}
						}
					}
				},
				view(editorView) {
					return {
						update: (view, prevState) => {
							// @ts-ignore
							const { state, focused } = view

							// 如果编辑器有焦点，保存当前选择
							if (focused) {
								const { from, to } = state.selection
								if (from !== to) {
									extensionThis.storage.savedSelection = { from, to }
									extensionThis.storage.selectionInfo = extensionThis.storage.savedSelection
								} else {
									// 当编辑器获得焦点且没有选择时，清除持久化选择
									extensionThis.storage.savedSelection = null
									extensionThis.storage.selectionInfo = null
								}
							}
						},
						// 添加焦点处理函数
						focus: () => {
							// 当编辑器重新获得焦点时，清除持久化选择
							// 但保留当前选择状态
							const { from, to } = editorView.state.selection
							if (from === to) {
								extensionThis.storage.savedSelection = null
								extensionThis.storage.selectionInfo = null
								// 强制重新渲染以清除装饰
								editorView.dispatch(editorView.state.tr)
							}
							return false
						},
						destroy: () => {
              extensionThis.storage.savedSelection = null
              extensionThis.storage.selectionInfo = null
            }
					}
				}
			})
		]
	}
})
