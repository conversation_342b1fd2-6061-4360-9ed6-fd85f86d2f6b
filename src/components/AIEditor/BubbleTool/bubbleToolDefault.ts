import { NButton } from "naive-ui";
import { h } from "vue";
import AIBubbleButton from "../AITools/AIBubbleButton.vue";
import { BubbleToolsGroupType } from "../type";
import ApplyNode from "../nodes/bubbles/ApplyNode.vue";
import FontSize from "../nodes/bubbles/FontSize.vue";

export const bubbleToolsDefault: BubbleToolsGroupType = [
	// ["undo", "redo"],
  [
    AIBubbleButton
  ],
	[ApplyNode, FontSize],
	[
		// "headding",
		// "addFontSize",
		// "reduceFontSize",
		"bold",
		"italic",
		"underline",
		"strikethrough",
	]
	// ["list", "orderedList", "todo", "indentLeft", "indentRight"],
	// ["alignLeft", "alignCenter", "alignRight"],
	// ["quote", "code", "clearFormat"],
];
