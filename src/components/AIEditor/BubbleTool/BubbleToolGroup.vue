<template>
	<div v-if="!isFirst" class="mx-2 text-neutral-400">|</div>
  <div class="flex gap-1 items-center">
		<template v-for="(item, index) in tools" :key="index" class="flex gap-2 items-center">
			<template v-if="typeof item == 'string'">
				<component v-if="item in commonNodes" :is="commonNodes[item]" :editor="editor" />
				<ButtonItem v-else >{{ item }}</ButtonItem>
			</template>
			<template v-else-if="isVNode(item) || isVueComponent(item)">
				<component :is="item" :editor="editor" />
			</template>
		</template>
  </div>
</template>

<script setup lang="ts">
import { defineProps, isVNode, } from 'vue'
import { ToolItemType } from '../type';
import { commonNodes } from '../nodes/commonNodes';
import ButtonItem from '../nodes/ButtonItem.vue';
const props = defineProps<{
	tools: ToolItemType[],
	isFirst: boolean,
	editor: any,
	editorKey?: string,
}>()

function isVueComponent(obj) {
  return obj && typeof obj === 'object' && 'setup' in obj || 'render' in obj;
}
</script>
