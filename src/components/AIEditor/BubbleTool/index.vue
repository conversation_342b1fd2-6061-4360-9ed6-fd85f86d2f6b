<template>
	<BubbleMenu :editor="editor" v-if="editor">
		<div class="flex gap-1 bg-white shadow-dropdown p-1 rounded-xl items-center">
			<template v-for="(item, index) in tools" :key="index" class="flex gap-2 items-center">
				<BubbleToolsGroup :editor="editor" :tools="item" :isFirst="index == 0" />
			</template>
		</div>
	</BubbleMenu>
</template>

<script setup lang="ts">
// @ts-ignore
import { BubbleMenu } from '@tiptap/vue-3/menus'
import BubbleToolsGroup from './BubbleToolGroup.vue'

const props = defineProps<{
	editor: any
	tools: any
}>()



</script>

<style lang="less">
	.tippy-box {
		max-width: 100vw !important
	}
</style>
