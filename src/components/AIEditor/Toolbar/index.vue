<template>
	<div class="flex gap-1 items-center justify-center flex-wrap px-4 py-2 text-neutral-700" @click.stop>
		<template v-for="(row, index) in tools" :key="index">
			<ToolbarGroup :editor="editor" :tools="row" :isFirst="index == 0" :editorKey="editorKey" />
		</template>
	</div>
</template>

<script setup lang="ts">
import type { Editor } from '@tiptap/core'
import ToolbarGroup from './ToolbarGroup.vue'
import { ToolbarsGroupType, ToolItemType } from '../type';

interface Props {
	editor: Editor | null,
	tools?: ToolbarsGroupType,
	editorKey: string,
}

const props = withDefaults(defineProps<Props>(), {})
</script>

<style lang="less" scoped>

</style>
