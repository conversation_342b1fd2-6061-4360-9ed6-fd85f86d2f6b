<template>
	<div v-if="!isFirst" class="mx-2 text-neutral-400">|</div>
  <div class="flex gap-1 items-center">
		<template v-for="(item, index) in tools" :key="index" class="flex gap-2 items-center">
			<template v-if="typeof item == 'string'">
				<component v-if="item in commonNodes" :is="commonNodes[item]" :editor="editor" :disabled="editorCtx.disabled" />
				<ButtonItem v-else :disabled="editorCtx.disabled">{{ item }}</ButtonItem>
			</template>
		</template>
  </div>
</template>

<script setup lang="ts">
import { defineProps } from 'vue'
import { ToolItemType } from '../type';
import { commonNodes } from '../nodes/commonNodes';
import ButtonItem from '../nodes/ButtonItem.vue';
import type { Component } from 'vue';
import { useEditorCtx } from '../editorCtx';
const props = defineProps<{
	tools: (string | ToolItemType | Component)[],
	isFirst: boolean,
	editor: any,
	editorKey: string,
}>()
const editorCtx = useEditorCtx()
</script>
