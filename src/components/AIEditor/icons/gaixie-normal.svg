<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="200" height="200" viewBox="0 0 24 24">
	<defs>
		<clipPath id="master_svg0_42_4525">
			<rect x="0" y="0" width="200" height="200" rx="0" />
		</clipPath>
	</defs>
	<g>
		<rect x="0" y="0" width="200" height="200" rx="0" fill-opacity="1" />
		<g>
			<g>
				<rect x="1" y="2" width="2" height="7" rx="0" fill="currentColor" fill-opacity="1" />
			</g>
			<g transform="matrix(0,-1,1,0,-2,4)">
				<rect x="1" y="3" width="2" height="8" rx="0" fill="currentColor" fill-opacity="1" />
			</g>
		</g>
		<g transform="matrix(1,0,0,-1,0,46)">
			<g>
				<rect x="1" y="24" width="2" height="7" rx="0" fill="currentColor" fill-opacity="1" />
			</g>
			<g transform="matrix(0,-1,1,0,-24,26)">
				<rect x="1" y="25" width="2" height="8" rx="0" fill="currentColor" fill-opacity="1" />
			</g>
		</g>
		<g transform="matrix(-1,0,0,1,46,0)">
			<g>
				<rect x="23" y="2" width="2" height="7" rx="0" fill="currentColor" fill-opacity="1" />
			</g>
			<g transform="matrix(0,-1,1,0,20,26)">
				<rect x="23" y="3" width="2" height="8" rx="0" fill="currentColor" fill-opacity="1" />
			</g>
		</g>
		<g transform="matrix(-1,0,0,-1,46,46)">
			<g>
				<rect x="23" y="24" width="2" height="7" rx="0" fill="currentColor" fill-opacity="1" />
			</g>
			<g transform="matrix(0,-1,1,0,-2,48)">
				<rect x="23" y="25" width="2" height="8" rx="0" fill="currentColor" fill-opacity="1" />
			</g>
		</g>
		<g>
			<path
				d="M6.225866,10.6814L7.91448,10.6814Q7.5165299999999995,10.15976,6.978751,9.57358L7.8607,8.93901L6.6453299999999995,8.93901L6.6453299999999995,7.831189999999999L8.91474,7.831189999999999Q8.6835,7.487019999999999,8.38772,7.08906L9.92576,6.78791L10.56034,7.831189999999999L12.458680000000001,7.831189999999999L13.18468,6.77715L14.78725,7.099819999999999L14.13654,7.831189999999999L16.4113,7.831189999999999L16.4113,8.93901L15.19058,8.93901L16.1102,9.63812Q15.66382,10.06834,15.07765,10.6814L16.82,10.6814L16.82,11.778459999999999L6.225866,11.778459999999999L6.225866,10.6814ZM11.02282,10.6814L12.001570000000001,10.6814L12.001570000000001,8.93901L11.02282,8.93901L11.02282,10.6814ZM9.076080000000001,10.111360000000001L8.33932,10.6814L9.56007,10.6814L9.56007,8.93901L7.98977,8.93901Q8.452259999999999,9.42301,9.076080000000001,10.111360000000001ZM14.97547,8.93901L13.46432,8.93901L13.46432,10.6814L14.5829,10.6814L13.91605,10.14363Q14.50223,9.530560000000001,14.97547,8.93901ZM7.46275,17.4143L7.46275,12.39153L15.58316,12.39153L15.58316,17.4143L14.12041,17.4143L14.12041,16.973399999999998L8.9255,16.973399999999998L8.9255,17.4143L7.46275,17.4143ZM14.12041,13.49934L8.9255,13.49934L8.9255,14.1769L14.12041,14.1769L14.12041,13.49934ZM8.9255,15.8656L14.12041,15.8656L14.12041,15.188L8.9255,15.188L8.9255,15.8656Z"
				fill="currentColor" fill-opacity="1" />
		</g>
	</g>
</svg>
