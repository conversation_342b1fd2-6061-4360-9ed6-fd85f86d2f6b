<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="none" version="1.1" width="200" height="200" viewBox="0 0 24 24">
	<defs>
		<clipPath id="master_svg0_42_4841">
			<rect x="0" y="0" width="200" height="200" rx="0" />
		</clipPath>
	</defs>
	<g>
		<rect x="0" y="0" width="200" height="200" rx="0" fill-opacity="1" />
		<g>
			<g>
				<rect x="1" y="2" width="2" height="7" rx="0" fill="currentColor" fill-opacity="1" />
			</g>
			<g transform="matrix(0,-1,1,0,-2,4)">
				<rect x="1" y="3" width="2" height="8" rx="0" fill="currentColor" fill-opacity="1" />
			</g>
		</g>
		<g transform="matrix(1,0,0,-1,0,46)">
			<g>
				<rect x="1" y="24" width="2" height="7" rx="0" fill="currentColor" fill-opacity="1" />
			</g>
			<g transform="matrix(0,-1,1,0,-24,26)">
				<rect x="1" y="25" width="2" height="8" rx="0" fill="currentColor" fill-opacity="1" />
			</g>
		</g>
		<g transform="matrix(-1,0,0,1,46,0)">
			<g>
				<rect x="23" y="2" width="2" height="7" rx="0" fill="currentColor" fill-opacity="1" />
			</g>
			<g transform="matrix(0,-1,1,0,20,26)">
				<rect x="23" y="3" width="2" height="8" rx="0" fill="currentColor" fill-opacity="1" />
			</g>
		</g>
		<g transform="matrix(-1,0,0,-1,46,46)">
			<g>
				<rect x="23" y="24" width="2" height="7" rx="0" fill="currentColor" fill-opacity="1" />
			</g>
			<g transform="matrix(0,-1,1,0,-2,48)">
				<rect x="23" y="25" width="2" height="8" rx="0" fill="currentColor" fill-opacity="1" />
			</g>
		</g>
		<g>
			<path
				d="M6.279643,13.58539L6.559286,9.76718L8.36621,9.76718L8.36621,8.508790000000001L6.333421,8.508790000000001L6.333421,7.22888L9.69989,7.22888L9.69989,11.04709L7.8069299999999995,11.04709L7.7101299999999995,12.30548L9.58158,12.30548Q9.53856,14.9298,9.47134,15.7096Q9.404119999999999,16.4894,9.17825,16.8228Q8.95239,17.1562,8.57594,17.2665Q8.1995,17.3767,6.656086,17.349800000000002Q6.570042,16.8228,6.344176,15.9086Q7.15622,15.9408,7.48964,15.9408Q7.91448,15.9408,8.03279,15.6531Q8.1511,15.3654,8.19412,13.58539L6.279643,13.58539ZM15.83591,14.5964Q16.368299999999998,15.672,16.916800000000002,16.984099999999998L15.67996,17.4789L15.40031,16.7905Q12.926549999999999,17.0863,10.00105,17.360599999999998L9.74292,15.9839L12.56086,15.7795L12.56086,14.5964L10.14087,14.5964L10.14087,11.186910000000001L12.56086,11.186910000000001L12.56086,10.44478L10.4958,10.44478L10.4958,7.23964L16.0026,7.23964L16.0026,10.44478L13.937560000000001,10.44478L13.937560000000001,11.186910000000001L16.3791,11.186910000000001L16.3791,14.5964L15.83591,14.5964ZM14.64743,8.42275L11.850999999999999,8.42275L11.850999999999999,9.261669999999999L14.64743,9.261669999999999L14.64743,8.42275ZM15.04538,13.4133L15.04538,12.37002L13.937560000000001,12.37002L13.937560000000001,13.4133L15.04538,13.4133ZM11.47455,13.4133L12.56086,13.4133L12.56086,12.37002L11.47455,12.37002L11.47455,13.4133ZM15.82516,14.5964L13.937560000000001,14.5964L13.937560000000001,15.6558L14.86254,15.5644L14.62592,15.0481L15.82516,14.5964Z"
				fill="currentColor" fill-opacity="1" />
		</g>
	</g>
</svg>
