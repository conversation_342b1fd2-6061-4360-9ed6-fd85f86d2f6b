# 编辑器工具栏组件

一个功能完整、结构清晰的富文本编辑器工具栏组件，基于 TipTap 编辑器构建，支持自定义和扩展。

## 功能特性

### 🎯 核心功能
- **撤销/重做**: 支持操作历史管理
- **标题选择**: H1-H6 标题级别选择
- **文本格式化**: 粗体、斜体、删除线
- **列表支持**: 有序列表、无序列表
- **引用和代码**: 块引用、行内代码
- **格式清除**: 一键清除所有格式

### 🎨 界面设计
- **响应式布局**: 适配不同屏幕尺寸
- **深色模式**: 支持明暗主题切换
- **直观图标**: 清晰的视觉指示
- **状态反馈**: 激活状态高亮显示

### 🔧 技术特性
- **模块化设计**: 组件化架构，易于维护
- **TypeScript**: 完整的类型支持
- **可扩展性**: 支持自定义工具和插件
- **性能优化**: 按需加载，高效渲染

## 组件结构

```
src/components/Editor/Toolbar/
├── index.vue              # 主工具栏组件
├── ToolbarButton.vue      # 工具栏按钮组件
├── ColorPicker.vue        # 颜色选择器（预留）
├── FontSizeSelector.vue   # 字体大小选择器（预留）
├── HeadingSelector.vue    # 标题选择器
├── icons.vue              # 图标组件
└── README.md              # 文档说明
```

## 使用方法

### 基础使用

```vue
<template>
  <div>
    <Editor v-model="content" />
  </div>
</template>

<script setup>
import Editor from '@/components/Editor/index.vue'
import { ref } from 'vue'

const content = ref('<p>Hello World!</p>')
</script>
```

### 自定义工具栏

工具栏组件支持通过 props 传递编辑器实例：

```vue
<template>
  <EditorToolbar :editor="editor" />
  <EditorContent :editor="editor" />
</template>

<script setup>
import { useEditor, EditorContent } from '@tiptap/vue-3'
import EditorToolbar from '@/components/Editor/Toolbar/index.vue'
import StarterKit from '@tiptap/starter-kit'

const editor = useEditor({
  content: '<p>Hello World!</p>',
  extensions: [StarterKit]
})
</script>
```

## 键盘快捷键

| 快捷键 | 功能 |
|--------|------|
| `Ctrl+B` | 粗体 |
| `Ctrl+I` | 斜体 |
| `Ctrl+Z` | 撤销 |
| `Ctrl+Y` | 重做 |
| `/` | 斜杠命令 |

## 扩展功能

### 添加新的工具按钮

1. 在 `icons.vue` 中添加新图标
2. 在主工具栏组件中添加按钮
3. 实现对应的编辑器命令

```vue
<!-- 示例：添加下划线按钮 -->
<toolbar-button
  icon="underline"
  tooltip="下划线 (Ctrl+U)"
  :is-active="editor?.isActive('underline')"
  @click="editor?.chain().focus().toggleUnderline().run()"
/>
```

### 添加新的扩展

1. 安装 TipTap 扩展
```bash
npm install @tiptap/extension-underline
```

2. 在编辑器中注册扩展
```javascript
import Underline from '@tiptap/extension-underline'

const editor = useEditor({
  extensions: [
    StarterKit,
    Underline
  ]
})
```

## 预留功能

以下功能组件已创建但需要额外的 TipTap 扩展支持：

### 颜色选择器
- 文字颜色设置
- 背景颜色高亮
- 预定义颜色面板
- 自定义颜色选择

需要安装：
```bash
npm install @tiptap/extension-color @tiptap/extension-text-style
```

### 字体大小选择器
- 预定义字体大小
- 自定义字体大小
- 下拉选择界面

### 文本对齐
- 左对齐、居中、右对齐
- 两端对齐

需要安装：
```bash
npm install @tiptap/extension-text-align
```

## 样式定制

### 主题变量

工具栏使用 Tailwind CSS 类名，可以通过修改 CSS 变量来定制主题：

```css
.editor-toolbar {
  --toolbar-bg: #ffffff;
  --toolbar-border: #e5e7eb;
  --button-hover: #f3f4f6;
  --button-active: #dbeafe;
}
```

### 深色模式

组件自动支持深色模式，通过 `.dark` 类名切换：

```css
.dark .editor-toolbar {
  background-color: #1f2937;
  border-color: #4b5563;
}
```

## 最佳实践

1. **性能优化**: 使用 `v-if` 条件渲染大型工具栏
2. **用户体验**: 提供清晰的工具提示和状态反馈
3. **可访问性**: 确保键盘导航和屏幕阅读器支持
4. **响应式**: 在小屏幕上考虑工具栏的折叠和简化

## 故障排除

### 常见问题

1. **工具栏按钮不响应**
   - 检查编辑器实例是否正确传递
   - 确认相关扩展已正确安装和注册

2. **样式显示异常**
   - 确认 Tailwind CSS 已正确配置
   - 检查 CSS 导入顺序

3. **TypeScript 错误**
   - 确认所有依赖的类型定义已安装
   - 检查 props 类型定义是否正确

## 贡献指南

欢迎提交 Issue 和 Pull Request 来改进这个组件！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 创建 Pull Request

## 许可证

MIT License
