<template>
	<div class="relative h-full" @click.stop>
		<div class="overflow-hidden border-neutral-200 h-full transition-all duration-300 ease-out"
			:class="show ? 'w-[300px] p-6 border-r' : 'w-0 p-0 border-0'">
			<div class="flex flex-col h-full overflow-y-auto gap-2 w-[252px]">
				<div class="flex justify-between items-center font-medium">
					<span>目录</span>
					<NButton quaternary @click="handleManualClose">
						<template #icon>
							<Close size="16" />
						</template>
					</NButton>
				</div>
				<div class="flex-1 flex flex-col gap-3">
					<template v-for="item in tableContent" :key="item.id">
						<div class="text-sm cursor-pointer hover:text-primary text-neutral-700"
							:class="{ 'ml-4': item.level === 2, 'ml-8': item.level === 3, 'ml-12': item.level === 4, 'ml-16': item.level === 5, 'ml-20': item.level === 6 }"
							@click="handleClick(item)">
							{{ item.textContent }}</div>
					</template>
				</div>
			</div>
		</div>
		<NTooltip>
			<template #trigger>
				<div v-show="!show"
					class="absolute -right-11 top-10 flex items-center justify-center bg-popover w-11 h-10 border border-border rounded-tr-[20px] rounded-br-[20px] cursor-pointer !border-l-0 -ml-[1px] z-10"
					@click="show = true">
					<MindmapList class=" cursor-pointer" />
				</div>
			</template>
			目录
		</NTooltip>
	</div>
</template>


<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { useEditorCtx } from '../editorCtx';
import { ref, watch } from 'vue';
import { Close, MindmapList } from '@icon-park/vue-next';
import { NButton, NTooltip } from 'naive-ui';
const editorCtx = useEditorCtx()

const { tableContent } = storeToRefs(editorCtx)

const show = ref(false)
const manualClose = ref(false)

const handleClick = (item) => {
	item.dom.scrollIntoView({ behavior: "smooth" })
}

watch(() => tableContent!.value, (v) => {
	if(v.length && v.length > 1 && !manualClose.value) show.value = true
})

const handleManualClose = () => {
	show.value = false
	manualClose.value = true
}

</script>

<style lang="less" scoped></style>
