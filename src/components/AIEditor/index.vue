<template>
	<div class="editor-container h-full text-base" @click="editor?.commands.focus()">
		<div class="flex w-full h-full min-h-0 flex-1">
			<div class="flex-1 h-full flex">
				<TableContent />
				<div class="flex-1 flex flex-col">
					<!-- 工具栏 -->
					<editor-toolbar v-if="toolbars && editor" :editor="editor || null" :tools="toolbars" :editorKey="editorKey" />
					<!-- 编辑器内容 -->
					<div class="flex-1 overflow-y-auto relative ai-editor-container min-h-0 flex flex-col" ref="$editorContent">
						<slot name="contentHeader"></slot>
						<AIThink v-if="editor" />
						<editor-content class="relative ai-editor-content flex-1" role="dialog" :editor="editor" v-model="value" />
					</div>
					<slot name="footer"></slot>
				</div>
			</div>
			<Sider />
		</div>

		<!-- 气泡菜单 -->
		<BubbleTool :editor="editor" :tools="bubbleTools" v-if="editor && bubbleTools" />

		<!-- 拖动句柄 -->
		<DragTool v-if="editor" />
		<!-- AI悬浮窗 -->
		<AIEditorFloatChat />
	</div>
</template>

<script setup lang="ts">
import { useEditor, EditorContent, AnyExtension, Editor } from '@tiptap/vue-3'
import StarterKit from '@tiptap/starter-kit'
import BubbleMenu from '@tiptap/extension-bubble-menu'
// 拖动手柄
import DragTool from './DragTool/index.vue'

import NodeRange from '@tiptap/extension-node-range'
// 斜杠命令
import Slash from './SlashTool/slash'
import suggestion from './SlashTool/suggestion'
import { suggestionList } from './SlashTool/suggestionList'
// markdown支持
import { Markdown } from 'tiptap-markdown'
// 字体拓展
import {TextStyle} from '@tiptap/extension-text-style';
import FontSize from '@tiptap/extension-font-size';
// 任务列表拓展
import TaskItem from '@tiptap/extension-task-item'
import TaskList from '@tiptap/extension-task-list'
// 对齐拓展
import TextAlign from '@tiptap/extension-text-align'
// 目录
import { getHierarchicalIndexes, TableOfContents } from '@tiptap/extension-table-of-contents'
import TableContent from './TableContent/index.vue'

// 工具栏组件
import EditorToolbar from './Toolbar/index.vue'
import BubbleTool from './BubbleTool/index.vue'
import { inject, onMounted, provide, Ref, ref, watch } from 'vue'
import { BubbleToolsGroupType, ToolbarsGroupType } from './type'
import { Placeholder } from '@tiptap/extensions'

import Underline from '@tiptap/extension-underline'
import { PersistentSelection } from './tools/PersistentSelection'
import { useEditorCtx } from './editorCtx'
import Sider from './AITools/AISider/index.vue'
import AIThink from './AITools/AIThink/AIThink.vue'
import AIEditorFloatChat from './AITools/AIFloatChat/index.vue';

import { storeToRefs } from 'pinia'

const value = defineModel('modelValue', {
	type: String,
	default: '',
})
const props = defineProps<{
	toolbars?: ToolbarsGroupType,
	bubbleTools?: BubbleToolsGroupType
	placeholder?: string
}>()
const editorCtx = useEditorCtx()
const editorKey = ref<string>('')
const $editorContent = ref<HTMLElement | null>(null)
const { disabled } = storeToRefs(editorCtx)
// const title = ref<string>('') // 文档中第一个h标签

const onEditorUpdateProvide = inject('onEditorUpdate', (content, editorCtx:any) => { })

const editor = useEditor({
	content: value.value,
	extensions: [
		StarterKit.configure({
			heading: {
				levels: [1, 2, 3, 4, 5, 6],
			},
		}) as AnyExtension,
		TextStyle,  // 必需：提供文本样式支持
		FontSize.configure({ types: ['textStyle'] }), // 绑定到文本样式
		TaskList,
		TaskItem.configure({
			nested: true,
		}),
		TextAlign.configure({
			types: ['heading', 'paragraph'],
		}),
		Markdown.configure({
			transformPastedText: true, // 粘贴时自动转换
			breaks: true,
			// transformCopiedText: true,
			// tightLists: true,
		}),
		NodeRange.configure({
			// allow to select only on depth 0
			// depth: 0,
			key: null,
		}),
		Slash.configure({
			suggestion: suggestion(suggestionList),
		}),
		BubbleMenu.configure({
			options: {
				autoPlacement: true
			}
		}),
		Placeholder.configure({
			placeholder: ({ editor }) => {
				if (editor.isEmpty) return props.placeholder || '输入文本或按"/"键快速提示'
				return '输入文本或按"/"键快速提示'
			},
		}),
		Underline.configure({
			HTMLAttributes: {
				class: 'underline underline-offset-4',
			},
		}),
		PersistentSelection,
		TableOfContents.configure({
			onUpdate: content => {
				editorCtx.tableContent = content
				onEditorUpdateProvide(content, editorCtx)
			},
		})
		// AIThink
	],
	onUpdate: ({ editor }) => {
		value.value = editor.getHTML()
		editorKey.value = new Date().getTime().toString()
		editorCtx.editorKey = editorKey.value
	},
	onSelectionUpdate: ({ editor }) => {
		getTitle()
	}
})

onMounted(() => {
	// @ts-ignore
	editorCtx.editor = editor.value as Editor
	editorCtx.editorScrollElement = $editorContent.value
	// editorCtx.title = title.value
})

watch(() => disabled.value, (v) => {
	editor.value?.setEditable(!v)
})

const getTitle = () => {
	const heading = editor.value?.$node('heading')
	if (heading) {
		// title.value = heading.textContent
		editorCtx.title = heading.textContent
	}
}

</script>

<style lang="less">
@import "./index.css";

.editor-container {

	* {
		scrollbar-color: rgba(0, 0, 0, 0.25) transparent;
		scrollbar-width: thin;
	}

	.auto-height {
		height: 0;
		transition: height 0.3s ease;
		overflow: hidden;
		interpolate-size: allow-keywords;
		/* 全局启用对关键词的支持 */

		&.active {
			// height: calc-size(auto); /* 使用 calc-size 转换 auto 为可动画的值 */
			height: fit-content;
		}
	}
}

.scrollbar {
	scrollbar-color: rgba(0, 0, 0, 0.25) transparent;
	scrollbar-width: thin;

	&::-webkit-scrollbar {
		width: 3px;
		background-color: transparent;
	}

	&::-webkit-scrollbar-thumb {
		background-color: rgba(0, 0, 0, 0.25);
		border-radius: 3px;
	}
}

.ai-editor-content .tiptap {
	min-height: 100%;
}

.block-full-button .n-button__content {
	flex: 1;
}

.editor-container .n-button__content {
	flex: 1;
}

.editor-container {
	width: 100%;
}

.editor-content-wrapper {
	border: 1px solid #e5e7eb;
	border-radius: 0.5rem;
	min-height: 300px;
	padding: 1rem;
	background-color: white;

	:deep(.ProseMirror) {
		outline: none;
		min-height: 250px;
	}
}

.dark .editor-content-wrapper {
	background-color: #1f2937;
	border-color: #4b5563;
}

// .drag-handle {
// 	position: absolute;
// 	opacity: 1;
// 	transition: opacity ease-in 0.2s;
// 	border-radius: 0.25rem;
// 	transform: translate(-10px, 0.125em);
// 	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 10' style='fill: rgba(0, 0, 0, 0.5)'%3E%3Cpath d='M3,2 C2.44771525,2 2,1.55228475 2,1 C2,0.44771525 2.44771525,0 3,0 C3.55228475,0 4,0.44771525 4,1 C4,1.55228475 3.55228475,2 3,2 Z M3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 C3.55228475,4 4,4.44771525 4,5 C4,5.55228475 3.55228475,6 3,6 Z M3,10 C2.44771525,10 2,9.55228475 2,9 C2,8.44771525 2.44771525,8 3,8 C3.55228475,8 4,8.44771525 4,9 C4,9.55228475 3.55228475,10 3,10 Z M7,2 C6.44771525,2 6,1.55228475 6,1 C6,0.44771525 6.44771525,0 7,0 C7.55228475,0 8,0.44771525 8,1 C8,1.55228475 7.55228475,2 7,2 Z M7,6 C6.44771525,6 6,5.55228475 6,5 C6,4.44771525 6.44771525,4 7,4 C7.55228475,4 8,4.44771525 8,5 C8,5.55228475 7.55228475,6 7,6 Z M7,10 C6.44771525,10 6,9.55228475 6,9 C6,8.44771525 6.44771525,8 7,8 C7.55228475,8 8,8.44771525 8,9 C8,9.55228475 7.55228475,10 7,10 Z'%3E%3C/path%3E%3C/svg%3E");
// 	background-size: calc(0.5em + 0.375rem) calc(0.5em + 0.375rem);
// 	background-repeat: no-repeat;
// 	background-position: center;
// 	width: 1.2rem;
// 	height: 1.5rem;
// 	z-index: 50;
// 	cursor: grab;

// 	// &:hover {
// 	// 	background-color: var(--novel-stone-100);
// 	// 	transition: background-color 0.2s;
// 	// }

// 	// &:active {
// 	// 	background-color: var(--novel-stone-200);
// 	// 	transition: background-color 0.2s;
// 	// 	cursor: grabbing;
// 	// }

// 	// &.hide {
// 	// 	opacity: 0;
// 	// 	pointer-events: none;
// 	// }

// 	// @media screen and (max-width: 600px) {
// 	// 	display: none;
// 	// 	pointer-events: none;
// 	// }
// }

// .dark .drag-handle {
// 	background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 10' style='fill: rgba(255, 255, 255, 0.5)'%3E%3Cpath d='M3,2 C2.44771525,2 2,1.55228475 2,1 C2,0.44771525 2.44771525,0 3,0 C3.55228475,0 4,0.44771525 4,1 C4,1.55228475 3.55228475,2 3,2 Z M3,6 C2.44771525,6 2,5.55228475 2,5 C2,4.44771525 2.44771525,4 3,4 C3.55228475,4 4,4.44771525 4,5 C4,5.55228475 3.55228475,6 3,6 Z M3,10 C2.44771525,10 2,9.55228475 2,9 C2,8.44771525 2.44771525,8 3,8 C3.55228475,8 4,8.44771525 4,9 C4,9.55228475 3.55228475,10 3,10 Z M7,2 C6.44771525,2 6,1.55228475 6,1 C6,0.44771525 6.44771525,0 7,0 C7.55228475,0 8,0.44771525 8,1 C8,1.55228475 7.55228475,2 7,2 Z M7,6 C6.44771525,6 6,5.55228475 6,5 C6,4.44771525 6.44771525,4 7,4 C7.55228475,4 8,4.44771525 8,5 C8,5.55228475 7.55228475,6 7,6 Z M7,10 C6.44771525,10 6,9.55228475 6,9 C6,8.44771525 6.44771525,8 7,8 C7.55228475,8 8,8.44771525 8,9 C8,9.55228475 7.55228475,10 7,10 Z'%3E%3C/path%3E%3C/svg%3E");
// }


// 占位符
.tiptap p.is-empty::before {
	color: #adb5bd;
	content: attr(data-placeholder);
	float: left;
	height: 0;
	pointer-events: none;
}
</style>
