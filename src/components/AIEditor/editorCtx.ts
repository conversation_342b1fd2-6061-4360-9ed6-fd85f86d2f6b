import { Editor } from "@tiptap/vue-3";
import { marked } from "marked";
import { defineStore } from "pinia";
import { Node } from "prosemirror-model";
import { saveAs } from 'file-saver'; // 用于保存文件

export interface WritingContext {
	id?: number // 文档id
	editor?: Editor | null; // 编辑器实例
	editorKey?: any
	disabled: boolean; // 是否启用编辑器
	dragOverNode?: Node | null; // 拖拽节点
	dragOverFrom?: number; // 拖拽节点起始位置
	thinkContent: string; // 思考内容
	content: string; // 编辑器内容
	title: string; // 文章标题
	editorScrollElement: HTMLElement | null; // 编辑器滚动元素
	tableContent?: any // 大纲
}

export const useEditorCtx = defineStore("editorContext", {
	state: (): WritingContext => ({
		id: undefined,
		editor: null,
		editorKey: "",
		disabled: false,
		dragOverNode: null,
		dragOverFrom: 0,
		title: "",
		thinkContent: "",
		content: "",
		editorScrollElement: null,
		tableContent: null
	}),
	actions: {
		reset() {
			const { editor, editorScrollElement } = this;
			this.$reset();
			// @ts-ignore
			this.$patch({
				// @ts-ignore
				editor,
				editorScrollElement,
			});
			this.setContent("");
		},
		setContent(content: string, thinkContent?: string) {
			content = content?.replace(/^```markdown/, '').replace(/```$/, '')
			this.editor?.commands.setContent(content);
			this.content = content;
			if (thinkContent) this.setThinkContent(thinkContent);
		},
		async setThinkContent(content: string) {
			const _content = await marked.parse(content, {breaks: true});
			this.$patch({
				thinkContent: _content,
			});
		},
		// 外部改写
		async saveDocument(){},
		/**
		 * TODO
		 */
		async exportWord() {
			// @link https://www.npmjs.com/package/@turbodocx/html-to-docx
			// const html = this.editor?.getHTML();
			const htmlString = `<!DOCTYPE html>
    <html lang="en">
        <head>
            <meta charset="UTF-8" />
            <title>Document</title>
        </head>
        <body>
            <h1>Hello world</h1>
        </body>
    </html>`;
			// const docx = await HtmlToDocx(htmlString);

			// saveAs(docxBlob, this.title+".docx");
		},
	},
});
