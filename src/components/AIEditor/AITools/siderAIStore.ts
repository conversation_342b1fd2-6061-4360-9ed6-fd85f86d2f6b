import { postRiskControl } from '@/utils/functions'
import { defineStore } from 'pinia'
import { post } from '@/utils/request'
import { useEditorCtx } from '../editorCtx'
import { AIType } from './types'

interface State {
  visible: boolean
  type?: AIType
  loading: boolean // 是否加载中
  content: string // 内容
  status: number // 状态 0:未开始 1:进行中 2:完成
  controller: AbortController | null // 取消请求
  // 上一次的参数
  lastParams?: {
    prompt?: string
    type: AIType
    id?: number
    style?: string
  }
}

export const useSiderAIStore = defineStore('siderAIStore', {
  state: (): State => ({
    visible: false,
    type: undefined,
    loading: false,
    content: '',
    status: 0,
    controller: null,
    lastParams: undefined,
  }),
  actions: {
    // 打开浮窗
    open() {
      this.close()
      this.visible = true
    },
    // 关闭浮窗
    close() {
      this.stop()
      this.$reset()
    },
    // 停止生成
    stop() {
      if (this.controller) this.controller.abort?.()
      this.controller = null
    },
    /**
     * @param {} prompt
     * @param {*} type 工具类型
     * @param {} id  文档ID
     * @param {*} style 附加风格
     * @returns
     */
    async generate({ prompt, type, id, style }: { prompt?: string; type: AIType; id?: number; style?: string }) {
      if (this.loading) return
      if (this.controller) {
        this.controller.abort?.()
        this.controller = null
      }

      await useEditorCtx().saveDocument()

      try {
        // 检查频控
        const captchaData = await postRiskControl('apps')

        this.loading = true
        this.status = 1
        this.type = type

        const prams = {
          prompt,
          ...captchaData,
          type,
          id,
          style,
        }
        this.lastParams = {
          prompt,
          type,
          id,
          style,
        }

        const res = await this.fetchGenerate(prams)
        this.loading = false
        this.status = 2
        this.controller = null
      } catch (error: any) {
        this.loading = false
        this.status = 3
        this.controller = null
        if (error.message) this.content = error.message
        return
      }
    },

    regenerate() {
      this.generate(this.lastParams!)
    },

    async fetchGenerate(params: any) {
      try {
        this.controller = new AbortController()
        const result = await new Promise((resolve, reject) => {
          post({
            url: '/api3/aiwork/core/chat-tools',
            data: params,
            signal: this.controller!.signal,
            onDownloadProgress: ({ event }) => {
              const xhr = event.target
              const { responseText } = xhr

              try {
                const chunkArr = responseText.split('\n') as any[]
                const content = chunkArr
                  .map((item) => JSON.parse(item) || {})
                  .map((item) => item.text)
                  .join('')
                this.$patch({
                  content,
                })
                const lastItem = JSON.parse(chunkArr.at(-1))
                const isCompute = lastItem?.detail?.choices?.[0]?.finish_reason
                if (isCompute === 'stop') {
                  resolve(true)
                }
              } catch (error) {
                console.error(error, responseText)
                // reject(error)
              }
            },
          }).catch((err) => {
            // throw err.message
            reject(err)
          })
        })
        return result
      } catch (error) {
        throw error
      }
    },
  },
})
