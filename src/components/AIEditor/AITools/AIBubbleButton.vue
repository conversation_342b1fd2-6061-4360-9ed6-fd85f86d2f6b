<template>
	<NButton quaternary size="medium" style="--n-font-weight: 600;" @click="handleClick">
		<template #icon>
			<AISvg class="w-full h-full text-primary" />
		</template>
		AI写作助手
	</NButton>
</template>

<script setup lang="ts">
import { NButton } from 'naive-ui';

import AISvg from "../icons/ai.svg"
import { storeToRefs } from 'pinia';
import { useEditorCtx } from '../editorCtx';
import { useFloatAIStore } from './floatAIStore';

const floatAIStore = useFloatAIStore()
const { visible } = storeToRefs(floatAIStore)
const editorCtx = useEditorCtx()

const emit = defineEmits<{
	click: []
}>()

const handleClick = () => {
	emit('click')
	// 获取选中文本
	const { from, to } = editorCtx.editor!.view.state.selection;
	const selectedText = editorCtx.editor!.view.state.doc.textBetween(from, to, ' ');

	// 调用 AIChat 组件的 show 方法
	const rect = getBlockRect()!
	floatAIStore.open(selectedText, {
		x: rect.x,
		y: rect.y + 12,
	})
}

const getBlockRect = () => {
	// 1. 获取当前选中文本的范围
	const selection = editorCtx.editor!.view.state.selection;
	if (!selection) return { x: 0, y: 0, width: 0, height: 0 };

	let startPos = 0
	if (!selection || selection.empty) { // 没有选中文本, 取光标所在位置
		startPos = selection.from
	}
	else { // 有选中文本
		// 2. 获取选中文本的起始位置
		startPos = selection.from + 1; // 需要+1, 不然获取到的是整个文档的
	}
	const blockNodePos = getBlockNode(editorCtx.editor!.$pos(startPos))
	const blockElement = blockNodePos.element.nodeName == '#text' ? blockNodePos.element.parentElement : blockNodePos.element


	const rect = blockElement.getBoundingClientRect();
	const ancestorRect = editorCtx.editorScrollElement!.querySelector('.ai-editor-content')!.getBoundingClientRect()
	return {
		x: rect.left - ancestorRect.left,
		y: rect.top - ancestorRect.top + rect.height, // 使用底部坐标以便元素定位在块下方
		width: rect.width,
		height: rect.height
	};
}

const getBlockNode = (nodePos) => {
	if (nodePos.depth > 1) return getBlockNode(nodePos.parent)
	return nodePos
}

</script>
