<template>
	<ToolLayout>
		<template #header>{{ getTypeTitle() }}</template>
		<div class="flex flex-col flex-1 h-full min-h-0">
			<div
				class=" bg-white flex flex-col w-full bg-card border border-border shadow-sm rounded-[8px] px-5 py-4 z-50 min-w-[200px] max-w-[600px]  shadow-border min-h-0 max-h-full"
				@click.stop>
				<div class="w-full flex-1 flex space-x-1 items-start overflow-y-auto" ref="$content">
					<div class="text-base px-2 h-full max-h-full">
						<span v-if="!siderAIStore.content">生成中...</span>
						<div v-if="siderAIStore.content" class="ai-side-container" v-html="contentHtml"></div>
					</div>
				</div>
				<div v-if="status == 1" class="flex justify-end gap-2 mt-2 px-2 pb-2">
					<NButton strong secondary type="tertiary" @click="handleCancel">
						停止生成
					</NButton>
				</div>

				<div v-if="status == 2 || status == 3" class="flex gap-2 px-2 mt-2">
					<NButton type="info" strong @click="replaceSelectedText(siderAIStore.content || '')">
						<template #icon>
							<Check />
						</template>
						替换原文
					</NButton>
					<NButton strong secondary type="tertiary" @click="insertBelowSelectedText(siderAIStore.content || '')">
						<template #icon>
							<CornerDownLeft />
						</template>
						插入到下方
					</NButton>
					<NTooltip>
						<template #trigger>
							<NButton quaternary type="tertiary" @click="handleCopyText">
								<template #icon>
									<Copy />
								</template>
							</NButton>
						</template>
						复制文本
					</NTooltip>
					<NTooltip>
						<template #trigger>
							<NButton quaternary type="tertiary" @click="handleCopyMarkdown">
								<template #icon>
									<DownloadMdSvg />
								</template>
							</NButton>
						</template>
						复制为Markdown
					</NTooltip>
					<NTooltip>
						<template #trigger>
							<NButton quaternary type="tertiary" @click="siderAIStore.regenerate()">
								<template #icon>
									<Redo />
								</template>
							</NButton>
						</template>
						重新生成
					</NTooltip>
					<!-- <NTooltip>
					<template #trigger>
						<NButton quaternary type="tertiary" @click="aigcStore.reset()">
							<template #icon>
								<DeleteFour />
							</template>
						</NButton>
					</template>
					清空
				</NTooltip> -->

				</div>
			</div>
			<div class="flex justify-center items-center px-2 mt-2 text-xs text-neutral-400">
				<Info class="mr-1" />AI生成内容不代表本站立场, 请遵守相关法律法规
			</div>
		</div>
	</ToolLayout>
</template>

<script setup lang="ts">
import { onMounted, ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { Check, Copy, CornerDownLeft, DeleteFour, Info, Redo } from '@icon-park/vue-next';
import { NButton, NTooltip } from 'naive-ui';
import { DownloadMdSvg } from '../../icons';
import { marked } from 'marked';
import { copyToClipboard } from '@/utils/clipboard';
import ToolLayout from './ToolLayout.vue';
import { useEditorCtx } from '../../editorCtx';
import { useRoute } from 'vue-router';
import { useSiderAIStore } from '../siderAIStore';
import { AITypeText } from '../types';

const siderAIStore = useSiderAIStore()
const { content, status } = storeToRefs(siderAIStore)
const editorCtx = useEditorCtx()
const route = useRoute()
const $content = ref<HTMLElement>()
const contentHtml = ref('')
const uuid = ref<number>(0)

const getTypeTitle = () => {
	return AITypeText[siderAIStore.type!]
}

const handleCancel = async () => {
	await siderAIStore.stop()
	siderAIStore.status = 2
}


watch(() => content.value, async (value) => {
	$content.value!.scrollTop = $content.value!.scrollHeight
	contentHtml.value = await marked.parse(value || '', { breaks: true })
})

const handleCopyMarkdown = () => {
	copyToClipboard(siderAIStore.content || '')
}

const handleCopyText = () => {
	copyToClipboard(siderAIStore.content || '')
}

// 替换全文
const replaceSelectedText = (text: string) => {
	text = text.replace(/^```markdown/, '').replace(/```$/, '')
	editorCtx.editor?.commands.setContent(marked.parse(text, { breaks: true }))
	const to = editorCtx.editor?.$doc.size!
	editorCtx.editor?.commands.setTextSelection({ from: 0, to })
	editorCtx.editor?.commands.focus()
	editorCtx.editor?.commands.scrollIntoView()
}

// 文后插入
const insertBelowSelectedText = (text: string) => {
	text = text.replace(/^```markdown/, '').replace(/```$/, '')
	const from = editorCtx.editor?.$doc.to!
	editorCtx.editor?.commands.insertContentAt(from - 1, marked.parse(text, { breaks: true }))
	const to = editorCtx.editor?.$doc.to!
	editorCtx.editor?.commands.setTextSelection({ from, to })
	editorCtx.editor?.commands.focus()
	editorCtx.editor?.commands.scrollIntoView()
}

</script>

<style>
.ai-side-container p {
	margin-top: 16px;
	margin-bottom: 16px;
}
</style>
