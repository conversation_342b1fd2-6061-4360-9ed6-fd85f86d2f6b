<template>
	<div class="w-[480px] relative border border-r-0 border-neutral-200/70 bg-white rounded-l-lg px-5 pb-5 flex flex-col h-full min-h-0">
		<div class="flex justify-between items-center">
			<div class="flex text-[18px] font-semibold py-7 justify-between">
				<slot name="header"></slot>
			</div>
			<NButton quaternary class="" @click="closeSubSider">
				<template #icon>
					<Close />
				</template>
			</NButton>
		</div>
		<div class="flex-1 h-full min-h-0">
			<slot></slot>
		</div>
	</div>
</template>

<script setup lang="ts">
import { Close } from '@icon-park/vue-next';
import { NButton } from 'naive-ui';
import { inject } from 'vue';

const closeSubSider = inject('closeSubSider') as () => void;
</script>
