<script setup lang="ts">
import { <PERSON><PERSON>pen, ClearFormat, Copy, Export, FourArrows, Right, SendOne, Translate } from '@icon-park/vue-next';
import { AiSvg, GaixieBaoshouSvg, GaixieGuwenSvg, GaixieNormalSvg, GaixieQiangliSvg, GaixieSvg, JianxieSvg, KuoxieSvg, XuxieSvg, ZongjieSvg } from '../../icons';
import { NPopover, useMessage } from 'naive-ui';
import Generate from './Generate.vue';
import { provide, reactive, ref } from 'vue';
import { useEditorCtx } from '../../editorCtx';
import { copyToClipboard } from '@/utils/clipboard';
import { storeToRefs } from 'pinia';
import { unified } from 'unified';
import markdown from "remark-parse";
import docx from "remark-docx";
import { saveAs } from "file-saver";
import { useSiderAIStore } from '../siderAIStore';
import { AIType } from '../types';

const message = useMessage()
const editorCtx = useEditorCtx()
const siderAIStore = useSiderAIStore()
const generateRef = ref<InstanceType<typeof Generate>>()

provide('closeSubSider', closeSubSider)

async function closeSubSider() {
	siderAIStore.stop()
	siderAIStore.$reset()
}

const copyAs = (type) => {
	let content = ''
	switch (type) {
		case "txt":
			content = editorCtx.editor?.getText()!
			break;
		case "markdown":
			// @ts-ignore
			content = editorCtx.editor?.storage.markdown.getMarkdown()
			break;
		case "html":
			content = editorCtx.editor?.getHTML()!
			break;
	}
	copyToClipboard(content)
	message.success('复制成功')
}

const openSubSider = async (type: AIType, style = '') => {
	// 等待 aigcStore.open() 完成中断和重置
	if (editorCtx.disabled) return
	if(type == AIType.titleOptimiza) {
		if (!editorCtx.title) return message.warning('请先在编辑器中输入H格式的标题')
	} else if(editorCtx.editor?.isEmpty) return message.warning('请先输入内容')

	siderAIStore.close()
	siderAIStore.open()
	siderAIStore.generate({
		type,
		prompt: type == AIType.titleOptimiza ? editorCtx.title : '',
		id: editorCtx.id,
		style,
	})
}

const todo = () => {
	message.info('TODO')
}


const handleExport = (type) => {
	switch (type) {
		case 'word':
			// editorCtx.exportWord()
			handleExportDocx()
			break
		case 'markdown':
			handleExportMarkdown()
			break
		case 'txt':
			handleExportTxt()
			break
	}
}

const handleExportDocx = async () => {
	if (!editorCtx.editor?.isEmpty) {
		// richtextRef.value.handlerExportDocx()
		const processor = unified().use(markdown).use(docx, { output: "blob" });
		// @ts-ignore
		const doc = await processor.process(editorCtx.editor?.storage.markdown.getMarkdown());
		const blob = await doc.result;
		saveAs(blob, "content.docx");
		message.success('导出成功')
	} else {
		message.error('请先输入内容')
	}
}

const handleExportMarkdown = () => {
	if (!editorCtx.editor?.isEmpty) {
		// @ts-ignore
		const markdownContent = editorCtx.editor?.storage.markdown.getMarkdown()
		const blob = new Blob([markdownContent], { type: "text/plain;charset=utf-8" });
		saveAs(blob, "content.md");
		message.success('导出成功')
	} else {
		message.error('请先输入内容')
	}
}

const handleExportTxt = () => {
	if (!editorCtx.editor?.isEmpty) {
		const txtContent = editorCtx.editor?.getText()!
		const blob = new Blob([txtContent], { type: "text/plain;charset=utf-8" });
		saveAs(blob, "content.txt");
		message.success('导出成功')
	} else {
		message.error('请先输入内容')
	}
}
</script>

<template>
	<div class="flex h-full items-stretch" @click.stop>
		<div class="editor-sider_centent h-full">
			<template v-if="siderAIStore.visible">
				<Generate ref="generateRef" v-if="siderAIStore.visible" :key="siderAIStore.type" />
			</template>
		</div>
		<div
			class="editor-sider_side h-full border-l border-solid border-neutral-200/70 flex flex-col space-y-1.5 overflow-y-auto py-4 px-4">
			<!-- <div class="text-xs text-neutral-500 px-2.5">AI特色工具</div> -->
			<div class="flex flex-col space-y-1.5" :class="editorCtx.disabled ? 'ations-disabled' : ''">
				<div
					class="w-full py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-col justify-center items-center space-y-1.5 text-xs hover:text-primary hover:bg-card"
					@click="openSubSider(AIType.fullExpansion)">
					<KuoxieSvg class="w-5 h-5" />
					<span>全文扩写</span>
				</div>
				<div
					class="w-full py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-col justify-center items-center space-y-1.5 text-xs hover:text-primary hover:bg-card "
					@click="openSubSider(AIType.fullSummary)">
					<JianxieSvg class="w-5 h-5" />
					<span>全文简写</span>
				</div>
				<div
					class="w-full py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-col justify-center items-center space-y-1.5 text-xs hover:text-primary hover:bg-card "
					@click="openSubSider(AIType.fullContinuation)">
					<XuxieSvg class="w-5 h-5" />
					<span>全文续写</span>
				</div>
				<NPopover trigger="hover" placement="left-start" :show-arrow="false" style="--n-padding: 8px 0;">
					<template #trigger>
						<div
							class="w-full py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-col justify-center items-center space-y-1.5 text-xs hover:text-primary hover:bg-card ">
							<GaixieSvg class="w-5 h-5" />
							<span>全文改写</span>
						</div>
					</template>
					<div class="flex flex-col space-y-1.5 px-3">
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card "
							@click="openSubSider(AIType.fullRewritten)">
							<GaixieNormalSvg class="w-4 h-4" />
							<span>普通改写</span>
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card "
							@click="openSubSider(AIType.fullStrongRewritten)">
							<GaixieQiangliSvg class="w-4 h-4" />
							<span>强力改写</span>
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card "
							@click="openSubSider(AIType.fullGuardRewritten)">
							<GaixieBaoshouSvg class="w-4 h-4" />
							<span>保守改写</span>
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card "
							@click="openSubSider(AIType.fullAncientRewritten)">
							<GaixieGuwenSvg class="w-4 h-4" />
							<span>古文改写</span>
						</div>
					</div>
				</NPopover>
				<div
					class="w-full py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-col justify-center items-center space-y-1.5 text-xs hover:text-primary hover:bg-card " @click="openSubSider(AIType.fullSummarize)">
					<ZongjieSvg class="w-5 h-5" />
					<span>全文总结</span>
				</div>
				<NPopover trigger="click" placement="left-start" :show-arrow="false" style="--n-padding: 8px 0;">
					<template #trigger>
						<div
							class="w-full py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-col justify-center items-center space-y-1.5 text-xs hover:text-primary hover:bg-card ">
							<Translate class="w-5 h-5" size="20" />
							<span>全文翻译</span>
						</div>
					</template>
					<div class="flex flex-col space-y-1.5 px-3">
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card " @click="openSubSider(AIType.fullTranslation, '中文')">
							<GaixieNormalSvg class="w-4 h-4" />
							<span>中文</span>
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card " @click="openSubSider(AIType.fullTranslation, '英文')">
							<GaixieNormalSvg class="w-4 h-4" />
							<span>英文</span>
						</div>
					</div>
				</NPopover>

				<div
					class="w-full py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-col justify-center items-center space-y-1.5 text-xs hover:text-primary hover:bg-card " @click="openSubSider(AIType.titleOptimiza)">
					<SendOne class="w-5 h-5" size="20" />
					<span>标题优化</span>
				</div>
				<div
					class="w-full py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-col justify-center items-center space-y-1.5 text-xs hover:text-primary hover:bg-card " @click="openSubSider(AIType.fullPolish)">
					<AiSvg class="w-5 h-5" />
					<span>全文润色</span>
				</div>
				<NPopover trigger="hover" placement="left-start" :show-arrow="false" style="--n-padding: 8px 0;">
					<template #trigger>
						<div
							class="w-full py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-col justify-center items-center space-y-1.5 text-xs hover:text-primary hover:bg-card ">
							<GaixieBaoshouSvg class="w-5 h-5" />
							<span>修改语气</span>
						</div>
					</template>
					<div class="flex flex-col space-y-1.5 max-h-[300px] overflow-y-auto px-3 scrollbar">
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card " @click="openSubSider(AIType.modifyTone, '正式')">
							<span>正式</span>
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card " @click="openSubSider(AIType.modifyTone, '通俗')">
							<span>通俗</span>
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card " @click="openSubSider(AIType.modifyTone, '礼貌')">
							<span>礼貌</span>
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card " @click="openSubSider(AIType.modifyTone, '严肃')">
							<span>严肃</span>
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card " @click="openSubSider(AIType.modifyTone, '幽默')">
							<span>幽默</span>
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card " @click="openSubSider(AIType.modifyTone, '细腻')">
							<span>细腻</span>
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card " @click="openSubSider(AIType.modifyTone, '叙述')">
							<span>叙述</span>
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card " @click="openSubSider(AIType.modifyTone, '议论')">
							<span>议论</span>
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card " @click="openSubSider(AIType.modifyTone, '抒情')">
							<span>抒情</span>
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card " @click="openSubSider(AIType.modifyTone, '说明')">
							<span>说明</span>
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card " @click="openSubSider(AIType.modifyTone, '描写')">
							<span>描写</span>
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card " @click="openSubSider(AIType.modifyTone, '对话')">
							<span>对话</span>
						</div>
					</div>
				</NPopover>

				<div
					class="w-full py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-col justify-center items-center space-y-1.5 text-xs hover:text-primary hover:bg-card " @click="openSubSider(AIType.fullAigc)">
					<FourArrows class="w-5 h-5" size="20" />
					<span>AI降痕</span>
				</div>
				<div
					class="w-full py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-col justify-center items-center space-y-1.5 text-xs hover:text-primary hover:bg-card " @click="openSubSider(AIType.fullAnalysis)">
					<BookOpen class="w-5 h-5" size="20" />
					<span>全文分析</span>
				</div>
			</div>

			<!-- <div class="text-xs text-neutral-500 px-2.5">复制导出</div> -->
			<div class="flex flex-col space-y-1.5" :class="editorCtx.disabled ? 'ations-disabled' : ''">
				<NPopover trigger="hover" placement="left-start" :show-arrow="false" style="--n-padding: 8px 0;"
					:disabled="editorCtx.disabled">
					<template #trigger>
						<div
							class="w-full py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-col justify-center items-center space-y-1.5 text-xs hover:text-primary hover:bg-card ">
							<Copy class="w-5 h-5" size="20" />
							<span>复制全文</span>
							<!-- <Right /> -->
						</div>
					</template>
					<div class="flex flex-col space-y-1.5 px-3">
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card "
							@click="copyAs('txt')">
							<!-- <GaixieNormalSvg class="w-4 h-4" /> -->
							<span>文本</span>
							<!-- <Right /> -->
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card "
							@click="copyAs('markdown')">
							<!-- <GaixieQiangliSvg class="w-4 h-4" /> -->
							<span>Markdown</span>
							<!-- <Right /> -->
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card "
							@click="copyAs('html')">
							<!-- <GaixieQiangliSvg class="w-4 h-4" /> -->
							<span>HTML</span>
							<!-- <Right /> -->
						</div>
					</div>
				</NPopover>
				<NPopover trigger="hover" placement="left-start" :show-arrow="false" style="--n-padding: 8px 0;"
					:disabled="editorCtx.disabled">
					<template #trigger>
						<div
							class="w-full py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-col justify-center items-center space-y-1.5 text-xs hover:text-primary hover:bg-card ">
							<Export class="w-5 h-5" size="20" />
							<span>导出全文</span>
							<!-- <Right /> -->
						</div>
					</template>
					<div class="flex flex-col space-y-1.5 px-3">
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card "
							@click="handleExport('txt')">
							<!-- <GaixieNormalSvg class="w-4 h-4" /> -->
							<span>Txt</span>
							<!-- <Right /> -->
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card "
							@click="handleExport('markdown')">
							<!-- <GaixieQiangliSvg class="w-4 h-4" /> -->
							<span>Markdown</span>
							<!-- <Right /> -->
						</div>
						<div
							class="w-full h-9 py-2.5 rounded-[8px] cursor-pointer shrink-0 flex flex-row items-center space-x-1.5 text-sm hover:text-primary hover:bg-card "
							@click="handleExport('word')">
							<!-- <GaixieQiangliSvg class="w-4 h-4" /> -->
							<span>Word</span>
							<!-- <Right /> -->
						</div>
					</div>
				</NPopover>
			</div>
		</div>
	</div>
</template>

<style scoped>
.ations-disabled>div {
	cursor: not-allowed;
	color: gray !important;
}
</style>
