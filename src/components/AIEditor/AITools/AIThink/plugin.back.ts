import { Node, mergeAttributes } from '@tiptap/core'
import { VueNodeViewRenderer } from '@tiptap/vue-3'
import AIThink from './AIThink.back.vue'

declare module '@tiptap/core' {
  interface Commands<ReturnType> {
		aiThink: {
			setThinkContent: (thinkContent: string) => any
		}
  }
}

export default Node.create({
  name: 'aiThink',

  group: 'block',

  content: 'inline*', // 允许内联内容

  parseHTML() {
    return [
      {
        tag: 'ai-think',
      },
    ]
  },

  renderHTML({ HTMLAttributes }) {
    return ['ai-think', mergeAttributes(HTMLAttributes, { class: 'ai-think-container' }), 0]
  },

  addNodeView() {
    return VueNodeViewRenderer(AIThink)
  },

  addCommands() {
    return {
      setThinkContent: (content) => ({ chain, commands }) => {
        // console.log('Setting think content:', content);

        // 确保内容被正确插入
        // return chain()
        //   .focus()
        //   .insertContent({
        //     type: this.name,
        //     content: [{
        //       type: 'paragraph',
        //       content: [{ type: 'text', text: content }]
        //     }]
        //   })
        //   .run();
				return chain().focus().insertContentAt(0, '<ai-think>' + content + '</ai-think>').run()
      }
    }
  }
})
