<template>
	<node-view-wrapper class="ai-think-container p-3 my-2 rounded-md bg-blue-50 border-l-4 border-blue-400">
		<div class="ai-think-content">
			<div class="ai-think-header flex items-center mb-1">
				<div class="ai-think-icon mr-2">
					<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-500">
						<circle cx="12" cy="12" r="10"></circle>
						<path d="M12 16v-4"></path>
						<path d="M12 8h.01"></path>
					</svg>
				</div>
				<div class="ai-think-title text-sm font-medium text-blue-700">AI 思考</div>
			</div>
			<div class="ai-think-body text-gray-700">
				<node-view-content class="content" />
			</div>
		</div>
	</node-view-wrapper>
</template>

<script setup lang="ts">
import { NodeViewWrapper, NodeViewContent, nodeViewProps } from '@tiptap/vue-3'

defineProps(nodeViewProps)
</script>

<style>
.ai-think-container {
	display: block;
	width: 100%;
}
</style>
