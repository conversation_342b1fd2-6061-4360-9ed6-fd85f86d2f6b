<template>
	<div v-if="thinkContent"
		class="ai-think-container p-3 my-2 rounded-md bg-blue-50 border-l-4 border-blue-400 max-w-[80%] mx-auto"
		@click.stop>
		<!-- :class="{ 'sticky top-0 z-10': open }"  -->
		<div class="ai-think-content">
			<div class="ai-think-header flex items-center mb-1">
				<div class="ai-think-icon mr-2">
					<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none"
						stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-500">
						<circle cx="12" cy="12" r="10"></circle>
						<path d="M12 16v-4"></path>
						<path d="M12 8h.01"></path>
					</svg>
				</div>
				<div class="ai-think-title text-sm font-medium text-blue-700 flex-1 cursor-pointer" @click="open = !open">AI
					深度思考{{ content ?
					'' : '中...' }}</div>
				<div>
					<div
						class="w-6 h-6 text-neutral-700 cursor-pointer transition-all duration-300 flex justify-center items-center"
						:class="{ ' rotate-180': !!open }" @click="open = !open">
						<Down />
					</div>
				</div>
			</div>
			<div class="ai-think-body text-neutral-500 text-sm auto-height" :class="{ 'active': open }">
				<div class="content" v-html="thinkContent"></div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia';
import { useEditorCtx } from '../../editorCtx';
import { ref, watch } from 'vue';
import { Down } from '@icon-park/vue-next';

const { thinkContent, content } = storeToRefs(useEditorCtx())
const open = ref(true)

watch([
	() => content!.value,
	() => thinkContent!.value
], ([contentValue, thinkValue]) => {
	if (thinkValue && !contentValue) open.value = true
	if (thinkValue && contentValue) open.value = false
})

</script>
