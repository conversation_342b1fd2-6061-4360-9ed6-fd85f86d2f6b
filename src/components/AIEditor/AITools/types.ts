/**
 * @link http://showdoc.weimob.com/index.php?s=/560&page_id=36306
 */
export enum AIType {
	fullExpansion = 'fullExpansion', // 全文扩写
	fullSummary = 'fullSummary', // 全文简写
	fullContinuation = 'fullContinuation', // 全文续写
	fullRewritten = 'fullRewritten', // 全文普通改写
	fullStrongRewritten = 'fullStrongRewritten', // 全文强力改写
	fullGuardRewritten = 'fullGuardRewritten', // 全文保守改写
	fullAncientRewritten = 'fullAncientRewritten', // 全文古文改写
	fullSummarize = 'fullSummarize', // 全文总结
	fullTranslation = 'fullTranslation', // 全文翻译, 需传入style语种
	fullPolish = 'fullPolish', // 全文润色
	fullAnalysis = 'fullAnalysis', // 全文分析
	fullAigc = 'fullAigc', // 全文降AIGC
	titleOptimiza = 'titleOptimiza', // 标题优化
	modifyTone = 'modifyTone', // 修改语气, style参数接受语气
	aigc = 'aigc', // AIGC降痕
	expansion = 'expansion', // 局部扩写
	summary = 'summary', // 局部简写
	continuation = 'continuation', // 局部续写
	rewritten = 'rewritten', // 局部改写
	strongRewritten = 'strongRewritten', // 局部强力改写
	guardRewritten = 'guardRewritten', // 局部保守改写
	ancientRewritten = 'ancientRewritten', // 局部古文改写
	custom = 'custom' // 用户自定义
}

export const AITypeText = {
	[AIType.fullExpansion]: '全文扩写',
	[AIType.fullSummary]: '全文简写',
	[AIType.fullContinuation]: '全文续写',
	[AIType.fullRewritten]: '全文普通改写',
	[AIType.fullStrongRewritten]: '全文强力改写',
	[AIType.fullGuardRewritten]: '全文保守改写',
	[AIType.fullAncientRewritten]: '全文古文改写',
	[AIType.fullSummarize]: '全文总结',
	[AIType.fullTranslation]: '全文翻译',
	[AIType.fullPolish]: '全文润色',
	[AIType.fullAnalysis]: '全文分析',
	[AIType.fullAigc]: '全文降AIGC',
	[AIType.titleOptimiza]: '标题优化',
	[AIType.modifyTone]: '修改语气',
	[AIType.aigc]: 'AIGC降痕',
	[AIType.expansion]: '局部扩写',
	[AIType.summary]: '局部简写',
	[AIType.continuation]: '局部续写',
	[AIType.rewritten]: '局部改写',
	[AIType.strongRewritten]: '局部强力改写',
	[AIType.guardRewritten]: '局部保守改写',
	[AIType.ancientRewritten]: '局部古文改写',
	[AIType.custom]: '用户自定义',
} as const
