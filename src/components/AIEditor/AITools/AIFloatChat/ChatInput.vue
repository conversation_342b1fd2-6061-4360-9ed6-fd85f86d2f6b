<template>
  <div
    class=" bg-white flex flex-col w-full bg-card border border-border shadow-sm rounded-[8px] px-5 pb-2.5 pt-4 z-50 min-w-[200px] max-w-[600px] shadow-dropdown"
    @click.stop>
    <div class="w-full flex space-x-1 items-center">
      <div class="w-7 h-7 flex justify-center items-center text-primary">
        <AISvg class="w-5 h-5" />
      </div>
      <NInput type="textarea" :autosize="{ minRows: 1, maxRows: 3 }" v-model:value="value"
        class="flex rounded-[6px] ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 resize-none bg-card border-0 focus-visible:ring-0 max-w-full w-full focus-visible:ring-offset-0 p-0 !min-h-5 max-h-[74px] text-[16px] translate-y-[0.125em]"
        placeholder="让AI写作助手帮我" :maxLength="3000" style="--n-border: none;--n-border-focus:none;" @keyup.enter="(e) => e.preventDefault()" />
    </div>
    <div class="flex justify-between items-center">
      <div class="text-xs text-neutral-500">{{value?.length || 0}}/3000</div>
      <div class="flex space-x-2">
        <NButton quaternary @click="emit('send')">
          <template #icon>
            <Telegram theme="outline" class="text-primary" />
          </template>
        </NButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Telegram } from '@icon-park/vue-next';
import { NButton, NInput, useMessage } from 'naive-ui'
import AISvg from '../../icons/ai.svg'

const emit = defineEmits<{
	send: []
}>()

const value = defineModel<string>('value')
</script>

<style scoped>
:deep(.n-input__state-border) {
	box-shadow: none !important;
}
</style>
