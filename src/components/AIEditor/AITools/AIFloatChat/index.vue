<template>
	<NModal v-if="visible" :show="visible"
		:to="editorCtx?.editorScrollElement!.querySelector('.ai-editor-content') as HTMLElement" :auto-focus="false"
		:block-scroll="false" style="--n-box-shadow:none;" @mask-click="floatAIStore.close()">
		<div class="editor_ai_tool_modal !absolute w-full"
			:style="`left:${floatPos?.x}px; top: ${floatPos?.y}px;`" @click="floatAIStore.close()">
			<!-- 待生成 -->
			<div v-show="status == 0">
				<ChatInput v-model:value="inputValue" @send="handleSend" @keyup.enter="handleSend" />
				<!-- sub -->
				<SubTools v-if="subVisible" />
			</div>
			<div v-show="status != 0">
				<ChatGenerate />
			</div>
		</div>
	</NModal>
</template>

<script setup lang="ts">
import { nextTick, onMounted, ref, watch } from 'vue'
import { NModal } from 'naive-ui';
import ChatInput from './ChatInput.vue';
import SubTools from './SubTools.vue'
import ChatGenerate from './ChatGenerate.vue';
import { storeToRefs } from 'pinia';
import { useEditorCtx } from '../../editorCtx';
import { useFloatAIStore } from '../floatAIStore';

const inputValue = ref('')
const subVisible = ref(true)

// const aigcStore = useAigcStore()
// const { status, visible, props: storeProps } = storeToRefs(aigcStore)
const floatAIStore = useFloatAIStore()
const { status, visible, pos: floatPos } = storeToRefs(floatAIStore)
const editorCtx = useEditorCtx()

watch(() => inputValue.value, (newValue) => {
	subVisible.value = !newValue.length
})

watch(() => visible.value, (newValue) => {
	if (visible.value) {
		editorCtx.editorScrollElement?.querySelector('.ai-editor-content')?.classList.add('!pb-[300px]')
		inputValue.value = ''
	}
	else editorCtx.editorScrollElement?.querySelector('.ai-editor-content')?.classList.remove('!pb-[300px]')
})

const handleSend = () => {
	nextTick(() => {
		floatAIStore.generate({
			prompt: `${floatAIStore.selectionText!}:\n${inputValue.value}`,
			type: undefined,
			botId: undefined
		})
	})
}

</script>

<style lang="less">
div:has(>.editor_ai_tool_modal) .n-modal-mask {
	opacity: 0 !important;
	// display: none !important;
}

.ai-editor-container .n-modal-container,
.ai-editor-container .n-modal-body-wrapper {
	position: absolute !important;
	width: 100% !important;
	height: 100% !important;
}

.ai-editor-container .n-modal-mask {
	// display: none !important;
	position: absolute;
}
</style>
