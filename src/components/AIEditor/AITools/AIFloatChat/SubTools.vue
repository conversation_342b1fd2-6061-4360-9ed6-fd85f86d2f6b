<template>
  <div
    class="w-[180px] rounded-[10px] bg-popover border border-border shadow-sm flex flex-col p-2 bg-white mt-3 shadow-dropdown block-full-button"
    @click.stop>
    <NButton block quaternary strong @click="handleClick(AIType.rewritten)">
      <template #icon>
        <KuoxieSvg class="w-full h-full" />
      </template>
      扩写
    </NButton>
    <NButton block quaternary strong @click="handleClick(AIType.summary)">
      <template #icon>
        <JianxieSvg class="w-full h-full" />
      </template>
      简写
    </NButton>
    <NButton block quaternary strong @click="handleClick(AIType.continuation)">
      <template #icon>
        <XuxieSvg class="w-full h-full" />
      </template>
      续写
    </NButton>
    <NPopover trigger="click" placement="right-start" :show-arrow="false"
      style="transform: translateX(16px);--n-padding:8px;">
      <template #trigger>
        <NButton quaternary block strong>
          <template #icon>
            <GaixieSvg class="w-full h-full" />
          </template>
          <div class="flex justify-between items-center w-full">
            <span>改写</span>
            <Right />
          </div>
        </NButton>
      </template>
      <div>
        <NButton quaternary block @click="handleClick(AIType.rewritten)">
          <template #icon>
            <GaixieNormalSvg class="w-full h-full" />
          </template>
          <span>普通改写</span>
        </NButton>
        <NButton quaternary block @click="handleClick(AIType.strongRewritten)">
          <template #icon>
            <GaixieQiangliSvg class="w-full h-full" />
          </template>
          <span>强力改写</span>
        </NButton>
        <NButton quaternary block @click="handleClick(AIType.guardRewritten)">
          <template #icon>
            <GaixieBaoshouSvg class="w-full h-full" />
          </template>
          <span>保守改写</span>
        </NButton>
        <NButton quaternary block @click="handleClick(AIType.ancientRewritten)">
          <template #icon>
            <GaixieGuwenSvg class="w-full h-full" />
          </template>
          <span>古文改写</span>
        </NButton>
      </div>
    </NPopover>
    <!-- <NButton block quaternary strong @click="todo">
      <template #icon>
        <ZongjieSvg class="w-full h-full" />
      </template>
      总结
    </NButton>
    <NButton block quaternary strong @click="todo">
      <template #icon>
        <Translate theme="outline" />
      </template>
      翻译
    </NButton>
    <NButton block quaternary strong @click="handleClick(ModeEnum.NORMAL)">
      <template #icon>
        <DocDetail theme="outline" />
      </template>
      润色
    </NButton> -->
    <NPopover trigger="click" placement="right-start" :show-arrow="false"
      style="transform: translateX(16px);--n-padding:8px;">
      <template #trigger>
        <NButton quaternary block strong>
          <template #icon>
            <Abnormal theme="outline" />
          </template>
          <div class="flex justify-between items-center w-full">
            <span>修改语气</span>
            <Right />
          </div>
        </NButton>
      </template>
      <div>
        <NButton quaternary block @click="handleClick(AIType.modifyTone, '正式')">
          <template #icon>
            <AlignTextLeft />
          </template>
          <span>正式</span>
        </NButton>
        <NButton quaternary block @click="handleClick(AIType.modifyTone, '通俗')">
          <template #icon>
            <AlignTextCenter />
          </template>
          <span>通俗</span>
        </NButton>
        <NButton quaternary block @click="handleClick(AIType.modifyTone, '礼貌')">
          <template #icon>
            <AlignTextRight />
          </template>
          <span>礼貌</span>
        </NButton>
        <NButton quaternary block @click="handleClick(AIType.modifyTone, '严肃')">
          <template #icon>
            <AlignTextRight />
          </template>
          <span>严肃</span>
        </NButton>
        <NButton quaternary block @click="handleClick(AIType.modifyTone, '幽默')">
          <template #icon>
            <AlignTextRight />
          </template>
          <span>幽默</span>
        </NButton>
        <NButton quaternary block @click="handleClick(AIType.modifyTone, '简洁')">
          <template #icon>
            <AlignTextRight />
          </template>
          <span>简洁</span>
        </NButton>
        <NButton quaternary block @click="handleClick(AIType.modifyTone, '细腻')">
          <template #icon>
            <AlignTextRight />
          </template>
          <span>细腻</span>
        </NButton>
        <NButton quaternary block @click="handleClick(AIType.modifyTone, '叙述')">
          <template #icon>
            <AlignTextRight />
          </template>
          <span>叙述</span>
        </NButton>
        <NButton quaternary block @click="handleClick(AIType.modifyTone, '议论')">
          <template #icon>
            <AlignTextRight />
          </template>
          <span>议论</span>
        </NButton>
        <NButton quaternary block @click="handleClick(AIType.modifyTone, '抒情')">
          <template #icon>
            <AlignTextRight />
          </template>
          <span>抒情</span>
        </NButton>
        <NButton quaternary block @click="handleClick(AIType.modifyTone, '说明')">
          <template #icon>
            <AlignTextRight />
          </template>
          <span>说明</span>
        </NButton>
        <NButton quaternary block @click="handleClick(AIType.modifyTone, '描写')">
          <template #icon>
            <AlignTextRight />
          </template>
          <span>描写</span>
        </NButton>
        <NButton quaternary block @click="handleClick(AIType.modifyTone, '对话')">
          <template #icon>
            <AlignTextRight />
          </template>
          <span>对话</span>
        </NButton>
      </div>
    </NPopover>
    <NButton block quaternary strong @click="handleClick(AIType.ancientRewritten)">
      <template #icon>
        <DocDetail theme="outline" />
      </template>
      降低AI痕迹
    </NButton>
  </div>
</template>

<script setup lang="ts">
import { DocAdd, DocDetail, Abnormal, AlignTextLeft, AlignTextCenter, AlignTextRight, Right, Translate } from '@icon-park/vue-next';
import { NButton, NPopover, useMessage } from 'naive-ui'
import {GaixieBaoshouSvg, GaixieGuwenSvg, GaixieNormalSvg, GaixieQiangliSvg, GaixieSvg, JianxieSvg, KuoxieSvg, XuxieSvg, ZongjieSvg} from "../../icons/index"
import { useRoute } from 'vue-router';
import { useFloatAIStore } from '../floatAIStore';
import { AIType } from '../types';

const floatAIStore = useFloatAIStore()
const route = useRoute()
const message = useMessage()

const handleClick = (type: AIType, style='') => {
  floatAIStore.generate({
		prompt: floatAIStore.selectionText!,
		type,
		style,
		// botId: route.query.appid as string
	})
}

const todo = () => {
  message.info('TODO')
}
</script>
