<template>
	<div
		class=" bg-white flex flex-col w-full bg-card border border-border shadow-sm rounded-[8px] px-5 pb-2.5 pt-4 z-50 min-w-[200px] max-w-[600px] shadow-dropdown"
		@click.stop>
		<div class="w-full flex space-x-1 items-start">
			<div class="text-[16px] px-2 max-h-[200px] overflow-y-auto" ref="$content">
				<span v-if="!content">生成中...</span>
				<div v-if="content" class="ai-float-container" v-html="contentHtml"></div>
			</div>

		</div>
		<div v-if="status == 1" class="flex justify-end gap-2 mt-2 px-2 pb-2">
			<NButton strong secondary type="tertiary" @click="floatAIStore.stop()">
				停止生成
			</NButton>
		</div>
		<div v-if="status == 2" class="flex justify-between items-center px-2 mt-2 text-sm text-neutral-300">
			<span>生成结果</span>
			<span class="flex items-center">
				<Info class="mr-1" />AI生成内容不代表本站立场, 请遵守相关法律法规
			</span>
		</div>
		<div v-if="status == 2 || status == 3" class="flex gap-2 px-2 mt-2">
			<NButton type="info" strong @click="replaceSelectedText(content || '')">
				<template #icon>
					<Check />
				</template>
				替换原文
			</NButton>
			<NButton strong secondary type="tertiary" @click="insertBelowSelectedText(content || '')">
				<template #icon>
					<CornerDownLeft />
				</template>
				插入到下方
			</NButton>
			<NTooltip>
				<template #trigger>
					<NButton quaternary type="tertiary" @click="handleCopyText">
						<template #icon>
							<Copy />
						</template>
					</NButton>
				</template>
				复制文本
			</NTooltip>
			<NTooltip>
				<template #trigger>
					<NButton quaternary type="tertiary" @click="handleCopyMarkdown">
						<template #icon>
							<DownloadMdSvg />
						</template>
					</NButton>
				</template>
				复制为Markdown
			</NTooltip>
			<NTooltip>
				<template #trigger>
					<NButton quaternary type="tertiary" @click="floatAIStore.regenerate()">
						<template #icon>
							<Redo />
						</template>
					</NButton>
				</template>
				重新生成
			</NTooltip>
			<NTooltip>
				<template #trigger>
					<NButton quaternary type="tertiary" @click="floatAIStore.clean()">
						<template #icon>
							<DeleteFour />
						</template>
					</NButton>
				</template>
				清空
			</NTooltip>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';
import { storeToRefs } from 'pinia';
import { Check, Copy, CornerDownLeft, DeleteFour, Info, Redo } from '@icon-park/vue-next';
import { NButton, NTooltip } from 'naive-ui';
import { DownloadMdSvg } from '../../icons';
import { marked } from 'marked';
import { copyToClipboard } from '@/utils/clipboard';
import { useEditorCtx } from '../../editorCtx';
import { useFloatAIStore } from '../floatAIStore';

const floatAIStore = useFloatAIStore()
const { content, status } = storeToRefs(floatAIStore)
const editorCtx = useEditorCtx()
const $content = ref<HTMLElement>()
const contentHtml = ref('')


watch(() => content.value, async (value) => {
	$content.value!.scrollTop = $content.value!.scrollHeight
	contentHtml.value = await marked.parse(value || '', {breaks: true})
})

const handleCopyMarkdown = () => {
  copyToClipboard(floatAIStore.content || '')
}

const handleCopyText = () => {
	copyToClipboard(floatAIStore.content || '')
}

// 替换选中文本
const replaceSelectedText = (text: string) => {
	text = text.replace(/^```markdown/, '').replace(/```$/, '')
	if(floatAIStore.selectionText) {
		editorCtx.editor?.commands.deleteSelection()
		editorCtx.editor!.commands.insertContent(marked.parse(text, {breaks: true}))
		// editorCtx.editor!.commands.replaceSelectedText(text)
	}
	// 在光标处插入文本
	else {
		editorCtx.editor!.commands.insertContent(marked.parse(text, {breaks: true}))
	}
	floatAIStore.close()
}

// 插入选中文本下方
const insertBelowSelectedText = (text: string) => {
	text = text.replace(/^```markdown/, '').replace(/```$/, '')
	if(floatAIStore.selectionText) {
		// @ts-ignore
		const {to} = editorCtx.editor?.state.selection
		editorCtx.editor?.commands.insertContentAt(to, marked.parse(text, {breaks: true}))
		// editorCtx.editor!.commands.insertBelowSelectedText(text)
	}
	// 在光标下一行处插入文本
	else {
		editorCtx.editor!.commands.insertContent(marked.parse(text, {breaks: true}))
	}

	floatAIStore.close()
}

</script>

<style>
.ai-float-container p {
	margin-top: 16px;
	margin-bottom: 16px;
}
</style>
