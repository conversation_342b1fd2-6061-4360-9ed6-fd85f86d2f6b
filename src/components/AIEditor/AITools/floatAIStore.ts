import { fetchChatAPIProcess } from '@/chatgpt'
import { postRiskControl } from '@/utils/functions'
import post from '@/utils/request'
import { defineStore } from 'pinia'
import { AIType } from './types'

interface State {
  visible: boolean
  pos?: { x: number; y: number } // 浮窗显示位置
  selectionText: string // 选中文本
  loading: boolean // 是否加载中
  content: string // 内容
  status: number // 状态 0:未开始 1:进行中 2:完成
  controller: AbortController | null // 取消请求
  // 上一次的参数
  lastParams?: {
    prompt?: string
    type?: AIType
    // botId?: string
		style?: string
  }
}

export const useFloatAIStore = defineStore('floadAIStore', {
  state: (): State => ({
    visible: false,
    pos: undefined,
    selectionText: '',
    loading: false,
    content: '',
    status: 0,
    controller: null,
    lastParams: undefined,
  }),
  actions: {
    // 打开浮窗
    open(selectionText: string, pos: State['pos']) {
      this.close()
      this.visible = true
      this.pos = pos
      this.selectionText = selectionText
    },
    // 关闭浮窗
    close() {
      this.stop()
      this.$reset()
    },
    // 清除生成内容
    clean() {
      this.content = ''
      this.loading = false
      this.status = 0
    },
    // 停止生成
    stop() {
      if (this.controller) this.controller.abort?.()
      this.controller = null
    },
    async generate({ prompt, type, style }: { prompt?: string; type?: AIType; style?: string }) {
      if (this.loading) return
      this.loading = true
      this.status = 1

      try {
        // 检查频控
        const captchaData = await postRiskControl('apps')

        const prams = {
          prompt,
          ...captchaData,
          type,
					style
        }
        this.lastParams = {
          prompt,
          type,
					style
        }

        await this.fetchGenerate(prams)

        this.loading = false
        this.status = 2
        this.controller = null
      } catch (error:any) {
        this.loading = false
        this.status = 3
        this.controller = null
				if(error.message) this.content = error.message
        return
      }
    },

    regenerate() {
      this.generate(this.lastParams!)
    },
    // async fetchGenerate(params: any) {
    //   try {
    //     const result = await new Promise((resolve, reject) => {
    //       fetchChatAPIProcess<any>({
    //         ...params,
    //         onDownloadProgress: ({ event }) => {
    //           const xhr = event.target
    //           const { responseText } = xhr

    //           try {
    //             const chunkArr = responseText.split('\n') as any[]
    //             const content = chunkArr
    //               .map((item) => JSON.parse(item) || {})
    //               .map((item) => item.text)
    //               .join('')
    //             this.$patch({
    //               content,
    //             })
    //             const lastItem = JSON.parse(chunkArr.at(-1))
    //             const isCompute = lastItem?.detail?.choices?.[0]?.finish_reason
    //             if (isCompute === 'stop') {
    //               resolve(true)
    //             }
    //           } catch (error) {
    //             console.error(error, responseText)
    //             reject(error)
    //           }
    //         },
    //       })
    //     })
    //     return result
    //   } catch (error) {
    //     throw error
    //   }
    // },
    async fetchGenerate(params: any) {
      try {
        this.controller = new AbortController()
        const result = await new Promise((resolve, reject) => {
          post({
            url: '/api3/aiwork/core/chat-tools',
            data: params,
            signal: this.controller!.signal,
            onDownloadProgress: ({ event }) => {
              const xhr = event.target
              const { responseText } = xhr

              try {
                const chunkArr = responseText.split('\n') as any[]
                const content = chunkArr
                  .map((item) => JSON.parse(item) || {})
                  .map((item) => item.text)
                  .join('')
                this.$patch({
                  content,
                })
                const lastItem = JSON.parse(chunkArr.at(-1))
                const isCompute = lastItem?.detail?.choices?.[0]?.finish_reason
                if (isCompute === 'stop') {
                  resolve(true)
                }
              } catch (error) {
                console.error(error, responseText)
                // reject(error)
              }
            },
          }).catch((err) => {
            // throw err.message
            reject(err)
          })
        })
        return result
      } catch (error) {
        throw error
      }
    },
  },
})
