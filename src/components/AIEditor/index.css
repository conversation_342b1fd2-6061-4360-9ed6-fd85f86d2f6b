.ProseMirror {
	padding-top: 40px;
	padding-bottom: 40px;
}
.ProseMirror figure[data-type="imageBlock"] {
	margin: 0;
}
.ProseMirror figure[data-type="imageBlock"] img {
	display: block;
	width: 100%;
	border-radius: 0.25rem;
}
.ProseMirror figure[data-type="blockquoteFigure"] {
	margin-top: 1.75rem;
	margin-bottom: 1.75rem;
	--tw-text-opacity: 1;
	color: rgb(0 0 0 / var(--tw-text-opacity));
}
:is(.dark .ProseMirror figure[data-type="blockquoteFigure"]) {
	--tw-text-opacity: 1;
	color: rgb(255 255 255 / var(--tw-text-opacity));
}
.ProseMirror > blockquote blockquote,
.ProseMirror [data-type="blockquoteFigure"] blockquote {
	margin: 0;
}
.ProseMirror > blockquote blockquote > *:first-child,
.ProseMirror [data-type="blockquoteFigure"] blockquote > *:first-child {
	margin-top: 0;
}
.ProseMirror > blockquote blockquote > *:last-child,
.ProseMirror [data-type="blockquoteFigure"] blockquote > *:last-child {
	margin-bottom: 0;
}
.ProseMirror [data-type="columns"] {
	margin-top: 1.75rem;
	margin-bottom: 1.5rem;
	display: grid;
	gap: 1rem;
}
.ProseMirror [data-type="columns"].layout-sidebar-left {
	grid-template-columns: 40fr 60fr;
}
.ProseMirror [data-type="columns"].layout-sidebar-right {
	grid-template-columns: 60fr 40fr;
}
.ProseMirror [data-type="columns"].layout-two-column {
	grid-template-columns: 1fr 1fr;
}
.ProseMirror [data-type="column"] {
	overflow: auto;
}
.ProseMirror code {
	border-radius: calc(var(--radius) - 4px);
	--tw-bg-opacity: 1;
	background-color: rgb(23 23 23 / var(--tw-bg-opacity));
	font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
		Liberation Mono, Courier New, monospace;
	--tw-text-opacity: 1;
	color: rgb(255 255 255 / var(--tw-text-opacity));
	caret-color: #fff;
	--tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1),
		0 4px 6px -4px rgb(0 0 0 / 0.1);
	--tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color),
		0 4px 6px -4px var(--tw-shadow-color);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
		var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.ProseMirror code::-moz-selection {
	background-color: #ffffff4d;
}
.ProseMirror code::selection {
	background-color: #ffffff4d;
}
.ProseMirror pre {
	margin-top: 1.5rem;
	margin-bottom: 1.5rem;
	border-radius: 0.25rem;
	border-width: 1px;
	--tw-border-opacity: 1;
	border-color: rgb(0 0 0 / var(--tw-border-opacity));
	--tw-bg-opacity: 1;
	background-color: rgb(64 64 64 / var(--tw-bg-opacity));
	padding: 1rem;
	--tw-text-opacity: 1;
	color: rgb(255 255 255 / var(--tw-text-opacity));
	caret-color: #fff;
}
:is(.dark .ProseMirror pre) {
	--tw-border-opacity: 1;
	border-color: rgb(38 38 38 / var(--tw-border-opacity));
	--tw-bg-opacity: 1;
	background-color: rgb(23 23 23 / var(--tw-bg-opacity));
}
.ProseMirror pre *::-moz-selection {
	background-color: #fff3;
}
.ProseMirror pre *::selection {
	background-color: #fff3;
}
.ProseMirror pre code {
	background-color: inherit;
	padding: 0;
	color: inherit;
	--tw-shadow: 0 0 #0000;
	--tw-shadow-colored: 0 0 #0000;
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
		var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.ProseMirror pre .hljs-comment,
.ProseMirror pre .hljs-quote {
	--tw-text-opacity: 1;
	color: rgb(163 163 163 / var(--tw-text-opacity));
}
.ProseMirror pre .hljs-variable,
.ProseMirror pre .hljs-template-variable,
.ProseMirror pre .hljs-attribute,
.ProseMirror pre .hljs-tag,
.ProseMirror pre .hljs-name,
.ProseMirror pre .hljs-regexp,
.ProseMirror pre .hljs-link,
.ProseMirror pre .hljs-selector-id,
.ProseMirror pre .hljs-selector-class {
	--tw-text-opacity: 1;
	color: rgb(252 165 165 / var(--tw-text-opacity));
}
.ProseMirror pre .hljs-number,
.ProseMirror pre .hljs-meta,
.ProseMirror pre .hljs-built_in,
.ProseMirror pre .hljs-builtin-name,
.ProseMirror pre .hljs-literal,
.ProseMirror pre .hljs-type,
.ProseMirror pre .hljs-params {
	--tw-text-opacity: 1;
	color: rgb(253 186 116 / var(--tw-text-opacity));
}
.ProseMirror pre .hljs-string,
.ProseMirror pre .hljs-symbol,
.ProseMirror pre .hljs-bullet {
	--tw-text-opacity: 1;
	color: rgb(190 242 100 / var(--tw-text-opacity));
}
.ProseMirror pre .hljs-title,
.ProseMirror pre .hljs-section {
	--tw-text-opacity: 1;
	color: rgb(253 224 71 / var(--tw-text-opacity));
}
.ProseMirror pre .hljs-keyword,
.ProseMirror pre .hljs-selector-tag {
	--tw-text-opacity: 1;
	color: rgb(94 234 212 / var(--tw-text-opacity));
}
.ProseMirror pre .hljs-emphasis {
	font-style: italic;
}
.ProseMirror pre .hljs-strong {
	font-weight: 700;
}
.ProseMirror .collaboration-cursor__caret {
	pointer-events: none;
	position: relative;
	margin-left: -1px;
	margin-right: -1px;
	overflow-wrap: normal;
	word-break: normal;
	border-right-width: 1px;
	border-left-width: 1px;
	--tw-border-opacity: 1;
	border-color: rgb(0 0 0 / var(--tw-border-opacity));
}
.ProseMirror .collaboration-cursor__label {
	position: absolute;
	left: -1px;
	top: -1.4em;
	-webkit-user-select: none;
	-moz-user-select: none;
	user-select: none;
	white-space: nowrap;
	border-radius: 0.25rem;
	border-top-left-radius: 0;
	padding: 0.125rem 0.375rem;
	font-size: 0.75rem;
	line-height: 1rem;
	font-weight: 600;
	line-height: 1;
	--tw-text-opacity: 1;
	color: rgb(0 0 0 / var(--tw-text-opacity));
}
.ProseMirror ol {
	list-style-type: decimal;
}
.ProseMirror ul {
	list-style-type: disc;
}
.ProseMirror ul,
.ProseMirror ol {
	margin-top: 0.75rem;
	margin-bottom: 0.75rem;
	padding-top: 0;
	padding-bottom: 0;
	padding-left: 2rem;
	padding-right: 2rem;
}
.ProseMirror ul:first-child,
.ProseMirror ol:first-child {
	margin-top: 0;
}
.ProseMirror ul:last-child,
.ProseMirror ol:last-child {
	margin-bottom: 0;
}
.ProseMirror ul ul,
.ProseMirror ul ol,
.ProseMirror ul li,
.ProseMirror ol ul,
.ProseMirror ol ol,
.ProseMirror ol li {
	margin-top: 0.25rem;
	margin-bottom: 0.25rem;
}
.ProseMirror ul p,
.ProseMirror ol p {
	margin-top: 0;
	margin-bottom: 0.25rem;
}
.ProseMirror > ul,
.ProseMirror > ol {
	margin-top: 1rem;
	margin-bottom: 1rem;
}
.ProseMirror > ul:first-child,
.ProseMirror > ol:first-child {
	margin-top: 0;
}
.ProseMirror > ul:last-child,
.ProseMirror > ol:last-child {
	margin-bottom: 0;
}
.ProseMirror ul[data-type="taskList"] {
	list-style-type: none;
	padding: 0;
}
.ProseMirror ul[data-type="taskList"] p {
	margin: 0;
}
.ProseMirror ul[data-type="taskList"] li {
	display: flex;
}
.ProseMirror ul[data-type="taskList"] li > label {
	margin-top: 0.25rem;
	margin-right: 0.5rem;
	flex: 1 1 auto;
	flex-shrink: 0;
	flex-grow: 0;
	-webkit-user-select: none;
	-moz-user-select: none;
	user-select: none;
}
.ProseMirror ul[data-type="taskList"] li > div {
	flex: 1 1 auto;
}
.ProseMirror ul[data-type="taskList"] li[data-checked="true"] {
	text-decoration-line: line-through;
}
.ProseMirror .is-empty:before {
	pointer-events: none;
	float: left;
	height: 0px;
	width: 100%;
	color: #0006;
}
:is(.dark .ProseMirror .is-empty):before {
	color: #fff6;
}
.ProseMirror.ProseMirror-focused > p.has-focus.is-empty:before {
	content: "输入文本或按“/”键快速提示";
}
.ProseMirror.ProseMirror-focused
	> [data-type="columns"]
	> [data-type="column"]
	> p.is-empty.has-focus:before {
	content: "输入文本或按“/”键快速提示";
}
.ProseMirror > .is-editor-empty:before {
	content: "点击这里开始写作 …";
}
.ProseMirror
	blockquote
	.is-empty:not(.is-editor-empty):first-child:last-child:before {
	content: "";
}
.ProseMirror blockquote + figcaption.is-empty:not(.is-editor-empty):before {
	content: "";
}
.ProseMirror [data-placeholder][data-suggestion]:before,
.ProseMirror [data-placeholder][data-suggestion] *:before {
	content: none !important;
}
.ProseMirror .tableWrapper {
	margin-top: 1.5rem;
	margin-bottom: 1.5rem;
}
.ProseMirror table {
	box-sizing: border-box;
	width: 100%;
	border-collapse: collapse;
	border-radius: 0.25rem;
	border-color: #0000001a;
}
:is(.dark .ProseMirror table) {
	border-color: #0000001a;
}
.ProseMirror table td,
.ProseMirror table th {
	position: relative;
	min-width: 100px;
	border-width: 1px;
	border-color: #0000001a;
	padding: 0.5rem;
	text-align: left;
	vertical-align: top;
}
:is(.dark .ProseMirror table td),
:is(.dark .ProseMirror table th) {
	border-color: #0000001a;
}
.ProseMirror table td:first-of-type:not(a),
.ProseMirror table th:first-of-type:not(a) {
	margin-top: 0;
}
.ProseMirror table td p,
.ProseMirror table th p {
	margin: 0;
}
.ProseMirror table td p + p,
.ProseMirror table th p + p {
	margin-top: 0.75rem;
}
.ProseMirror table th {
	font-weight: 700;
}
.ProseMirror table .column-resize-handle {
	pointer-events: none;
	position: absolute;
	bottom: -2px;
	right: -0.25rem;
	top: 0px;
	display: flex;
	width: 0.5rem;
}
.ProseMirror table .column-resize-handle:before {
	margin-left: 0.5rem;
	height: 100%;
	width: 1px;
	background-color: #0003;
}
:is(.dark .ProseMirror table .column-resize-handle):before {
	background-color: #0003;
}
.ProseMirror table .column-resize-handle:before {
	content: "";
}
.ProseMirror table .selectedCell {
	border-style: double;
	border-color: #0003;
	background-color: #0000000d;
}
:is(.dark .ProseMirror table .selectedCell) {
	border-color: #fff3;
	background-color: #0000000d;
}
.ProseMirror table .grip-column,
.ProseMirror table .grip-row {
	position: absolute;
	z-index: 10;
	display: flex;
	cursor: pointer;
	align-items: center;
	justify-content: center;
	background-color: #0000000d;
}
:is(.dark .ProseMirror table .grip-column),
:is(.dark .ProseMirror table .grip-row) {
	background-color: #0000000d;
}
.ProseMirror table .grip-column {
	left: 0px;
	top: -0.75rem;
	margin-left: -1px;
	height: 0.75rem;
	width: calc(100% + 1px);
	border-left-width: 1px;
	border-color: #0003;
}
:is(.dark .ProseMirror table .grip-column) {
	border-color: #0003;
}
.ProseMirror table .grip-column:hover:before,
.ProseMirror table .grip-column.selected:before {
	content: "";
	width: 0.625rem;
}
.ProseMirror table .grip-column:hover {
	background-color: #0000001a;
}
:is(.dark .ProseMirror table .grip-column:hover) {
	background-color: #0000001a;
}
.ProseMirror table .grip-column:hover:before {
	border-bottom-width: 2px;
	border-style: dotted;
	border-color: #0009;
}
:is(.dark .ProseMirror table .grip-column:hover):before {
	border-color: #0009;
}
.ProseMirror table .grip-column.first {
	border-top-left-radius: calc(var(--radius) - 4px);
	border-color: transparent;
}
.ProseMirror table .grip-column.last {
	border-top-right-radius: calc(var(--radius) - 4px);
}
.ProseMirror table .grip-column.selected {
	border-color: #0000004d;
	background-color: #0000004d;
	--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
	--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
		var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
:is(.dark .ProseMirror table .grip-column.selected) {
	border-color: #0000004d;
	background-color: #0000004d;
}
.ProseMirror table .grip-column.selected:before {
	border-bottom-width: 2px;
	border-style: dotted;
}
.ProseMirror table .grip-row {
	left: -0.75rem;
	top: 0px;
	margin-top: -1px;
	height: calc(100% + 1px);
	width: 0.75rem;
	border-top-width: 1px;
	border-color: #0003;
}
:is(.dark .ProseMirror table .grip-row) {
	border-color: #0003;
}
.ProseMirror table .grip-row:hover:before,
.ProseMirror table .grip-row.selected:before {
	height: 0.625rem;
	content: "";
}
.ProseMirror table .grip-row:hover {
	background-color: #0000001a;
}
:is(.dark .ProseMirror table .grip-row:hover) {
	background-color: #0000001a;
}
.ProseMirror table .grip-row:hover:before {
	border-left-width: 2px;
	border-style: dotted;
	border-color: #0009;
}
:is(.dark .ProseMirror table .grip-row:hover):before {
	border-color: #0009;
}
.ProseMirror table .grip-row.first {
	border-top-left-radius: calc(var(--radius) - 4px);
	border-color: transparent;
}
.ProseMirror table .grip-row.last {
	border-bottom-left-radius: calc(var(--radius) - 4px);
}
.ProseMirror table .grip-row.selected {
	border-color: #0000004d;
	background-color: #0000004d;
	--tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
	--tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000),
		var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
:is(.dark .ProseMirror table .grip-row.selected) {
	border-color: #0000004d;
	background-color: #0000004d;
}
.ProseMirror table .grip-row.selected:before {
	border-left-width: 2px;
	border-style: dotted;
}
.ProseMirror p {
	margin-top: 0.375rem;
	margin-bottom: 0.375rem;
	line-height: 1.625;
}
.ProseMirror p:first-child {
	margin-top: 0;
}
.ProseMirror p:last-child {
	margin-bottom: 0;
}
.ProseMirror > p {
	margin-top: 16px;
	margin-bottom: 16px;
	font-size: 17px;
	line-height: 1.6;
}
.ProseMirror > p:first-child {
	margin-top: 0;
}
.ProseMirror > p:last-child {
	margin-bottom: 0;
}
.ProseMirror h1 {
	font-size: 1.875rem;
	line-height: 2.25rem;
}
.ProseMirror h2 {
	font-size: 1.5rem;
	line-height: 2rem;
}
.ProseMirror h3 {
	font-size: 1.25rem;
	line-height: 1.75rem;
}
.ProseMirror h4 {
	font-size: 1.125rem;
	line-height: 1.75rem;
}
.ProseMirror h5 {
	font-size: 1rem;
	line-height: 1.5rem;
}
.ProseMirror h6 {
	font-size: 0.875rem;
	line-height: 1.25rem;
}
.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3,
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
	font-weight: 700;
}
.ProseMirror h1:first-child,
.ProseMirror h2:first-child,
.ProseMirror h3:first-child,
.ProseMirror h4:first-child,
.ProseMirror h5:first-child,
.ProseMirror h6:first-child {
	margin-top: 0;
}
.ProseMirror h1:last-child,
.ProseMirror h2:last-child,
.ProseMirror h3:last-child,
.ProseMirror h4:last-child,
.ProseMirror h5:last-child,
.ProseMirror h6:last-child {
	margin-bottom: 0;
}
.ProseMirror h1,
.ProseMirror h2,
.ProseMirror h3 {
	margin-top: 1.5rem;
}
.ProseMirror h4,
.ProseMirror h5,
.ProseMirror h6 {
	margin-top: 1rem;
}
.ProseMirror a.link {
	font-weight: 800;
	--tw-text-opacity: 1;
	color: rgb(59 130 246 / var(--tw-text-opacity));
}
:is(.dark .ProseMirror a.link) {
	--tw-text-opacity: 1;
	color: rgb(96 165 250 / var(--tw-text-opacity));
}
.ProseMirror mark {
	border-radius: calc(var(--radius) - 4px);
	--tw-bg-opacity: 1;
	background-color: rgb(239 68 68 / var(--tw-bg-opacity));
	-webkit-box-decoration-break: clone;
	box-decoration-break: clone;
	padding-top: 0.25rem;
	padding-bottom: 0.25rem;
	padding-left: 0;
	padding-right: 0;
	color: inherit;
}
:is(.dark .ProseMirror mark) {
	--tw-bg-opacity: 1;
	background-color: rgb(248 113 113 / var(--tw-bg-opacity));
}
.ProseMirror img {
	height: auto;
	width: 100%;
	max-width: 100%;
}
.ProseMirror [data-type="horizontalRule"] {
	margin-top: 2rem;
	margin-bottom: 2rem;
	cursor: pointer;
	padding-top: 1rem;
	padding-bottom: 1rem;
	transition-property: all;
	transition-duration: 0.1s;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	animation-duration: 0.1s;
	animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ProseMirror [data-type="horizontalRule"].ProseMirror-selectednode {
	background-color: #0000000d;
}
:is(.dark .ProseMirror [data-type="horizontalRule"].ProseMirror-selectednode) {
	background-color: #ffffff1a;
}
.ProseMirror [data-type="horizontalRule"].ProseMirror-selectednode hr {
	border-top-color: #0000004d;
}
:is(
		.dark .ProseMirror [data-type="horizontalRule"].ProseMirror-selectednode hr
	) {
	border-top-color: #ffffff4d;
}
.ProseMirror
	[data-type="horizontalRule"]:hover:not(
		.ProseMirror [data-type="horizontalRule"].ProseMirror-selectednode
	) {
	background-color: #0000000d;
}
:is(
		.dark
			.ProseMirror
			[data-type="horizontalRule"]:hover:not(
				.ProseMirror [data-type="horizontalRule"].ProseMirror-selectednode
			)
	) {
	background-color: #ffffff1a;
}
.ProseMirror [data-type="horizontalRule"] hr {
	border-width: 0px;
	border-top-width: 1px;
	border-color: #0003;
	background-color: #000c;
}
:is(.dark .ProseMirror [data-type="horizontalRule"] hr) {
	border-color: #fff3;
	background-color: #fffc;
}
.ProseMirror {
	caret-color: hsl(var(--foreground));
	outline-width: 0px;
}
.ProseMirror > * {
	margin-left: auto;
	margin-right: auto;
	max-width: 80%;
}
.ProseMirror .selection {
	display: inline;
}
.ProseMirror *::-moz-selection {
	background-color: hsl(var(--ai-writing-selection));
}
.ProseMirror .selection,
.ProseMirror *::selection {
	background-color: hsl(var(--ai-writing-selection));
}
.ProseMirror > .react-renderer {
	margin-top: 1.5rem;
	margin-bottom: 1.5rem;
}
.ProseMirror > .react-renderer:first-child {
	margin-top: 0;
}
.ProseMirror > .react-renderer:last-child {
	margin-bottom: 0;
}
.ProseMirror.resize-cursor {
	cursor: col-resize;
}
.ProseMirror .ProseMirror-gapcursor {
	position: relative;
	margin-left: auto;
	margin-right: auto;
	width: 100%;
	max-width: 42rem;
}
.ProseMirror .ProseMirror-gapcursor:after {
	top: -1.5em;
	left: 0px;
	right: 0px;
	margin-left: auto;
	margin-right: auto;
	width: 100%;
	max-width: 42rem;
	border-top-color: #0006;
}
:is(.dark .ProseMirror .ProseMirror-gapcursor):after {
	border-top-color: #fff6;
}
[data-theme="slash-command"] {
	width: 1000vw;
}
.ProseMirror .is-empty:before {
	--tw-text-opacity: 1;
	color: rgb(115 115 115 / var(--tw-text-opacity));
}
:is(.dark .ProseMirror .is-empty):before {
	--tw-text-opacity: 1;
	color: rgb(163 163 163 / var(--tw-text-opacity));
}

/* 覆盖 Tiptap 编辑器的选中样式 */
.ProseMirror ::selection {
	background-color: rgba(100, 200, 255, 0.5) !important;
}

/* 持久化选择样式 */
.persistent-selection {
	background-color: rgba(100, 200, 255, 0.5) !important;
}

/* 当无法直接包装选中内容时的备选方案 */
.ProseMirror.has-persistent-selection {
	position: relative;
}

.ProseMirror.has-persistent-selection::after {
	content: "";
	position: absolute;
	background-color: rgba(100, 200, 255, 0.3);
	pointer-events: none;
	/* 这里可以添加更复杂的逻辑来标记选中区域 */
}
