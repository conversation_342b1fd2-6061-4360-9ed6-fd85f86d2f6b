import { VueRenderer } from "@tiptap/vue-3";
import tippy from "tippy.js";

import CommandsList from "./CommandsList.vue";

/**
 * 配置斜杠命令的建议项及交互逻辑
 * @param items 自定义建议项（可选），未传入时使用默认建议项
 * @returns 包含建议项配置和渲染逻辑的对象
 */
export default function set(items) {
	return {
		/**
		 * 获取斜杠命令的建议项列表
		 * @returns 建议项数组（自定义或默认）
		 */
		items: () => {
			// 优先使用传入的自定义建议项，无自定义时使用默认项（示例为"一级标题"）
			return items
				? items
				: [
						{
							title: "一级标题",
							icon: "mdi:format-header-1",
							// 命令执行逻辑：删除当前输入的"/"，插入一级标题节点
							command: ({ editor, range }) => {
								editor
									.chain()
									.focus()
									.deleteRange(range) // 删除斜杠输入的位置范围
									.setNode("heading", { level: 1 }) // 插入一级标题节点
									.run();
							},
						},
				  ];
		},
		/**
		 * 控制建议框的渲染与交互生命周期
		 * @returns 包含生命周期方法的对象（onStart/onUpdate/onKeyDown/onExit）
		 */
		render: () => {
			let component; // Vue渲染器实例（用于挂载命令列表组件）
			let popup; // tippy弹出框实例（用于显示建议框）

			return {
				/**
				 * 建议框初始化时触发（首次输入斜杠时）
				 * @param props 包含editor（编辑器实例）、clientRect（光标位置）等信息
				 */
				onStart: (props) => {
					// 初始化Vue渲染器，挂载命令列表组件（CommandsList.vue）
					component = new VueRenderer(CommandsList, {
						props,
						editor: props.editor,
					});

					// 获取编辑器选区信息（用于判断是否在段落内）
					const { $cursor } = props.editor.view.state.selection;
					const selection = props.editor.state.selection;
					const context = selection.$from.depth
						? selection.$from.node(selection.$from.depth - 1)
						: null;
					const listItem = context && context.type.name === "listItem"; // 是否在列表项内
					const isCursorInParagraph =
						$cursor && $cursor.parent.type.name === "paragraph"; // 光标是否在段落内

					// 仅当光标在段落内且不在列表项时显示建议框
					if (!props.clientRect || !isCursorInParagraph || listItem) {
						return;
					}

					// 使用tippy创建弹出框（基于光标位置定位）
					popup = tippy("body", {
						getReferenceClientRect: props.clientRect, // 光标位置坐标
						appendTo: () => document.body, // 挂载到body避免样式隔离问题
						content: component.element, // 弹出框内容为Vue组件渲染结果
						showOnCreate: true, // 创建后立即显示
						interactive: true, // 允许与弹出框内容交互（如点击命令）
						trigger: "manual", // 手动控制显示/隐藏
						placement: "bottom-start", // 弹出框位置：光标底部左侧对齐
					});
				},

				/**
				 * 输入内容变化时触发（用于更新建议框）
				 * @param props 包含最新的clientRect（光标位置）和text（输入内容）
				 */
				onUpdate(props) {
					component.updateProps(props); // 更新Vue组件的props（如最新的建议项）

					// 无光标位置 或 输入内容不是"/"时隐藏弹出框
					if (!props.clientRect || props.text !== "/") {
						popup[0].hide();
						return;
					}

					// 输入内容为"/"时显示弹出框，并更新位置
					if (props.text === "/") {
						popup[0].show();
					}
					popup[0].setProps({
						getReferenceClientRect: props.clientRect, // 更新光标位置（随输入移动）
					});
				},

				/**
				 * 键盘按下时触发（用于快捷键控制）
				 * @param props 包含event（键盘事件）和editor（编辑器实例）
				 * @returns 是否阻止默认事件（true表示已处理）
				 */
				onKeyDown(props) {
					// 按下ESC键时隐藏弹出框
					if (props.event.key === "Escape") {
						popup[0].hide();
						return true; // 阻止默认ESC行为（避免编辑器失去焦点）
					}

					// 其他按键由命令列表组件处理（如上下箭头选择命令）
					return component.ref?.onKeyDown?.(props);
				},

				/**
				 * 建议框退出时触发（清理资源）
				 */
				onExit() {
					popup[0].destroy(); // 销毁tippy弹出框
					component.destroy(); // 销毁Vue渲染器
				},
			};
		},
	};
}
