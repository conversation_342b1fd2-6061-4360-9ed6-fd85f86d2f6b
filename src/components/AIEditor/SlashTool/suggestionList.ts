import { Code, H1, H2, H3, <PERSON>Two, OrderedList, Text } from "@icon-park/vue-next";
import { h } from "vue";
import AIBubbleButton from "../AITools/AIBubbleButton.vue";

export const suggestionList = [
	{
		title: <PERSON>BubbleButton,
		command: ({ editor, range }) => {
			editor.chain().focus().deleteRange(range).run()
		}
	},
  {
    title: '正文',
    icon: h(Text, {theme: 'outline'}),
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).setNode('paragraph').run();
    },
  },
  {
    title: '一级标题',
    icon: h(H1, {theme: 'outline'}),
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).setNode('heading', { level: 1 }).run();
    },
  },
  {
    title: '二级标题',
    icon: h(H2, {theme: 'outline'}),
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).setNode('heading', { level: 2 }).run();
    },
  },
  {
    title: '三级标题',
    icon: h(H3, {theme: 'outline'}),
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).setNode('heading', { level: 3 }).run();
    },
  },
  {
    title: '代码块',
    icon: h(Code, {theme: 'outline'}),
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).setNode('codeBlock').run();
    },
  },
  {
    title: '无序列表',
    icon: h(ListTwo, {theme: 'outline'}),
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).toggleBulletList().run();
    },
  },
  {
    title: '有序列表',
    icon: h(OrderedList),
    command: ({ editor, range }) => {
      editor.chain().focus().deleteRange(range).toggleOrderedList().run();
    },
  },
];
