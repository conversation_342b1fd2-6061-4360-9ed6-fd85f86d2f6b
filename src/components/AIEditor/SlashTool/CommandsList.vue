<template>
	<div class="w-max flex shadow rounded-lg bg-white p-2 overflow-auto shadow-dropdown block-full-button scrollbar">
		<div id="scrollingDiv"
			class="flex flex-col items-center gap-y-1 max-h-52 h-max overflow-y-auto dark:bg-zinc-900 dark:text-zinc-300">
			<template v-for="(item, index) in items" :key="index">
				<template v-if="isVNode(item.title) || isVueComponent(item.title)">
					<component :is="item.title" @click="selectItem(index)"></component>
				</template>
				<NButton v-else quaternary block style="--n-font-weight: 600;" @click="selectItem(index)">
					<template #icon>
						<!-- <Icon :icon="item.icon" /> -->
						 <component :is="item.icon" />
					</template>
					{{ item.title }}
				</NButton>
			</template>

			<!-- <button
				class="tippy-item flex items-center p-1 pr-12 text-xs hover:bg-zinc-200 dark:hover:bg-zinc-700 transition-colors rounded cursor-pointer w-full group"
				:class="{ 'bg-zinc-200 dark:bg-zinc-700': index === selectedIndex }" v-for="(item, index) in items" :key="index"
				@click="selectItem(index)">
				<div
					class="flex p-1 mr-2 bg-zinc-200 dark:bg-zinc-500 group-hover:bg-zinc-50 dark:group-hover:bg-zinc-600 transition-colors rounded-sm"
					:class="{ 'bg-zinc-50 dark:bg-zinc-600': index === selectedIndex }">
					<Icon :icon="item.icon" />
				</div>
				{{ item.title }}
			</button> -->
		</div>
	</div>
</template>

<script setup lang="ts">
import { NButton } from 'naive-ui';
import { ref, onMounted, watch, isVNode } from 'vue'
import { useEditorCtx } from '../editorCtx';

const props = defineProps<{
	items: any[]
	command: (item: any) => void
}>()

const selectedIndex = ref(0)
const scrollingDiv = ref<any>(null)
const editorCtx = useEditorCtx()

onMounted(() => {
	scrollingDiv.value = document.getElementById('scrollingDiv')
})

watch(() => props.items, () => {
	selectedIndex.value = 0
})

function isVueComponent(obj) {
  return obj && typeof obj === 'object' && ('setup' in obj || 'render' in obj);
}

const onKeyDown = ({ event }) => {
	if (event.key === 'ArrowUp') {
		upHandler();
		return true;
	}

	if (event.key === 'ArrowDown') {
		downHandler();
		return true;
	}

	if (event.key === 'Enter') {
		enterHandler();
		return true;
	}
	return false;
}
const upHandler = () => {
	selectedIndex.value = (selectedIndex.value + props.items.length - 1) % props.items.length;
	scrollDiv();
}
const downHandler = () => {
	selectedIndex.value = (selectedIndex.value + 1) % props.items.length;
	scrollDiv();
}
const scrollDiv = () => {
	let buttons = scrollingDiv.value.querySelectorAll('button');
	let button = buttons[selectedIndex.value];
	button.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' });
}
const enterHandler = () => {
	selectItem(selectedIndex.value);
}
const selectItem = (index) => {
	const item = props.items[index];

	// if(isVNode(item) || isVueComponent(item)) {
	// 	editorCtx.editor!.chain().focus().deleteRange(item.range)
	// 	return
	// }

	if (item) {
		props.command(item);
	}
}

</script>
