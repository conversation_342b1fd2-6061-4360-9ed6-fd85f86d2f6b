<template>
	<div class="w-max flex shadow rounded-lg bg-white p-2 overflow-auto shadow-dropdown block-full-button scrollbar"
		ref="containerRef" tabindex="0" @keydown="handleKeyDown">
		<div id="scrollingDiv"
			class="flex flex-col items-center gap-y-1 max-h-52 h-max overflow-y-auto dark:bg-zinc-900 dark:text-zinc-300">
			<template v-for="(item, index) in items" :key="index">
				<template v-if="isVNode(item.title) || isVueComponent(item.title)">
					<component :is="item.title" @click="selectItem(index)" :class="{ 'selected-item': index === selectedIndex }">
					</component>
				</template>
				<NButton v-else quaternary block style="--n-font-weight: 600;"
					:class="{ 'selected-item': index === selectedIndex }" @click="selectItem(index)">
					<template #icon>
						<!-- <Icon :icon="item.icon" /> -->
						<component :is="item.icon" />
					</template>
					{{ item.title }}
				</NButton>
			</template>

			<!-- <button
				class="tippy-item flex items-center p-1 pr-12 text-xs hover:bg-zinc-200 dark:hover:bg-zinc-700 transition-colors rounded cursor-pointer w-full group"
				:class="{ 'bg-zinc-200 dark:bg-zinc-700': index === selectedIndex }" v-for="(item, index) in items" :key="index"
				@click="selectItem(index)">
				<div
					class="flex p-1 mr-2 bg-zinc-200 dark:bg-zinc-500 group-hover:bg-zinc-50 dark:group-hover:bg-zinc-600 transition-colors rounded-sm"
					:class="{ 'bg-zinc-50 dark:bg-zinc-600': index === selectedIndex }">
					<Icon :icon="item.icon" />
				</div>
				{{ item.title }}
			</button> -->
		</div>
	</div>
</template>

<script setup lang="ts">
import { NButton } from 'naive-ui';
import { ref, onMounted, watch, isVNode, nextTick } from 'vue'

const props = defineProps<{
	items: any[]
	command: (item: any) => void
}>()

const selectedIndex = ref(0)
const scrollingDiv = ref<HTMLElement | null>(null)
const containerRef = ref<HTMLElement | null>(null)

onMounted(async () => {
	scrollingDiv.value = document.getElementById('scrollingDiv')
	// 确保组件获得焦点以接收键盘事件
	await nextTick()
	if (containerRef.value) {
		containerRef.value.focus()
	}
})

watch(() => props.items, () => {
	selectedIndex.value = 0
}, { immediate: true })

function isVueComponent(obj: any): boolean {
	return obj && typeof obj === 'object' && ('setup' in obj || 'render' in obj);
}

// 键盘事件处理函数
const handleKeyDown = (event: KeyboardEvent) => {
	if (event.key === 'ArrowUp') {
		event.preventDefault()
		upHandler();
	} else if (event.key === 'ArrowDown') {
		event.preventDefault()
		downHandler();
	} else if (event.key === 'Enter') {
		event.preventDefault()
		enterHandler();
	}
}

const upHandler = () => {
	if (props.items.length === 0) return
	selectedIndex.value = (selectedIndex.value + props.items.length - 1) % props.items.length;
	scrollToSelected();
}

const downHandler = () => {
	if (props.items.length === 0) return
	selectedIndex.value = (selectedIndex.value + 1) % props.items.length;
	scrollToSelected();
}

const scrollToSelected = () => {
	if (!scrollingDiv.value) return

	// 查找所有按钮和组件元素
	const buttons = scrollingDiv.value.querySelectorAll('button, .selected-item');
	const selectedElement = buttons[selectedIndex.value] as HTMLElement;

	if (selectedElement) {
		selectedElement.scrollIntoView({
			behavior: 'smooth',
			block: 'nearest',
			inline: 'start'
		});
	}
}

const enterHandler = () => {
	selectItem(selectedIndex.value);
}

const selectItem = (index: number) => {
	const item = props.items[index];

	if (item) {
		props.command(item);
	}
}

</script>

<style scoped>
/* 选中状态的视觉反馈 */
.selected-item {
	background-color: #e5e7eb !important;
	border: 2px solid #3b82f6 !important;
	box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2) !important;
}

/* 暗色模式下的选中状态 */
.dark .selected-item {
	background-color: #374151 !important;
	border-color: #60a5fa !important;
	box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.2) !important;
}

/* 确保容器可以获得焦点 */
.w-max:focus {
	outline: none;
}

/* 为按钮添加过渡效果 */
.n-button {
	transition: all 0.2s ease-in-out;
}
</style>
