<template>
	<DragHandle class="drag-handle-container" v-if="editorCtx.editor" :editor="editorCtx.editor as Editor" @nodeChange="handleNodeChange as any">
		<NPopover trigger="click" :show="visible" :showArrow="false" style="--n-padding: 0;" placement="right-start"
			:to="false">
			<template #trigger>
				<div class="editor-drag-handle -translate-x-2 translate-y-2 text-neutral-400 hover:text-neutral-600"
					@click.stop="open">
					<Drag />
				</div>
			</template>
			<div class="p-3 rounded-lg shadow-shadow1 w-[240px]">
				<div class="flex flex-wrap">
					<NButton quaternary @click="actions('h1')">
						<template #icon>
							<H1 theme="outline" />
						</template>
					</NButton>
					<NButton quaternary @click="actions('h2')">
						<template #icon>
							<H2 theme="outline" />
						</template>
					</NButton>
					<NButton quaternary @click="actions('h3')">
						<template #icon>
							<H3 theme="outline" />
						</template>
					</NButton>
					<NButton quaternary @click="actions('h4')">
						<template #icon>
							<LevelFourTitle theme="outline" />
						</template>
					</NButton>
					<NButton quaternary @click="actions('h5')">
						<template #icon>
							<LevelFiveTitle theme="outline" />
						</template>
					</NButton>
					<NButton quaternary @click="actions('h6')">
						<template #icon>
							<LevelSixTitle theme="outline" />
						</template>
					</NButton>
					<NButton quaternary @click="actions('blockquote')">
						<template #icon>
							<Quote theme="outline" />
						</template>
					</NButton>
					<NButton quaternary @click="actions('bulletList')">
						<template #icon>
							<ListTwo theme="outline" />
						</template>
					</NButton>
					<NButton quaternary @click="actions('orderedList')">
						<template #icon>
							<OrderedList theme="outline" />
						</template>
					</NButton>
					<NButton quaternary @click="actions('taskList')">
						<template #icon>
							<ListCheckbox theme="outline" />
						</template>
					</NButton>
					<NButton quaternary @click="actions('code')"><template #icon><CodeIcon theme="outline" /></template></NButton>
				</div>
				<NDivider />
				<div>
					<NPopover trigger="click" placement="right-start" :show-arrow="false"
						style="transform: translateX(16px);--n-padding:8px;">
						<template #trigger>
							<NButton quaternary block strong @click.stop>
								<template #icon>
									<AlignTextLeft theme="outline" />
								</template>
								<div class="flex justify-between items-center w-full">
									<span>对齐</span>
									<Right />
								</div>
							</NButton>
						</template>
						<div @click.stop>
							<NButton quaternary block @click="actions('justifyLeft')">
								<template #icon>
									<AlignTextLeft />
								</template>居左对齐
							</NButton>
							<NButton quaternary block @click="actions('justifyCenter')">
								<template #icon>
									<AlignTextCenter />
								</template>居中对齐
							</NButton>
							<NButton quaternary block @click="actions('justifyRight')">
								<template #icon>
									<AlignTextRight />
								</template>居右对齐
							</NButton>
						</div>
					</NPopover>
					<!-- <NPopover trigger="click" placement="right-start" :show-arrow="false"
					style="transform: translateX(16px);--n-padding:8px;">
					<template #trigger>
						<NButton quaternary block @click.stop>
							<template #icon>
								<Platte theme="outline" />
							</template>
							<div class="flex justify-between items-center w-full">
								<span>颜色</span>
								<Right />
							</div>
						</NButton>
					</template>
					<div>
						<NButton quaternary block><template #icon>
								<AlignTextLeft />
							</template>TODO</NButton>
					</div>
				</NPopover> -->
				</div>
				<NDivider />
				<div>
					<NButton quaternary block strong @click="actions('ai')">
						<template #icon>
							<AiSvg class="w-5 h-5 text-primary" />
						</template>AI写作助手
					</NButton>
					<NButton quaternary block strong @click="actions('copy')">
						<template #icon>
							<Copy theme="outline" />
						</template>复制
					</NButton>
					<NButton quaternary block strong @click="actions('cut')">
						<template #icon>
							<CuttingOne theme="outline" />
						</template>剪切
					</NButton>
					<NButton quaternary block strong @click="actions('delete')">
						<template #icon>
							<DeleteOne theme="outline" />
						</template>删除
					</NButton>
				</div>
				<!-- <NDivider /> -->
				<!-- <div>
				<NPopover trigger="click" placement="right-start" :show-arrow="false"
					style="transform: translateX(16px);--n-padding:8px;">
					<template #trigger>
						<NButton quaternary block @click.stop><template #icon>
								<AddFour theme="outline" />
							</template>
							<span class="flex justify-between w-full">
								<span>在下方添加</span>
								<Right />
							</span>
						</NButton>
					</template>
					<div>
						<NButton quaternary block>
							<template #icon>
								<AlignTextLeft />
							</template>
							TODO
						</NButton>
					</div>
				</NPopover>
			</div> -->
			</div>
		</NPopover>
	</DragHandle>
</template>

<script setup lang="ts">
import { NButton, NDivider, NPopover, useMessage } from 'naive-ui'
import { AlignTextCenter, AlignTextLeft, AlignTextRight, AddFour, Copy, CuttingOne, DeleteOne, H1, H2, H3, LevelFiveTitle, LevelFourTitle, LevelSixTitle, ListCheckbox, ListTwo, OrderedList, Platte, Quote, Right, Drag, Code as CodeIcon } from '@icon-park/vue-next'
import AiSvg from "../icons/ai.svg"
import { editorHandles } from '../tools/handle';
import { Editor } from '@tiptap/vue-3';
import { useEditorCtx } from '../editorCtx';
import { onMounted, onUnmounted, ref } from 'vue';
import { copyToClipboard } from '@/utils/clipboard';
import { DragHandle } from '@tiptap/extension-drag-handle-vue-3'
import { useFloatAIStore } from '../AITools/floatAIStore';

const props = defineProps<{
	tools?: any
}>()

const floatAIStore = useFloatAIStore()
const editorCtx = useEditorCtx()
const message = useMessage()

const visible = ref(false)

const showAITool = () => {
	// 起始位置需要+1, 不然获取到的是整个文档的
	const blockNodePos = editorCtx.editor?.$pos(editorCtx.dragOverFrom! + 1)
	const blockElement = blockNodePos?.element
	const rect = blockElement?.getBoundingClientRect()!;
	const ancestorRect = editorCtx.editorScrollElement!.querySelector('.ai-editor-content')!.getBoundingClientRect()

	floatAIStore.open(editorCtx.dragOverNode?.textContent!, {
			x: rect.left - ancestorRect.left,
			y: rect.top - ancestorRect.top + rect.height + 12, // 使用底部坐标以便元素定位在块下方
		})
}

const open = () => {
	visible.value = true
	editorCtx.editor?.commands.focus()
	editorCtx.editor?.commands.setTextSelection({
		from: editorCtx.dragOverFrom!,
		to: editorCtx.dragOverFrom! + editorCtx.dragOverNode?.nodeSize! + 1
	})
}

const close = () => {
	visible.value = false
}

onMounted(() => {
	document.body.addEventListener('click', close)
})

onUnmounted(() => {
	document.body.removeEventListener('click', close)
})

const handleNodeChange = (data: { editor: Editor; node: Node | null; pos: number; }) => {
	if (data.pos > -1) {
		// @ts-ignore
		editorCtx.dragOverNode = data.node as Node
		editorCtx.dragOverFrom = data.pos
		document.querySelector('.drag-handle-container')?.classList.remove('hidden')
	}
	else {
		document.querySelector('.drag-handle-container')?.classList.add('hidden')
	}
}

const actions = (type) => {
	switch (type) {
		case 'h1':
			editorHandles.setHeading({ editor: editorCtx.editor as Editor, level: 1 })
			break;
		case 'h2':
			editorHandles.setHeading({ editor: editorCtx.editor as Editor, level: 2 })
			break;
		case 'h3':
			editorHandles.setHeading({ editor: editorCtx.editor as Editor, level: 3 })
			break;
		case 'h4':
			editorHandles.setHeading({ editor: editorCtx.editor as Editor, level: 4 })
			break;
		case 'h5':
			editorHandles.setHeading({ editor: editorCtx.editor as Editor, level: 5 })
			break;
		case 'h6':
			editorHandles.setHeading({ editor: editorCtx.editor as Editor, level: 6 })
			break;
		case 'blockquote':
			editorHandles.setBlockquote({ editor: editorCtx.editor as Editor })
			break;
		case 'bulletList':
			editorHandles.toggleBulletList({ editor: editorCtx.editor as Editor })
			break;
		case 'orderedList':
			editorHandles.toggleOrderedList({ editor: editorCtx.editor as Editor })
			break;
		case 'taskList':
			editorHandles.toggleTaskList({ editor: editorCtx.editor as Editor })
			break;
		case 'code':
			editorHandles.toggleCode({ editor: editorCtx.editor as Editor })
			break;
		case 'justifyLeft':
			editorHandles.setTextAlign({ editor: editorCtx.editor as Editor, align: 'left' })
			break;
		case 'justifyCenter':
			editorHandles.setTextAlign({ editor: editorCtx.editor as Editor, align: 'center' })
			break;
		case 'justifyRight':
			editorHandles.setTextAlign({ editor: editorCtx.editor as Editor, align: 'right' })
			break;
		case 'ai':
			showAITool()
			break;
		case 'copy':
			copyToClipboard(editorCtx.dragOverNode?.textContent as string)
			message.success('复制成功')
			break;
		case 'cut':
			copyToClipboard(editorCtx.dragOverNode?.textContent as string)
			// const pos = editorCtx.editor?.$pos(editorCtx.dragOverFrom!+1)
			// editorCtx.editor?.commands.deleteNode(editorCtx.dragOverNode?.type!)
			editorCtx.editor?.commands.deleteSelection()
			editorCtx.editor?.commands.clearPersistentSelection()
			message.success('剪切成功')
			break;
		case 'delete':
			// editorHandles.delete({ editor: editorCtx.editor as Editor })
			editorCtx.editor?.commands.deleteSelection()
			editorCtx.editor?.commands.clearPersistentSelection()
			message.success('删除成功')
			break;
		case 'addAbove':
			editorHandles.addAbove({ editor: editorCtx.editor as Editor })
			break;
		case 'addBelow':
			editorHandles.addBelow({ editor: editorCtx.editor as Editor })
			break;
	}
	visible.value = false
}

</script>

<style lang="less">
.drag-handle-container {

}
.editor-drag-handle {
	z-index: 50;
	cursor: grab;

	&:hover {
		background-color: var(--novel-stone-100);
		transition: background-color 0.2s;
	}

	&:active {
		background-color: var(--novel-stone-200);
		transition: background-color 0.2s;
		cursor: grabbing;
	}

	&.hide {
		opacity: 0;
		pointer-events: none;
	}
}

::selection {
	background-color: #70cff850;
}

.ProseMirror {
	padding: 1rem 1rem 1rem 0;

	* {
		margin-top: 0.75em;
	}

	>* {
		margin-left: 3rem;
	}

	.ProseMirror-widget * {
		margin-top: auto;
	}

	ul,
	ol {
		padding: 0 1rem;
	}
}

.ProseMirror-noderangeselection {
	*::selection {
		background: transparent;
	}

	* {
		caret-color: transparent;
	}
}

.ProseMirror-selectednode,
.ProseMirror-selectednoderange {
	position: relative;

	&::before {
		position: absolute;
		pointer-events: none;
		z-index: -1;
		content: '';
		top: -0.25rem;
		left: -0.25rem;
		right: -0.25rem;
		bottom: -0.25rem;
		background-color: #70cff850;
		border-radius: 0.2rem;
	}
}

.custom-drag-handle {
	&::after {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 1rem;
		height: 1.25rem;
		content: '⠿';
		font-weight: 700;
		cursor: grab;
		background: #0d0d0d10;
		color: #0d0d0d50;
		border-radius: 0.25rem;
	}
}
</style>
