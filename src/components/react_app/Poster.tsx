import React, { useRef, useEffect } from 'react'
import 'markdown-to-image/dist/style.css'
import { Md2<PERSON>oster, Md2Poster<PERSON>ontent, Md2PosterHeader, Md2PosterFooter, Md2PosterProps } from 'markdown-to-image'

export const Poster: React.FC = (props: Md2PosterProps & {
	bgClass?: string,
	marginClass?: string
	proseSize?: string
	themeClass?: string
	content?: string
	triggerCopy?: boolean
	triggerDownload?: boolean
	copySuccessCallback?: () => void
	copyFailCallback?: (err: any) => void
	downloadSuccessCallback?: () => void
	downloadFailCallback?: (err: any) => void
}) => {
	const {bgClass, themeClass, marginClass, proseSize, content, triggerCopy, triggerDownload, copySuccessCallback, copyFailCallback, downloadSuccessCallback, downloadFailCallback, ...rest} = props
	const $main = useRef<any>(null)

	useEffect(() => {
		if(triggerCopy && $main.current) {
			// console.log(1111, $main.current, $main.current.handleCopy)
			$main.current.handleCopy().then(() => {
				copySuccessCallback?.()
			}).catch(err => copyFailCallback?.(err))
		}
	}, [triggerCopy])

	useEffect(() => {
		if(triggerDownload && $main.current) handleDownload()
	}, [triggerDownload])

	const handleDownload = async () => {
		try {
			const blob = await $main.current.handleCopy()
			const url = URL.createObjectURL(blob);
			const a = document.createElement('a');
			a.href = url;
			a.download = 'image.png'; // 设置下载的文件名
			a.click();
			URL.revokeObjectURL(url); // 释放URL对象
			downloadSuccessCallback?.()
		}
		catch (err) {
			downloadFailCallback?.(err)
		}
	}

	const markdown = `
# 暂无内容

请在编辑器内生成或输入内容
...
`
	return (
		<div className="w-full">
				<Md2Poster ref={$main} className={`${bgClass} ${themeClass} w-full mx-auto !max-w-2xl`} {...rest}>
					<div className={marginClass}>
						{/* <Md2PosterHeader>Poster Header</Md2PosterHeader> */}
						<Md2PosterContent className={`${proseSize}`} articleClassName='prose prose-gray prose-img:rounded-lg prose-img:border prose-img:opacity-100 font-size-inherit'>{ content || markdown}</Md2PosterContent>
						{/* <Md2PosterFooter>Powered by ReadPo.com</Md2PosterFooter> */}
					</div>
				</Md2Poster>
		</div>
	)
}
