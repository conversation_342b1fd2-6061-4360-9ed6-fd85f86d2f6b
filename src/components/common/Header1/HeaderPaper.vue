<template>
	<HeaderBase showMember showLogin showLogo>
		<template #nav>
			<!--占位-->
			<!-- <div class="w-[270px]"></div> -->
			<div class="flex flex-1 items-center text-[14px] text-[#444] gap-[28px] sm:hidden ipad:hidden">
				<template v-for="(item, index) in navList" :key="index">
					<a v-if="(!item.hide || item.hide() === false) && !item.expand" @click="handleRoute(item)"
						class="relative flex items-center hover:text-[#0E69FF] sm:text-sm text-[14px] cursor-pointer grow-0 shrink-0 flex-nowrap gap-[3px] px-[5px] sm:px-0"
						:class="{ 'text-[#0E69FF]': item.key === route.meta.navKey }">
						<SvgLoader :name="item.icon" class="w-[16px] h-[16px]" />
						<!-- <span>{{ textTransform(item.title, "论文", "学术") }}</span> -->
						<span v-replace>{{ item.title }}</span>
						<div v-if="item.tag" :class="['nav-badge']">
							<div :class="item.tag"></div>
						</div>
					</a>
					<NPopover v-else trigger="hover" :placement="'bottom'" :show-arrow="false">
						<template #trigger>
							<a @click="handleRoute(item)"
								class="relative flex items-center justify-center hover:text-[#0E69FF] sm:text-sm text-[14px] cursor-pointer grow-0 shrink-0 flex-nowrap gap-[3px] px-[5px] sm:px-0"
								:class="{
									'text-[#0E69FF]': item.key === route.meta.navKey,
								}">
								<SvgLoader :name="item.icon" class="w-[16px] h-[16px]" />
								<span v-replace>{{ item.title }}</span>
								<div v-if="item.tag" :class="['nav-badge']">
									<div :class="item.tag"></div>
								</div>
								<IconDown class="text-black pt-1" />
							</a>
						</template>
						<div class="panel-container max-w-[1007px] max-h-[418px] flex flex-col gap-y-[12px] py-[14px] px-[10px]"
							:class="{
								'px-[24px] py-[27px] gap-y-[30px]': item?.deep !== 1,
							}">
							<div v-if="item.key.startsWith('paper')" class="flex flex-col">
								<div v-for="(i, index) in item?.childList" :key="index">
									<div class="text-[#3D3D3D] text-[12px] mb-[11px]" v-replace>
										{{ i.title }}
									</div>
									<div class="grid grid-cols-5 gap-x-[12px]">
										<div v-for="(j, jIndex) in i.list" :key="jIndex"
											class="flex flex-row items-center gap-x-[14px] bg-[#fff] hover:bg-[#F6F8F9] hover:rounded-[8px] cursor-pointer py-[9px]"
											@click="handlePanelJump(j.path!)">
											<div class="flex items-center justify-start mb-1 relative">
												<img :src="j.icon" class="w-[40px] h-[40px]" alt="" />
											</div>
											<div class="relative">
												<div class="text-[12px] text-[#3D3D3D] text-left" v-replace>
													{{ j.title }}
												</div>
												<div v-if="j.isHot" class="hot-tag tag">HOT</div>
												<div class="text-[12px] text-[#A1A1A1] text-left" v-replace>
													{{ j.description }}
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div v-else v-for="(i, index) in item?.childList" :key="index"
								class="flex flex-row gap-x-[19px] items-center">
								<template v-if="i.type === 'item'">
									<div class="flex items-center hover:text-[#0E69FF] cursor-pointer">
										<div
											class="w-[40px] h-[40px] bg-[#F5F6F7] rounded-[5px] flex items-center justify-center mr-[10px]">
											<SvgLoader :name="i.icon || 'paper'" class="w-[18px] h-[22px]" />
										</div>
										<div class="flex flex-col">
											<span class="text-[14px] text-[#000000] font-[600]" v-replace>{{ i.title }}</span>
											<span class="text-[12px] text-[#A1A1A1]" v-replace>{{
												i.description
											}}</span>
										</div>
									</div>
								</template>
								<template v-else-if="i.type === 'group'">
									<span class="inline-flex w-[78px] text-[#000000] font-[600]">{{ i.title }}</span>
									<NDivider :vertical="true" style="--color: #d8d8d8" />
									<div v-for="(j, index) in i.list" :key="index" class="panel-item-list text-[#3D3D3D]">
										<a :href="'/office' + j.path" target="_blank" class="hover:text-[#0E69FF]" v-replace>{{ j.title
										}}</a>
									</div>
								</template>
							</div>
						</div>
					</NPopover>
				</template>
			</div>
		</template>
	</HeaderBase>
</template>

<script setup lang="ts">
import Paper from './index.vue'
import HeaderBase from '@/components/common/Header1/HeaderBase.vue'
import adultIcon from "@/assets/paper/adult.png";
import booksIcon from "@/assets/paper/books.png";
import careerIcon from "@/assets/paper/career.png";
import checkIcon from "@/assets/paper/check.png";
import courseIcon from "@/assets/paper/course.png";
import examIcon from "@/assets/paper/exam.png";
import paperIcon from "@/assets/paper/paper.png";
import proposalIcon from "@/assets/paper/proposal.png";
import teachIcon from "@/assets/paper/teach.png";
import technicalIcon from "@/assets/paper/technical.png";
import practicalIcon from "@/assets/paper/practical.png";
import lowerPaperIcon from "@/assets/paper/lower-paper.png";
import aigcCheckIcon from "@/assets/paper/aigc-check.png";
import lowerAIGCIcon from "@/assets/paper/lower-aigc.png";
import summaryIcon from "@/assets/paper/summary.png";
import { useRoute, useRouter } from 'vue-router';
import { ref } from 'vue';
import { NDivider, NPopover } from 'naive-ui';
import SvgLoader from "@/components/svgLoader/index.vue";

const router = useRouter()
const route = useRoute()
interface NavItem {
	icon: string;
	key: string;
	title: string;
	mTitle: string;
	url?: string;
	tag?: string;
	external?: boolean;
	list?: NavItem[];
	hide?: () => boolean;
	expand?: boolean;
}
type newChildItem = {
	title: string;
	key?: string;
	path?: string;
	list?: newChildItem[];
	type?: string;
	icon?: string;
	description?: string;
	isHot?: boolean;
};
type newNavItem = NavItem & {
	deep?: number;
	childList?: newChildItem[];
	path?: string;
};
const navList = ref<newNavItem[]>([
	{
		icon: "paper1",
		key: "paper1",
		title: "论文助手",
		mTitle: "论文",
		url: "/paper",
		tag: "hot",
		expand: true,
		deep: 1,
		childList: [
			{
				title: "通关锦囊",
				type: "paper",
				list: [
					{
						title: "论文助手",
						description: "万字文稿",
						path: "/paper?id=1",
						key: "paper",
						icon: paperIcon,
					},
					{
						title: "职称范文",
						description: "助你快速完成职称评定",
						path: "/paper?id=8",
						key: "technical",
						icon: technicalIcon,
					},
					{
						title: "期末范文",
						description: "学期期末轻松过",
						path: "/paper?id=4",
						key: "exam",
						icon: examIcon,
					},
					{
						title: "课程范文",
						description: "轻松拿高分，作业不愁",
						path: "/paper?id=3",
						key: "course",
						icon: courseIcon,
					},
					{
						title: "实训报告",
						description: "智能总结实践报告",
						path: "/paper?id=7",
						key: "practical",
						icon: practicalIcon,
					},
					{
						title: "职业生涯规划书",
						description: "全面洞悉，精准规划未来",
						path: "/paper?id=9",
						key: "career",
						icon: careerIcon,
					},
					{
						title: "成教专升本范文",
						description: "助你顺利通关升级",
						path: "/paper?id=5",
						key: "adult",
						icon: adultIcon,
					},

					{
						title: "教学设计",
						description: "高质教学方案的备课助手",
						path: "/paper?id=6",
						key: "teach",
						icon: teachIcon,
					},
				],
			},
		],
	},
	{
		icon: "paperStart",
		key: "paper2",
		title: "论文开题",
		mTitle: "开题",
		url: "/paper",
		// tag: "hot",
		expand: true,
		deep: 1,
		childList: [
			{
				title: "论文开题",
				type: "paper",
				list: [
					{
						title: "开题报告",
						description: "一键解锁研究方向",
						path: "/paper?id=2",
						key: "proposal",
						icon: proposalIcon,
					},
					{
						title: "任务书",
						description: "论文进度全流程解析",
						path: "/paper?id=10",
						key: "books",
						icon: booksIcon,
					},

					{
						title: "文献综述",
						description: "洞悉学术前沿动态",
						path: "/paper?id=13",
						key: "summary",
						icon: summaryIcon,
					},

				],
			},

		],
	},
	{
		title: "论文查重",
		mTitle: "查重",
		tag: "hot",
		url: "/plagiarism-check",
		key: "plagiarism-check",
		icon: 'paperCheck',
	},
	{
		title: "论文降重",
		mTitle: "降重",
		url: "/lower-paper-similarity-rate",
		key: "lower-paper-similarity-rate",
		icon: 'paperDown',
	},
	{
		title: "AIGC检测",
		mTitle: "检测",
		url: "/lower-aigc-rate?type=aigc",
		key: "aigc-check",
		icon: 'aigcCheck',
	},
	{
		title: "降AIGC",
		mTitle: "降AIGC",
		tag: "hot",
		url: "/lower-aigc-rate?type=aigcAmend",
		key: "lower-aigc-check",
		icon: 'aigcDown',
	},
]);

const handleRoute = (obj: NavItem) => {
	if (!obj.url) return;
	router.push(obj.url);
};
const handlePanelJump = (path: string) => {
	router.push(path);
};
</script>


<style scoped>
.tag {
	display: inline-block;
	font-size: 8px;
	color: white;
	font-weight: bold;
	padding: 0 3px;
	border-top-left-radius: 17px;
	border-top-right-radius: 17px;
	border-bottom-right-radius: 17px;
	border-bottom-left-radius: 0px;
	height: 15px;
	line-height: 15px;
	position: absolute;
	top: -10px;
	left: 50px;
}

.hot-tag {
	background: linear-gradient(to left, #ff9a47, #ff582a);
}
</style>
