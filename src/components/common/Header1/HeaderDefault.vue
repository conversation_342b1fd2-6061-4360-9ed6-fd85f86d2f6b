<script lang="ts" setup>
import { ref, onMounted, provide } from "vue";
import { NImage, NPopover, NDivider } from "naive-ui";
import { useRoute, useRouter } from "vue-router";
import SvgLoader from "@/components/svgLoader/index.vue";
import Permission from "@/views/components/Permission.vue";
import Setting from "@/components/common/Setting/index.vue";
import {
	getActivity,
	getActivityImageShow,
	setActivityImageShow,
} from "@/store/modules/auth/helper";
import { useAuthStore, useChatStore, useUserStore } from "@/store";
import logoImage from "@/assets/aiwork/logo.png";
import wechatOfficialImage from "@/assets/aiwork/wechat-official.png";
import customerImage from "@/assets/aiwork/customer.png";
import MemberSvg from "@/assets/aiwork/svg/member.svg";
import AppSvg from "@/assets/aiwork/svg/app.svg";

// import NewRecharge from "@/views/components/Recharge/index.vue";
import NewRecharge from "@/views/components/NewRecharge/index.vue";
import ActivityImg from "@/views/components/ActivityImg.vue";
import { AiworkType, BaseRechargeDetail, RechargeDetailType } from "@/typings";
import CustomerServiceImg from "@/assets/images/cm.png";
import HeaderUser from "@/components/common/HeaderUser/index.vue";
import SelectTeam1 from "../SelectTeam/SelectTeam1.vue";
import { textTransform } from "@/utils/textTransform";
// 导入论文助手相关图标
import adultIcon from "@/assets/paper/adult.png";
import booksIcon from "@/assets/paper/books.png";
import careerIcon from "@/assets/paper/career.png";
import checkIcon from "@/assets/paper/check.png";
import courseIcon from "@/assets/paper/course.png";
import examIcon from "@/assets/paper/exam.png";
import paperIcon from "@/assets/paper/paper.png";
import proposalIcon from "@/assets/paper/proposal.png";
import teachIcon from "@/assets/paper/teach.png";
import technicalIcon from "@/assets/paper/technical.png";
import practicalIcon from "@/assets/paper/practical.png";
import lowerPaperIcon from "@/assets/paper/lower-paper.png";
import aigcCheckIcon from "@/assets/paper/aigc-check.png";
import lowerAIGCIcon from "@/assets/paper/lower-aigc.png";
import summaryIcon from "@/assets/paper/summary.png";

interface NavItem {
	icon: string;
	key: string;
	title: string;
	mTitle: string;
	url?: string;
	tag?: string;
	external?: boolean;
	list?: NavItem[];
	hide?: () => boolean;
	expand?: boolean;
}

interface OfficeItem {
	title: string;
	path: string;
}
interface OfficeList {
	title: string;
	list: OfficeItem[];
}

const userStore = useUserStore();

const router = useRouter();
const route = useRoute();
const chatStore = useChatStore();
const needBind = ref(false);
const show = ref(false);

const system = ref("");
const canDownload = ref(true);
const activityRef = ref<any>();
const activityModalVisible = ref(false);
// 选择个人/团队 或者切换团队时执行的函数
provide("changeTeamProvide", () => {
	const _path = route.path;
	// 团队版
	if (userStore.curTeam) {
		// 成员没有访问团队页面的权限
		if (
			_path.startsWith("/profile") &&
			userStore.curTeam?.TeamsUsers?.role !== "member"
		)
			router.replace("/team/setting");
		else if (_path.startsWith("/team/agent")) router.go(0);
		else if (userStore.curTeam?.TeamsUsers?.role !== "member")
			router.replace("/team/agent");
		else router.go(0);
	}
	// 个人版
	else {
		router.go(0);
	}
});

onMounted(() => {
	const activity = getActivity();
	const activityImgShow = getActivityImageShow();
	if (activity?.title) {
		activityRef.value = activity;
	}
	activityModalVisible.value = activityImgShow;
});

const handleUpdateVisible = (visible: boolean) => {
	activityModalVisible.value = visible;
	setActivityImageShow(visible);
};

const handleOpenRecharge = () => {
	window.$aiwork.openRecharge?.({ type: "ai" });
};

window.$aiwork.openActivity = async () => {
	activityModalVisible.value = true;
};
type newChildItem = {
	title: string;
	key?: string;
	path?: string;
	list?: newChildItem[];
	type?: string;
	icon?: string;
	description?: string;
	isHot?: boolean;
};
type newNavItem = NavItem & {
	deep?: number;
	childList?: newChildItem[];
	path?: string;
};
const navList = ref<newNavItem[]>([
	{
		icon: "create",
		key: "create",
		title: "AI 创作",
		mTitle: "创作",
		// url: "/create",
		url: "/apps",
		// tag: "new",
	},
	{
		icon: "paper",
		key: "paper",
		title: "论文助手",
		mTitle: "论文",
		url: "/paper",
		tag: "hot",
		expand: true,
		deep: 1,
		childList: [
			{
				title: "通关锦囊",
				type: "paper",
				list: [
					{
						title: "论文助手",
						description: "万字文稿",
						path: "/paper?id=1",
						key: "paper",
						icon: paperIcon,
					},
					{
						title: "开题报告",
						description: "一键解锁研究方向",
						path: "/paper?id=2",
						key: "proposal",
						icon: proposalIcon,
					},
					{
						title: "任务书",
						description: "论文进度全流程解析",
						path: "/paper?id=10",
						key: "books",
						icon: booksIcon,
					},
					{
						title: "成教专升本范文",
						description: "助你顺利通关升级",
						path: "/paper?id=5",
						key: "adult",
						icon: adultIcon,
					},
					{
						title: "文献综述",
						description: "洞悉学术前沿动态",
						path: "/paper?id=13",
						key: "summary",
						icon: summaryIcon,
					},
					{
						title: "论文查重",
						description: "官方授权，助你轻松过关",
						path: "/plagiarism-check",
						key: "plagiarism-check",
						icon: checkIcon,
						isHot: true,
					},
					{
						title: "论文降重",
						description: "助你顺利通过查重检测",
						path: "/lower-paper-similarity-rate",
						key: "lower-paper-similarity-rate",
						icon: lowerPaperIcon,
						isHot: true,
					},
					{
						title: "AIGC检测",
						description: "专业的AIGC检测平台",
						path: "/lower-aigc-rate?type=aigc",
						key: "aigc-check",
						icon: aigcCheckIcon,
						isHot: true,
					},
					{
						title: "降AIGC",
						description: " 一键解锁优质原创文章",
						path: "/lower-aigc-rate?type=aigcAmend",
						key: "lower-aigc-check",
						icon: lowerAIGCIcon,
						isHot: true,
					},
				],
			},
			{
				title: "期末轻松过级",
				type: "paper",
				list: [
					{
						title: "期末范文",
						description: "学期期末轻松过",
						path: "/paper?id=4",
						key: "exam",
						icon: examIcon,
					},
					{
						title: "课程范文",
						description: "轻松拿高分，作业不愁",
						path: "/paper?id=3",
						key: "course",
						icon: courseIcon,
					},
					{
						title: "实训报告",
						description: "智能总结实践报告",
						path: "/paper?id=7",
						key: "practical",
						icon: practicalIcon,
					},
					{
						title: "职业生涯规划书",
						description: "全面洞悉，精准规划未来",
						path: "/paper?id=9",
						key: "career",
						icon: careerIcon,
					},
				],
			},
			{
				title: "学术进阶必备",
				type: "paper",
				list: [
					{
						title: "职称范文",
						description: "助你快速完成职称评定",
						path: "/paper?id=8",
						key: "technical",
						icon: technicalIcon,
					},
					{
						title: "教学设计",
						description: "高质教学方案的备课助手",
						path: "/paper?id=6",
						key: "teach",
						icon: teachIcon,
					},
				],
			},
		],
	},
	{
		icon: "chat",
		key: "chat",
		title: "AI 聊天",
		mTitle: "聊天",
		url: "/chat?deepseek=true",
	},
	{
		icon: "ppt",
		key: "ppt",
		title: "AI PPT",
		mTitle: "PPT",
		url: "/ppt",
	},
	// {
	// 	icon: "draw",
	// 	key: "painting",
	// 	title: "AI 绘图",
	// 	mTitle: "绘图",
	// 	tag: "new",
	// 	url: "/painting/square",
	// },
	// {
	// 	icon: "training",
	// 	key: "team",
	// 	title: "AI 训练",
	// 	mTitle: "训练",
	// 	tag: "new",
	// 	url: "/team/agent",
	// 	// hide: () => !(userStore.curTeam && (userStore.curTeam.TeamsUsers.role === "superadmin" || userStore.curTeam.TeamsUsers.role === "admin"))
	// },
	{
		icon: "exchange",
		key: "agent",
		title: "AI 办公助手",
		mTitle: "办公助手",
		url: "/office",
		expand: true,
		childList: [
			{
				title: "PDF工具",
				type: "group",
				list: [
					{
						title: "PDF转Word",
						path: "/pdf2word",
					},
					{
						title: "PDF转Excel",
						path: "/pdf2excel",
					},
					{
						title: "PDF转PPT",
						path: "/pdf2ppt",
					},
					{
						title: "PDF合并",
						path: "/pdfmerge",
					},
					{
						title: "PDF拆分",
						path: "/pdfsplit",
					},
				],
			},
			{
				title: "Office工具",
				type: "group",
				list: [
					{
						title: "Word转PDF",
						path: "/word2pdf",
					},
					{
						title: "Excel转PDF",
						path: "/excel2pdf",
					},
					{
						title: "PPT转PDF",
						path: "/ppt2pdf",
					},
				],
			},
			{
				title: "图片工具",
				type: "group",
				list: [
					{
						title: "图片转Word",
						path: "/img2word",
					},
					{
						title: "图片转Excel",
						path: "/img2excel",
					},
					{
						title: "图片转PPT",
						path: "/img2ppt",
					},
					{
						title: "图片转PDF",
						path: "/img2pdf",
					},
					{
						title: "图片压缩",
						path: "/imgcompress",
					},
				],
			},
			{
				title: "AI工具",
				type: "group",
				list: [
					{
						title: "文档翻译",
						path: "/doc2translate",
					},
					{
						title: "找回密码",
						path: "/findpassword",
					},
				],
			},
		],
	},
	// {
	// 	icon: "resume",
	// 	key: "resume",
	// 	title: "AI 简历",
	// 	// url: "/resume",
	// },
	// {
	// 	icon: "draw",
	// 	key: "draw",
	// 	title: "AI 绘图/视频",
	// 	// url: "/draw",
	// 	// url: "/mj",
	// },
]);

// const officeList = ref<OfficeList[]>([
// 	{
// 		title: "PDF工具",
// 		// PDF->word PDF->Excel PDF->PPT PDF MERGE PDF SPLIT
// 		list: [
// 			{
// 				title: "PDF转Word",
// 				path: "/pdf2word",
// 			},
// 			{
// 				title: "PDF转Excel",
// 				path: "/pdf2excel",
// 			},
// 			{
// 				title: "PDF转PPT",
// 				path: "/pdf2ppt",
// 			},
// 			{
// 				title: "PDF合并",
// 				path: "/pdfmerge",
// 			},
// 			{
// 				title: "PDF拆分",
// 				path: "/pdfsplit",
// 			},
// 		],
// 	},
// 	{
// 		title: "Office工具",
// 		// word->pdf excel->pdf ppt->pdf
// 		list: [
// 			{
// 				title: "Word转PDF",
// 				path: "/word2pdf",
// 			},
// 			{
// 				title: "Excel转PDF",
// 				path: "/excel2pdf",
// 			},
// 			{
// 				title: "PPT转PDF",
// 				path: "/ppt2pdf",
// 			},
// 		],
// 	},
// 	{
// 		title: "图片工具",
// 		// 图片转文字 图片转Excel 图片转PDF 图片压缩
// 		list: [
// 			{
// 				title: "图片转文字",
// 				path: "/img2word",
// 			},
// 			{
// 				title: "图片转Excel",
// 				path: "/img2excel",
// 			},
// 			{
// 				title: "图片转PDF",
// 				path: "/img2pdf",
// 			},
// 			{
// 				title: "图片压缩",
// 				path: "/imgcompress",
// 			},
// 		],
// 	},
// 	{
// 		title: "视频工具",
// 		// 视频转GIF
// 		list: [
// 			{
// 				title: "视频转GIF",
// 				path: "/videotogif",
// 			},
// 		],
// 	},
// 	{
// 		title: "AI工具",
// 		// 文档翻译 找回密码
// 		list: [
// 			{
// 				title: "文档翻译",
// 				path: "/doc2translate",
// 			},
// 			{
// 				title: "找回密码",
// 				path: "/findpassword",
// 			},
// 		],
// 	},
// ]);

// const infoBarList = ["member", "wechatOfficial", "help", "customer", "login"];
const infoBarList = ["help", "customer", "app", "member", "login"];
const handlePanelJump = (path: string) => {
	router.push(path);
};
const handleRoute = (obj: NavItem) => {
	if (!obj.url) return;
	if (obj.url === "/chat") {
		const chats: any = chatStore.history.filter(
			(item) => !item.type || item.type == "chat"
		);
		if (chats && chats?.length === 0) {
			chatStore.addHistory({
				title: "新建会话",
				uuid: 1002,
				isEdit: false,
				type: "chat",
				createTime: new Date().getTime(),
			});
		}
	}

	if (obj.url === "/mj") {
		const chats: any = chatStore.history.filter((item) => item.type == "mj");
		if (chats && chats?.length === 0) {
			chatStore.addHistory({
				title: "新建绘图",
				uuid: 1001,
				type: "mj",
				isEdit: false,
				createTime: new Date().getTime(),
			});
		}
	}
	// const urlObj = new URL(window.location.href);
	// router.push(path + urlObj.search);
	router.push(obj.url);
};
const handleBecomeMember = () => {
	let categoryId = 1;
	console.log("route.name: ", route.name);

	switch (route.name) {
		case "PPT":
		case "PPTGenerate":
			categoryId = 2;
			break;
		case "Paper":
			categoryId = 3;
			break;
		case "Text2Img":
		case "Img2Img":
		case "square":
		case "":
		case "FaceSwapper":
		case "shorten":
			categoryId = 4;
			break;
		default:
			categoryId = 1;
			break;
	}
	// @ts-ignore
	window.$aiwork?.openRecharge?.({ type: ['Paper2', 'Paper', 'Paper2Confirm', 'Paper2History'].includes(route.name) ? "paper" : "pro", categoryId });
};

const openUrl = (url, target = "_blank") => window.open(url, target);
onMounted(async () => {
	const userAgent = window.navigator.userAgent;
	if (userAgent.indexOf("Windows") !== -1 || userAgent.indexOf("Win") !== -1) {
		system.value = "windows";
	} else if (
		userAgent.indexOf("Macintosh") !== -1 ||
		userAgent.indexOf("Mac") !== -1
	) {
		system.value = "mac";
	} else if (userAgent.indexOf("Linux") !== -1) {
		system.value = "linux";
	}

	if (userAgent.indexOf("Aiwork365") > -1) {
		canDownload.value = false;
	} else {
		canDownload.value = true;
	}
});

const renderDownLink = () => {
	const prefix =
		"https://cdn2.weimob.com/static/aiwork365-web-stc/release/latest/Aiwork365";
	const versionSign = String(new Date().getTime());
	if (system.value === "mac") {
		return `${prefix}.dmg?v=${versionSign}`;
	} else if (system.value == "windows") {
		return `${prefix}_x64.msi?v=${versionSign}`;
	} else if (system.value == "linux") {
		return `${prefix}_x86_64.deb?v=${versionSign}`;
	}
};
</script>

<template>
	<header id="header"
		class="fixed top-0 sm:relative ipad:relative z-50 w-full flex-none text-sm leading-6 backdrop-blur duration-500 transition-colors shadow-shadow1 h-[64px] flex items-center sm:items-start ipad:items-start"
		style="
			box-shadow: 0px 0px 10px 0px rgba(226, 226, 226, 0.5);
			background-color: rgba(255, 255, 255, 0.5);
			backdrop-filter: blur(10px);
		">
		<nav class="mx-auto relative flex-1 h-full">
			<div id="menu-wrapper"
				class="relative flex items-center justify-between min-w-0 overflow-hidden py-[8px] sm:py-0 ipad:py-0 h-full max-h-[64px] sm:items-start ipad:items-start">
				<input type="checkbox" class="hidden" id="menu-toggle" />
				<div class="flex max-w-[100vw] w-full sm:justify-between ipad:justify-between">
					<!-- logo -->
					<a href="/"
						class="flex items-center w-[250px] overflow-hidden justify-center cursor-pointer sm:w-[160px] ipad:w-[160px] sm:h-[64px] ipad:h-[64px] sm:relative ipad:relative">
						<!-- <NImage :src="logoImage" class="!h-[26px] mt-[3px]" preview-disabled height="26px" /> -->
						<img :src="logoImage" class="!h-[26px] mt-[3px]" />
						<!-- <label for="menu-toggle" class="hidden sm:block ipad:block menu-toggle-btn absolute right-4 top-1/2 -translate-y-1/2 text-[#444]">
							<MenuSvg id="menu-icon-hide" class="w-[16px] h-[16px]" />
							<MenuCloseSvg id="menu-icon-show" class="w-[16px] h-[16px]" />
						</label> -->
					</a>
					<!-- navbar -->
					<div class="flex flex-1 items-center text-[14px] text-[#444] gap-[20px] sm:hidden ipad:hidden">
						<template v-for="(item, index) in navList" :key="index">
							<a v-if="(!item.hide || item.hide() === false) && !item.expand" @click="handleRoute(item)"
								class="relative flex items-center hover:text-[#0E69FF] sm:text-sm text-[14px] cursor-pointer grow-0 shrink-0 flex-nowrap gap-[3px] px-[5px] sm:px-0"
								:class="{ 'text-[#0E69FF]': item.key === route.meta.navKey }">
								<SvgLoader :name="item.icon" class="w-[16px] h-[16px]" />
								<!-- <span>{{ textTransform(item.title, "论文", "学术") }}</span> -->
								<span v-replace>{{ item.title }}</span>
								<div v-if="item.tag" :class="['nav-badge']">
									<div :class="item.tag"></div>
								</div>
							</a>
							<n-popover v-else trigger="hover" :placement="'bottom'" :show-arrow="false">
								<template #trigger>
									<a @click="handleRoute(item)"
										class="relative flex items-center justify-center hover:text-[#0E69FF] sm:text-sm text-[14px] cursor-pointer grow-0 shrink-0 flex-nowrap gap-[3px] px-[5px] sm:px-0"
										:class="{
											'text-[#0E69FF]': item.key === route.meta.navKey,
										}">
										<SvgLoader :name="item.icon" class="w-[16px] h-[16px]" />
										<span v-replace>{{ item.title }}</span>
										<div v-if="item.tag" :class="['nav-badge']">
											<div :class="item.tag"></div>
										</div>
										<IconDown class="text-black pt-1" />
									</a>
								</template>
								<div class="panel-container max-w-[1007px] max-h-[418px] flex flex-col gap-y-[12px] py-[14px] px-[10px]"
									:class="{
										'px-[24px] py-[27px] gap-y-[30px]': item?.deep !== 1,
									}">
									<div v-if="item.key === 'paper'" class="flex flex-col">
										<div v-for="(i, index) in item?.childList" :key="index">
											<div class="text-[#3D3D3D] text-[12px] mb-[11px]" v-replace>
												{{ i.title }}
											</div>
											<div class="grid grid-cols-5 gap-x-[12px]">
												<div v-for="(j, jIndex) in i.list" :key="jIndex"
													class="flex flex-row items-center gap-x-[14px] bg-[#fff] hover:bg-[#F6F8F9] hover:rounded-[8px] cursor-pointer py-[9px]"
													@click="handlePanelJump(j.path!)">
													<div class="flex items-center justify-start mb-1 relative">
														<img :src="j.icon" class="w-[40px] h-[40px]" alt="" />
													</div>
													<div class="relative">
														<div class="text-[12px] text-[#3D3D3D] text-left" v-replace>
															{{ j.title }}
														</div>
														<div v-if="j.isHot" class="hot-tag tag">HOT</div>
														<div class="text-[12px] text-[#A1A1A1] text-left" v-replace>
															{{ j.description }}
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
									<div v-else v-for="(i, index) in item?.childList" :key="index"
										class="flex flex-row gap-x-[19px] items-center">
										<template v-if="i.type === 'item'">
											<div class="flex items-center hover:text-[#0E69FF] cursor-pointer">
												<div
													class="w-[40px] h-[40px] bg-[#F5F6F7] rounded-[5px] flex items-center justify-center mr-[10px]">
													<SvgLoader :name="i.icon || 'paper'" class="w-[18px] h-[22px]" />
												</div>
												<div class="flex flex-col">
													<span class="text-[14px] text-[#000000] font-[600]" v-replace>{{ i.title }}</span>
													<span class="text-[12px] text-[#A1A1A1]" v-replace>{{
														i.description
													}}</span>
												</div>
											</div>
										</template>
										<template v-else-if="i.type === 'group'">
											<span class="inline-flex w-[78px] text-[#000000] font-[600]">{{ i.title }}</span>
											<n-divider :vertical="true" style="--color: #d8d8d8" />
											<div v-for="(j, index) in i.list" :key="index" class="panel-item-list text-[#3D3D3D]">
												<a :href="'/office' + j.path" target="_blank" class="hover:text-[#0E69FF]" v-replace>{{ j.title
													}}</a>
											</div>
										</template>
									</div>
								</div>
							</n-popover>
						</template>
					</div>
					<div
						class="flex justify-end items-center gap-[20px] text-[14px] leading-none pr-[40px] sm:gap-[16px] sm:pr-[16px]">
						<template v-for="(item, index) in infoBarList" :key="index">
							<div v-if="item === 'member'" class="member-btn relative" @click="handleBecomeMember">
								<div v-if="activityRef?.title" class="absolute top-[-9px] right-[-6px] activity-tag">
									{{ activityRef?.title }}
								</div>
								<MemberSvg class="w-[16px] h-[16px]" />
								成为会员
							</div>
							<NPopover v-if="item === 'wechatOfficial'" trigger="hover" :placement="'bottom-end'" :show-arrow="false"
								class="sm:hidden ipad:hidden">
								<template #trigger>
									<div class="flex items-center justify-center gap-[2px] cursor-pointer">
										<img :src="wechatOfficialImage" class="w-[20px] h-[20px]" />
										<span>公众号</span>
									</div>
								</template>
								<NImage width="160" src="https://cdn.aiwork365.cn/common/kefu.jpg" />
							</NPopover>
							<NPopover v-if="item === 'customer'" trigger="hover" :placement="'bottom-end'" :show-arrow="false">
								<template #trigger>
									<div class="flex items-center justify-center gap-[2px] cursor-pointer sm:hidden ipad:hidden">
										<img :src="customerImage" class="w-[20px] h-[20px]" />
										<span>客服</span>
									</div>
								</template>
								<NImage width="160" :src="CustomerServiceImg" />
							</NPopover>
							<a :href="renderDownLink()" v-if="item === 'app' && canDownload" target="_blank"
								class="flex items-center justify-center gap-[2px] cursor-pointer sm:hidden ipad:hidden">
								<!-- <i class="fi fi-rs-cloud-download text-[18px]"></i> -->
								<AppSvg class="w-[14px] h-[14px]" />
								<span class="ml-[2px]">客户端</span>
							</a>
							<template v-if="item === 'login'">
								<HeaderUser />
							</template>
						</template>
					</div>
				</div>
				<label id="menu-mask" for="menu-toggle"
					class="hidden absolute left-0 top-0 w-full h-[100vh] bg-black bg-opacity-50 z-[-1]"></label>
			</div>
		</nav>
	</header>

	<!-- mobile bottom navbar -->
	<div
		class="hidden sm:flex ipad:flex items-center text-[14px] text-[#444] fixed left-0 bottom-0 w-full h-[50px] z-50 bg-white shadow-dropdown">
		<template v-for="(item, index) in navList" :key="index">
			<a @click="handleRoute(item)"
				class="relative flex items-center justify-center flex-1 hover:text-[#0E69FF] sm:text-sm text-[14px] cursor-pointer flex-nowrap px-[5px] sm:px-0 gap-1"
				:class="{
					'text-[#0E69FF]': item.key === route.meta.navKey,
					'border-l-[1px] border-l-[#EBEBEB]': index !== 0,
				}">
				<SvgLoader :name="item.icon" class="w-[16px] h-[16px]" />
				<span>{{ item.mTitle }}</span>
				<!-- <n-icon>
					<IconDown v-if="item.expand" class="w-[16px] h-[16px] text-[#020202]" />
				</n-icon> -->

				<!-- <div v-if="item.tag" :class="['nav-badge']">
					<div :class="item.tag"></div>
				</div> -->
			</a>
			<!-- <a v-else
				class="relative flex items-center justify-center flex-1 hover:text-[#0E69FF] sm:text-sm text-[14px] cursor-pointer flex-nowrap px-[5px] sm:px-0 gap-1"
				:class="{
					'text-[#0E69FF]': item.key === route.meta.navKey,
					'border-l-[1px] border-l-[#EBEBEB]': index !== 0,
				}">
				<SvgLoader :name="item.icon" class="w-[16px] h-[16px]" />
				<span>{{ item.mTitle }}</span>
				<IconDown class="w-[16px] h-[16px] text-[#020202]" />
			</a> -->
		</template>
	</div>
	<!-- <Member v-if="showMember" v-model:visible="showMember" @change-fn="changeFn" /> -->
	<Setting v-if="show" v-model:visible="show" />
	<!-- <BindAccount v-if="needBind" v-model:visible="needBind" @login="handleLogin" /> -->
	<Permission />
	<NewRecharge />
	<ActivityImg :visible="activityModalVisible" :img-src="activityRef?.url" @update-visible="handleUpdateVisible"
		@open-recharge="handleOpenRecharge" />
	<SelectTeam1 />
</template>

<style lang="less" scoped>
@import "./index.less";

#header {
	#menu-wrapper {
		transition: max-height 0.3s ease-in-out;
		max-height: 64px;
	}

	#menu-icon-hide {
		display: block;
	}

	#menu-icon-show {
		display: none;
	}

	&:has(#menu-toggle:checked) {
		#menu-wrapper {
			max-height: 100vh;
			height: auto;
			background-color: white;
			padding-bottom: 24px;
			box-shadow: rgba(226, 226, 226, 0.5) 0px 0px 10px 0px;
			overflow: visible;
		}

		#menu-mask {
			display: block;
		}

		#menu-icon-hide {
			display: none;
		}

		#menu-icon-show {
			display: block;
		}
	}
}

.tag {
	display: inline-block;
	font-size: 8px;
	color: white;
	font-weight: bold;
	padding: 0 3px;
	border-top-left-radius: 17px;
	border-top-right-radius: 17px;
	border-bottom-right-radius: 17px;
	border-bottom-left-radius: 0px;
	height: 15px;
	line-height: 15px;
	position: absolute;
	top: -10px;
	left: 50px;
}

.hot-tag {
	background: linear-gradient(to left, #ff9a47, #ff582a);
}
</style>
