<template>
	<HeaderPaper v-if="navigationType === 'paper'" />
	<HeaderDefault v-else />
</template>

<script lang="ts" setup>
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
import HeaderDefault from './HeaderDefault.vue';
import HeaderPaper from './HeaderPaper.vue';

const route = useRoute();
const navigationType = ref(route.query.navigation_type as string || 'default');

onMounted(() => {
	if(route.query.navigation_type) sessionStorage.setItem('navigation_type', route.query.navigation_type as string)
	else {
		const type = sessionStorage.getItem('navigation_type')
		if(type) navigationType.value = type
	}
})
</script>
