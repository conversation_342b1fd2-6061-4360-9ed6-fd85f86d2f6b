<script lang="ts" setup>
import type { CSSProperties } from 'vue'
import { ref,onMounted,computed } from 'vue'
import {
	NImage,
	NPopover,
	NMenu,
	NAvatar,
	NBadge,
	NDropdown,
	MenuOption
} from 'naive-ui'
import { useRoute, useRouter } from 'vue-router'
import { Home as IconHome, Message as IconMessage } from '@icon-park/vue-next'
import BindAccount from '@/views/components/BindAccount.vue'
import Permission from '@/views/components/Permission.vue'
import Setting from '@/components/common/Setting/index.vue'
import { Member } from '@/components/common'
import { getUser } from '@/store/modules/auth/helper'
import { useChatStore } from '@/store'
import vip_angle from '@/assets/images/vip_angle_mark.png'
// import kf from '@/assets/kefu.png'
const defaultAvatar = "https://cdn2.weimob.com/static/aiwork365-web-stc/home/<USER>"
import payImg from '@/assets/vp1.png'
const route = useRoute()
const router = useRouter()
const chatStore = useChatStore()

const pathname = ref(route.name)
const current = ref(1)
const needBind = ref(false)
const needPermission = ref(false)
const isLogin = ref(false)
const activeKey = ref()
const show = ref(false)
const showMember = ref(false)
const system = ref('')
const isDownload = ref(true)
const isHidden = computed(()=> {
	return false
})

const user = getUser()
const menuOptions: MenuOption[] = [
	{
		label: user?.phone,
		key: 'dance-dance-dance',
		children: [
			{
				label: '个人信息',
				key: 'profile'
			},
			{
				label: '账号绑定',
				key: 'bind'
			},
			{
				label: '退出登录',
				key: 'logout'
			}
		]
	}
]

if (user) {
	isLogin.value = user?.id
}

onMounted(async() => {
	const userAgent = window.navigator.userAgent;
	if (userAgent.indexOf("Windows") !== -1 || userAgent.indexOf("Win") !== -1) {
		system.value = "windows";
	} else if (userAgent.indexOf("Macintosh") !== -1 ||userAgent.indexOf("Mac") !== -1 ) {
		system.value = "mac";
	} else if(userAgent.indexOf("Linux") !== -1) {
		system.value = "linux";
	} else if(userAgent.indexOf('chatrobot')!== -1) {
		system.value = 'application' // 桌面软件
	}

	if(userAgent.indexOf('chatrobot')>-1) {
		isDownload.value = false
	} else {
		isDownload.value = true
	}

})

const renderDownLink = () => {
	if(system.value==='mac') {
		return 'https://cdn.aiwork365.cn/software/v3.0/ChatRobot.dmg'
	} else if(system.value =='windows') {
		return 'https://cdn.aiwork365.cn/software/v3.0/ChatRobot_x64.msi'
	} else if(system.value == 'linux'){
		return 'https://cdn.aiwork365.cn/software/v3.0/ChatRobot_x86_64.deb'
	}
}

const handleBind = () => {
	needBind.value = true
}
const handleLogin = () => {
	needPermission.value = true
}
const changeFn = async () => {
	needBind.value = true
}

const handleMember = () => {
	showMember.value = true
}

const onChange = (key: string) => {
	if (key === 'logout') {
		window.localStorage.removeItem('SECRET_TOKEN')
		window.location.reload()
	}
	if(key === 'profile') {
		show.value = true
	}
	if(key === 'bind') {
		handleBind()
	}
}
const optionss=ref([
	 	{
          label: 'AI PPT',
          key: "ppt"
        }
])
const handleSelect =(key: string)=> {
	if(!key.includes("/")){
		handleMdRoute(key);
	}else{
		handleRoute(key)
	}

}
const handleRoute = (path: string) => {

	if (path === '/chat') {
		const chats: any = chatStore.history.filter(item=> !item.type || item.type === 'chat')
		if (chats && chats?.length === 0) {
			chatStore.addHistory({
				title: '新建会话',
				uuid: 1002,
				isEdit: false,
				type: 'chat',
				createTime: new Date().getTime()
			})
		}
	}

	if (path === '/mj') {
		const chats: any = chatStore.history.filter(item=> item.type == 'mj')
		if (chats && chats?.length === 0) {
			chatStore.addHistory({
				title: '新建绘图',
				uuid: 1001,
				type: 'mj',
				isEdit: false,
				createTime: new Date().getTime()
			})
		}
	}
  const urlObj = new URL(window.location.href);
	router.push(path + urlObj.search)
}

const handleMdRoute = (type='tweet') => {
	const mapUrl:any = {
		"ppt":"https://ppt.aiwork365.cn"
	}
	if(user.type!=='temp' && user.token) {
		window.open(`${mapUrl[type]}?token=${user.token}`)
	} else {
		window.open(`${mapUrl[type]}`)
	}
}

const toggleMember = () => {
	showMember.value = true
}

const getDownClass = computed<CSSProperties>(() => {
	return {
			backgroundImage: `url(${vip_angle})`,
			backgroundRepeat: 'no-repeat',
			backgroundSize: 'contain'
		}
	return {}
})

</script>

<template>
	<header
		class="fixed top-0 sm:relative z-50 w-full flex-none text-sm leading-6 backdrop-blur duration-500 bg-[#fff] transition-colors shadow-shadow1">
		<nav class="mx-auto sm:px-4 lg:px-4 relative">
			<div class="relative flex items-center justify-between min-w-0 overflow-hidden py-[8px] sm:py-[10px]">
				<div class="flex">
					<a class="flex items-center w-[200px] overflow-hidden" @click="handleRoute('/')">
						<NImage src="https://cdn2.weimob.com/static/aiwork365-web-stc/home/<USER>" width="36" height="36" class="rounded-[5px]" preview-disabled></NImage>
						<div class="text-[22px] font-bold pl-[10px] sm:text-sm textGradient">
							AIWork365
						</div>
					</a>
					<div class="hidden lg:flex lg:items-center text-[14px] text-[#444]">
						<a class="flex items-center hover:text-[#3ebba2] sm:text-sm text-[14px] cursor-pointer" @click="handleRoute('/')"
							:class="pathname == 'Home' ? 'text-[#3ebba2]' : ''">
							<IconHome class="w-[20px] h-[20px] mr-1" theme="outline" /> 首页
						</a>
						<a class="flex items-center ml-8 hover:text-[#3ebba2] sm:text-sm text-[14px] cursor-pointer"
							:class="pathname == 'Apps' || pathname == 'AppDetail' ? 'text-[#3ebba2]' : ''" @click="handleRoute('/apps')">
							<IconAllApplication class="w-[20px] h-[20px] mr-1"></IconAllApplication>
							AI写作
						</a>
						<a class="flex items-center ml-8 hover:text-[#3ebba2] sm:text-sm text-[14px] cursor-pointer"
							:class="pathname == 'Chat' ? 'text-[#3ebba2]' : ''" @click="handleRoute('/chat')">
							<IconMessage class="w-[20px] h-[20px] mr-1" theme="outline" /> AI聊天
						</a>
						<a  class="flex items-center ml-8 hover:text-[#3ebba2] sm:text-sm text-[14px] cursor-pointer"
							:class="pathname == 'Mj' ? 'text-[#3ebba2]' : ''" @click="handleRoute('/mj')">
							<IconPencil class="w-[20px] h-[20px] mr-1"></IconPencil>AI绘图
						</a>

						<a class="flex items-center ml-8 hover:text-[#3ebba2] sm:text-sm text-[14px] cursor-pointer"
								:class="pathname == 'Paper' ? 'text-[#3ebba2]' : ''" @click="handleRoute('/paper')">
								<IconPencil class="w-[20px] h-[20px] mr-1"></IconPencil>论文助手 <NBadge value="新" color="red"></NBadge>
							</a>

							<a class="flex items-center ml-8 hover:text-[#3ebba2] sm:text-sm text-[14px] cursor-pointer">
								<n-dropdown
									trigger="click"
									:options="optionss"
									:show-arrow="true"
									@select="handleSelect"
								><span>
									<IconAllApplication class="w-[20px] h-[20px] mr-1"></IconAllApplication>更多</span>
								</n-dropdown>
							</a>

					</div>
				</div>
				<div>
					<div class="flex justify-center items-center flex-wrap sm:hidden h-[50px]">
						<div
							class="w-[60px] h-[50px] rounded-[6px] flex justify-center items-center flex-col hover:bg-[#3ebba2]/[0.05] hover:text-[#3ebba2] cursor-pointer"
							:class="current === -1 ? 'bg-[#3ebba2]/[0.05] text-[#3ebba2]' : 'text-[#666]'" @click="handleMember">
							<i class="fi fi-rs-chess-queen mr-1 text-[18px]" />
							<span class="text-[12px]">会员</span>
						</div>

						<n-popover trigger="hover" placement="bottom">
							<template #trigger>
								<div
									class="w-[60px] h-[50px] rounded-[6px] flex justify-center items-center flex-col hover:bg-[#3ebba2]/[0.05] hover:text-[#3ebba2] cursor-pointer"
									:class="current === -1 ? 'bg-[#3ebba2]/[0.05] text-[#3ebba2]' : 'text-[#666]'">
									<i class="fi fi-rr-headset text-[18px]"></i>
									<span class="text-[12px]">客服</span>
								</div>
							</template>
							<n-image width="160" src="https://cdn.aiwork365.cn/common/kefu.jpg" />
						</n-popover>
						<a href="https://data.aiwork365.cn/aiwork/out/doc" target="_blank"
							class="w-[60px] h-[50px] rounded-[6px] flex justify-center items-center flex-col hover:bg-[#3ebba2]/[0.05] hover:text-[#3ebba2] cursor-pointer"
							:class="current === -1 ? 'bg-[#3ebba2]/[0.05] text-[#3ebba2]' : 'text-[#666]'">
							<i class="fi fi-rr-cloud-question text-[18px]"></i>
							<span class="text-[12px]">帮助文档</span>
						</a>
						<a :href="renderDownLink()" v-if="system!=='application' && isDownload && !isHidden" target="_blank"
							class="w-[60px] h-[50px] rounded-[6px] flex justify-center items-center flex-col hover:bg-[#3ebba2]/[0.05] hover:text-[#3ebba2] cursor-pointer relative mr-[70px]"
							:class="current === -1 ? 'bg-[#3ebba2]/[0.05] text-[#3ebba2]' : 'text-[#666]'">
							<i class="fi fi-rs-cloud-download text-[18px]"></i>
							<span class="text-[12px]">客户端</span>
							<div class=" absolute top-1 left-[50px] w-[90px] h-[20px] overflow-hidden">
								<div :style="getDownClass" class="text-[10px] text-[#fff] w-[90px] h-[20px] text-center px-[6px] flex items-center">客户端享9折</div>
							</div>
						</a>
						<a v-if="!isLogin" class="ckButtonNew ckButtonblue cursor-pointer transition duration-300 ml-[20px]"
							@click="handleLogin">登录/注册</a>

						<div class="flex items-center ml-[30px]" v-if="isLogin">
							<div class="overflow-hidden rounded-full shrink-0 mr-[-5px]">
								<NAvatar size="small" round :src="defaultAvatar" />
							</div>
							<n-menu v-model:value="activeKey" class="ml-[-10px] text-[#666]" mode="horizontal" :options="menuOptions"
								@update-value="onChange" />
						</div>
					</div>
					<div >
						<Button @click="toggleMember" class="hidden sm:block">
							<NImage :src="payImg" width="30" height="30" preview-disabled />
						</Button>
					</div>

				</div>
			</div>
		</nav>
	</header>
	<Member v-if="showMember" v-model:visible="showMember" @change-fn="changeFn" />
	<Setting v-if="show" v-model:visible="show" />
	<BindAccount v-if="needBind" v-model:visible="needBind" @login="handleLogin" />
	<Permission v-if="needPermission" v-model:visible="needPermission" />
</template>
