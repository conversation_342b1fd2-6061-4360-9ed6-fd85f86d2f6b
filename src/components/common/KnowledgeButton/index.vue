<template>
  <NPopover
    v-if="!disabled"
    trigger="click"
    @update:show="handlePopoverOpen"
    :show="isPopoverVisible"
    :show-arrow="false"
    placement="bottom-start"
    raw
    class="rounded-[7px] bg-white"
  >
    <template #trigger>
      <NButton
        :disabled="disabled"
        round
        :style="{
          '--n-color': !!selectedKnowledge ? '#DBEAFE' : '#fff',
          '--n-color-focus': !!selectedKnowledge ? '#DBEAFE' : '#fff',
          '--n-text-color': !!selectedKnowledge ? '#4D6BFE' : '#4c4c4c',
          '--n-border': !!selectedKnowledge
            ? '1px solid rgba(0, 122, 255, 0.15)'
            : '1px solid rgba(0, 0, 0, 0.12)',
          '--n-color-hover': !!selectedKnowledge ? '#C3DAF8' : '#E0E4ED',
          '--n-color-pressed': '#DBEAFE',
          '--n-text-color-hover': !!selectedKnowledge ? '#4D6BFE' : '#4c4c4c',
          '--n-text-color-pressed': '#4D6BFE',
          '--n-border-hover': !!selectedKnowledge
            ? '1px solid rgba(0, 122, 255, 0.15)'
            : '1px solid rgba(0, 122, 255, 0.15)',
          '--n-border-pressed': '1px solid rgba(0, 122, 255, 0.15)',
          '--n-border-focus': '1px solid rgba(0, 122, 255, 0.15)',
          '--n-text-color-focus': !!selectedKnowledge ? '#4D6BFE' : '#4c4c4c',
          '--n-width': isMobile ? '70px' : 'auto',
          '--n-padding': isMobile ? '0 6px' : '0 14px',
        }"
      >
        <ChatKnowledgeIcon />
        <span class="mr-[6px] ml-[8px]">我的知识库</span>
        <NIcon>
          <component :is="isPopoverVisible ? Up : Down" />
        </NIcon>
      </NButton>
    </template>

    <div
      class="flex flex-col w-[208px] p-[12px] border-[1px] border-[#D3D3D3] border-solid rounded-[7px]"
    >
      <div
        class="pb-[10px] mb-[10px] border-[#DFDFDF] border-solid border-b-[1px]"
      >
        基于知识库回答
      </div>

      <div
        v-if="!knowledgeList.length"
        class="flex w-[185px] h-[33px] border-[#DFDFDF] border-solid border-[1px] rounded-[2px] pl-[8px]"
        @click="handleJumpToKnowledge"
      >
        <span class="text-[#DFDFDF] text-[18px] rounded-[7px] mr-[6px]">+</span>
        <NButton
          text
          class="flex items-center"
          :style="{ '--n-text-color': '#6D6D6D' }"
        >
          创建知识库
        </NButton>
      </div>

      <NRadioGroup v-else v-model:value="selectedKnowledge" name="radiogroup">
        <NRadio
          v-for="item in knowledgeList"
          :key="item.id"
          :value="item.id"
          class="!leading-[32px]"
          @click="handleRadioClick(item.id)"
        >
          <template #default>
            <div class="flex items-center">
              <div
                v-if="item.id === selectedKnowledge"
                class="text-[#343434] ml-auto"
              >
                <CheckOneIcon />
              </div>
              <div
                v-else
                class="h-[12px] w-[12px] rounded-[12px] border-[1px] border-[#D1D1D1] border-solid"
              ></div>
              <div
                class="flex items-center ml-[6px] pl-[8px] w-[166px]"
                :class="
                  item.id === selectedKnowledge
                    ? 'bg-[#F6F7F9] rounded-[2px]'
                    : ''
                "
              >
                {{ item.name }}
              </div>
            </div>
          </template>
        </NRadio>
      </NRadioGroup>
    </div>
  </NPopover>

  <NTooltip v-else>
    <template #trigger>
      <NButton
        :disabled="disabled"
        round
        :style="{
          '--n-color': !!selectedKnowledge ? '#DBEAFE' : '#fff',
          '--n-color-focus': !!selectedKnowledge ? '#DBEAFE' : '#fff',
          '--n-text-color': !!selectedKnowledge ? '#4D6BFE' : '#4c4c4c',
          '--n-border': !!selectedKnowledge
            ? '1px solid rgba(0, 122, 255, 0.15)'
            : '1px solid rgba(0, 0, 0, 0.12)',
          '--n-color-hover': !!selectedKnowledge ? '#C3DAF8' : '#E0E4ED',
          '--n-color-pressed': '#DBEAFE',
          '--n-text-color-hover': !!selectedKnowledge ? '#4D6BFE' : '#4c4c4c',
          '--n-text-color-pressed': '#4D6BFE',
          '--n-border-hover': !!selectedKnowledge
            ? '1px solid rgba(0, 122, 255, 0.15)'
            : '1px solid rgba(0, 122, 255, 0.15)',
          '--n-border-pressed': '1px solid rgba(0, 122, 255, 0.15)',
          '--n-border-focus': '1px solid rgba(0, 122, 255, 0.15)',
          '--n-text-color-focus': !!selectedKnowledge ? '#4D6BFE' : '#4c4c4c',
          '--n-width': isMobile ? '70px' : 'auto',
          '--n-padding': isMobile ? '0 6px' : '0 14px',
        }"
      >
        <ChatKnowledgeIcon />
        <span class="mr-[6px] ml-[8px]">我的知识库</span>
        <NIcon>
          <component :is="isPopoverVisible ? Up : Down" />
        </NIcon>
      </NButton>
    </template>
    <div>暂不支持知识库功能</div>
  </NTooltip>
</template>

<script lang="ts" setup>
import {
  NText,
  NPopover,
  NButton,
  NIcon,
  NRadio,
  NRadioGroup,
  NTooltip,
} from "naive-ui";
import { h, onMounted, reactive, ref, watch, defineEmits } from "vue";
import { getUser } from "@/store/modules/auth/helper";
import { AiworkType } from "@/typings";
import ChatKnowledgeIcon from "@/assets/chat_knowledge.svg";
import CheckOneIcon from "@/assets/checked.svg";
import AddIcon from "@/assets/aiwork/svg/add.svg";
import { useRouter } from "vue-router";
import { useRequest } from "vue-hooks-plus";
import { getKnowledgeListAll } from "./api";
import { Up, Down } from "@icon-park/vue-next";
import { useBasicLayout } from "@/hooks/useBasicLayout";

const router = useRouter();
const { isMobile } = useBasicLayout();
const selectedKnowledge = ref(null);
const knowledgeList = ref([]);
const isPopoverVisible = ref(false);

interface Props {
  disabled: boolean;
}

const props = defineProps<Props>();

onMounted(() => {
  const u = getUser();
  if (u.type !== "temp") {
    runAsync({});
  }
});

const { loading, error, runAsync } = useRequest(getKnowledgeListAll, {
  manual: true,
  onSuccess: (data: any) => {
    knowledgeList.value = data;
  },
});
const handleJumpToKnowledge = () => {
  router.push("/knowledge");
};

const handleRadioClick = (id: number) => {
  if (selectedKnowledge.value === id) {
    selectedKnowledge.value = null; // 取消选中
    emit("onSelectKnowledge", null);
    return;
  }
  emit("onSelectKnowledge", id);
};

const handlePopoverOpen = (value) => {
  const u = getUser();
  if (u.type === "temp" && value) {
    (window.$aiwork as AiworkType).openLogin?.().then(() => {
      runAsync({}).then(() => {
        isPopoverVisible.value = value;
      });
    });
    return;
  }
  isPopoverVisible.value = value;
};

const emit = defineEmits(["onSelectKnowledge"]);
</script>

<style scoped>
:deep(.n-radio__dot-wrapper) {
  display: none !important;
}

:deep(.n-radio__label) {
  padding-left: 0 !important;
}
</style>