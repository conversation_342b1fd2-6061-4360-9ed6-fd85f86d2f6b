<template>
	<div>
		<div v-if="!isLogin" class="login-btn" @click="handleLogin">登录/注册</div>
		<div class="flex items-center" v-if="isLogin" @click.stop>
			<div class="shrink-0 cursor-pointer relative">
				<NDropdown
					trigger="click"
					:options="userOptions"
					size="huge"
					@click.stop
				>
					<div class="flex items-center gap-[6px]">
						<NAvatar
							:size="36"
							:src="userStore.curTeam?.avatar || userStore.userInfo?.avatar || defaultAvatar"
							@click="showUserInfo = true"
						/>
						<div class="flex flex-col gap-[3px] items-start">
							<span class="text-[14px]">{{
								userStore.curTeam
									? userStore.curTeam.name
									: userStore.userInfo?.name
							}}</span>
							<!-- <NPopover
								:trigger="'click'"
								:show-arrow="false"
								:show="changeTeamPopShow"
							>
								<template #trigger>
									<div
										class="text-[12px] bg-[#A061FF] rounded-[2px] text-white flex nowrap items-center p-1 cursor-pointer"
										:class="{ 'bg-primary': userStore.curTeam }"
										@click.stop="changeTeamPopShow = true"
									>
										<span>{{ userStore.curTeam ? "团队版" : "个人版" }}</span>
										<ChangeSvg class="ml-1 w-[10px] h-[10px]" />
									</div>
								</template>
								<div class="flex flex-col gap-1 max-w-[200px] -mb-2">
									<div
										class="flex gap-[7px] items-center justify-start cursor-pointer"
										@click="handleChangePersonal"
									>
										<NAvatar :src="defaultAvatar" :size="30" class="shrink-0" />
										<NEllipsis>{{ userStore.userInfo?.name }}</NEllipsis>
										<div
											class="text-[12px] bg-[#A061FF] rounded-[2px] text-white px-1 shrink-0"
										>
											个人
										</div>
									</div>
									<NDivider />
									<SelectTeam
										@select="handleChangeTeam"
										:current-id="userStore.curTeam?.id as string"
										class="-mx-[14px]"
									/>
								</div>
							</NPopover> -->
						</div>
					</div>
				</NDropdown>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { onMounted, ref, watch, computed } from "vue";
import {
	ChangeTeam,
	Help,
	UserInfoCenter,
	Distribution,
	UserInfoContact,
	UserInfoHeader,
	UserInfoLogout,
	UserInfoMember,
} from "./render";
import defaultAvatar from "@/assets/avatar.jpg";
import { useUserStore } from "@/store";
import { useRoute, useRouter } from "vue-router";
import request from "@/utils/request";
import {
	NDropdown,
	NAvatar,
	NPopselect,
	NDivider,
	NPopover,
	NButton,
	NEllipsis,
	NModal,
	NFormItem,
	NInput,
	NUpload,
	NForm,
	useMessage,
} from "naive-ui";
import ChangeSvg from "@/assets/aiwork/svg/change.svg";
import SelectTeam from "../SelectTeam/SelectTeam.vue";
import { inject } from "vue";
import { getUser } from "@/store/modules/auth/helper";

// 域名枚举
enum HostnameEnum {
	AIWORK365_NET = "aiwork365.net",
	AIWORK365_QA = "mjmobi.com",
}

const changeTeamProvide = inject<any>("changeTeamProvide");

const showUserInfo = ref(false);
const router = useRouter();
const route = useRoute();
const isLogin = ref(false);
const userStore = useUserStore();
console.log(1111, userStore);
const changeTeamPopShow = ref(false);

const message = useMessage();

// 移除静态的 user 变量，在需要时动态获取
// 判断当前是否为.net域名或qa环境
const isNetOrQaDomain = computed(() => {
	const hostname = window.location.hostname;
	return (
		hostname.indexOf(HostnameEnum.AIWORK365_NET) > -1 ||
		hostname.indexOf(HostnameEnum.AIWORK365_QA) > -1 ||
		hostname.indexOf("localhost") > -1
	);
});

watch(
	() => userStore.userInfo,
	(v) => {
		isLogin.value = !!v?.uid;
	},
	{
		immediate: true,
		deep: true,
	}
);

const userOptions = computed(() => {
	const options = [
		{
			key: "header",
			type: "render",
			render: () => UserInfoHeader({ user: userStore.userInfo }),
		},
		// {
		// 	key: "header-divider",
		// 	type: "divider",
		// },
		{
			// label: '处理群消息 342 条',
			key: "beMember",
			type: "render",
			render: UserInfoMember,
			props: {
				// onClick: () => showMember.value = true,
				onClick: () => {
					window.$aiwork.openRecharge({
						type: "ai",
					});
				},
			},
		},
	];

	// 只有在非.net域名和非qa环境下才显示help菜单项
	if (!isNetOrQaDomain.value) {
		options.push({
			label: Help,
			key: "help",
			props: {
				onClick: () => window.open("https://docs.aiwork365.cn/#/"),
			},
		});
	}

	options.push(
		{
			label: UserInfoCenter,
			key: "user-center",
			props: {
				onClick: () => {
					// 动态获取最新的用户信息
					const currentUser = getUser()
					if(currentUser?.isNewMember) {
						router.push("/profile-new")
					} else {
						router.push("/profile")
					}
				},
			},
		},
		{
			label: Distribution,
			key: "distribution",
			props: {
				onClick: () => router.push("/distribution"),
			},
		},
		// {
		// 	label: ChangeTeam,
		// 	key: "change-team",
		// 	props: {
		// 		// onClick: () => changeTeamPopShow.value = true,
		// 		onClick: () => window.$aiwork.openSelectTeam?.(),
		// 	},
		// },
		// {
		// 	label: UserInfoContact,
		// 	key: "contact",
		// 	props: {
		// 		onClick: () => console.log("contact"),
		// 	}
		// },
		{
			key: "header-divider",
			type: "divider",
		},
		{
			label: UserInfoLogout,
			key: "logout",
			props: {
				onClick: () => {
					logout().finally(() => {
						window.localStorage.removeItem("SECRET_TOKEN");
						if (
							route.path.startsWith("/team") ||
							route.path.startsWith("/aiteam-landing")
						)
							router.go(0);
						else window.location.href = "/";
					});
				},
			},
		}
	);

	return options;
});

onMounted(() => {
	document.addEventListener("click", () => {
		changeTeamPopShow.value = false;
	});
});

const handleChangeTeam = (team) => {
	userStore.changeTeam(team.id);
	message.destroyAll();
	message.success("切换成功");
	changeTeamPopShow.value = false;
	changeTeamProvide();
};
const handleChangePersonal = () => {
	changeTeamPopShow.value = false;
	userStore.changeTeam(null);
	message.success("切换成功");
	changeTeamProvide();
};

const handleLogin = () => {
	// needPermission.value = true;
	window.$aiwork.openLogin();
};
const logout = () => {
	return request({ url: "/api3/aiwork/sign/logout", method: "GET" }).then(
		() => {
			userStore.resetUserInfo();
		}
	);
};
</script>

<style lang="less">
@import "@/components/common/Header1/index.less";
</style>
