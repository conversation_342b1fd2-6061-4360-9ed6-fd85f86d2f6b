import { NAvatar, <PERSON><PERSON><PERSON>, NIcon } from "naive-ui";
import { getUser } from "@/store/modules/auth/helper";
import MemberCardImage from "@/assets/aiwork/member-card.png";
import UserSvg from "@/assets/aiwork/svg/user.svg";
import HelperSvg from "@/assets/aiwork/svg/help.svg"
import MessageSvg from "@/assets/aiwork/svg/message.svg";
import LogoutSvg from "@/assets/aiwork/svg/logout.svg";
import defaultAvatar from "@/assets/avatar.jpg";
import ChangeSvg from '@/assets/aiwork/svg/change.1.svg'
import DistributionSvg from '@/assets/aiwork/svg/distribution.svg'


export const UserInfoHeader = ({ user }) => {
	return (
		<div class="flex gap-[7px] py-[10px] px-[15px] mb-[10px] w-[165px] items-center">
			<NAvatar src={user.avatar || defaultAvatar} size={30} round />
			<div>{user.name}</div>
		</div>
	);
};

export const UserInfoMember = () => {
	return (
		<div class="flex justify-center cursor-pointer mb-[10px]">
			<img src={MemberCardImage} width="135" height="30" />
		</div>
	);
};

// 分销
export const Distribution = () => (
	<div class="flex justify-start items-center cursor-pointer">
		<DistributionSvg class="w-[18px] h-[18px] mr-[14px]" />
		分销中心
	</div>
);

export const Help = () => (
	<div class="flex justify-start items-center cursor-pointer">
		<HelperSvg class="w-[18px] h-[18px] mr-[14px]" />
		帮助中心
	</div>
)

export const UserInfoCenter = () => (
	<div class="flex justify-start items-center cursor-pointer">
		<UserSvg class="w-[18px] h-[18px] mr-[14px]" />
		个人中心
	</div>
);

export const UserInfoContact = () => (
	<div class="flex justify-start items-center cursor-pointer">
		<MessageSvg class="w-[16px] h-[16px] mr-[14px]" />
		在线咨询
	</div>
);

export const UserInfoLogout = () => (
	<div class="flex justify-start items-center cursor-pointer">
		<LogoutSvg class="w-[18px] h-[18px] mr-[14px]" />
		退出登录
	</div>
);

export const ChangeTeam = () => (
	<div class="flex justify-start items-center cursor-pointer">
		<ChangeSvg class="w-[18px] h-[18px] mr-[14px]" />
		切换团队
	</div>
);
