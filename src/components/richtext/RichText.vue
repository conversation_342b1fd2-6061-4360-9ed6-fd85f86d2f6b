<template>
	<div class="h-full flex flex-col min-h-0">
		<div ref="$editor" class="h-full flex-1 editos bg-white flex flex-col min-h-0"></div>
		<div v-if="!loading && hasContent()" class="flex items-center gap-1 text-[12px] text-neutral-400 p-2 justify-center">
			<Info />
			<span>本内容由AI生成，仅供参考，不代表我方立场或观点，甄别使用。</span>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from "vue";
import { TextSelection } from "@tiptap/pm/state";
import { useMessage } from "naive-ui";

import { AiEditor } from "aieditor";
import "aieditor/dist/style.css";
import { customBubbleToolbar, customToolbar } from "./toolbars";

import { unified } from "unified";
import markdown from "remark-parse";
import docx from "remark-docx";
// import pdf from "remark-pdf";
import gfm from "remark-gfm";
import frontmatter from "remark-frontmatter";
// @ts-ignore
import { saveAs } from "file-saver";
import { getText, markdownParser } from "@/utils/utils";
import { Fragment } from "@tiptap/pm/model";
import { Info } from "@icon-park/vue-next";
enum ModeEnum {
	EXPAND = "expand",
	REWRITE = "rewrite",
	ABBREVIATE = "simplify",
	CONTINUE = "continue",
	NORMAL = "normal",
}
const message = useMessage();
const cursorPos = ref(0);
interface Props {
	placeholder?: string;
	content?: string;
	extraContent?: string;
	loading?: boolean;
	expand?: (content: string, type: ModeEnum) => void;
	rewrite?: (content: string, type: ModeEnum, style: string) => void;
	abbreviate?: (content: string, type: ModeEnum) => void;
	continue?: (content: string, type: ModeEnum) => void;
}
const $props = defineProps<Props>();

const $editor = ref<HTMLDivElement>();
const mkp = ref<markdownParser>();
// 默认是普通模式
const mode = ref(ModeEnum.NORMAL);
const from = ref(0);
const options = ref({
	markdownParseEnable: true,
	useMarkdownTextOnly: false,
});

let aiEditor: AiEditor;

const clearContent = () => {
	aiEditor.clear();
	aiEditor.focus();
};
const hasContent = () => {
	const contentLength = aiEditor?.getText?.().length;
	console.log("contentLength", contentLength);
	return contentLength > 0;
};
const handlerExportDocx = async () => {
	const processor = unified().use(markdown).use(docx, { output: "blob" });
	const doc = await processor.process(aiEditor.getMarkdown());
	const blob = await doc.result;
	saveAs(blob, "content.docx");
};
defineExpose({
	clearContent,
	hasContent,
	handlerExportDocx,
	getMarkdown: () => aiEditor.getMarkdown(), // 新增暴露getMarkdown方法
});
const contentDetected = (warningText: string) => {
	const content = aiEditor.getSelectedText();
	if (!content) {
		message.error(warningText);
		return;
	}
	return content;
};
const getLastSentenceBeforeCursor = () => {
	const { from } = aiEditor.innerEditor.state.selection;
	const docContent = aiEditor.innerEditor.state.doc.textBetween(
		0,
		from,
		"\n",
		" "
	);
	// 使用正则表达式分割句子
	const sentences = docContent.split(/[.!?。！？]+/);
	for (let i = sentences.length - 1; i >= 0; i--) {
		const sentence = sentences[i].trim();
		if (sentence) {
			return sentence;
		}
	}
	return ""; // 如果没有找到有效句子，返回空字符串
};

const clearSelectionContent = () => {
	const { ranges } = aiEditor.innerEditor.state.selection;
	const { $from, $to } = ranges?.[0];
	cursorPos.value = $from.pos;
	// 删除选中的内容
	aiEditor.innerEditor.commands.deleteSelection();
};
const handleExpandContent = () => {
	const content = contentDetected("请先选择需要扩展的内容");
	if (!content) return;
	from.value = aiEditor.innerEditor.view.state.selection.from;
	mode.value = ModeEnum.EXPAND;
	clearSelectionContent();
	$props.expand?.(content, ModeEnum.EXPAND);
};
const handleRewriteContent = () => {
	const content = contentDetected("请先选择需要重写的内容");
	if (!content) return;
	from.value = aiEditor.innerEditor.view.state.selection.from;

	mode.value = ModeEnum.REWRITE;
	clearSelectionContent();
	$props.rewrite?.(content, ModeEnum.REWRITE, "正式得体");
};
const handleAbbreviateContent = () => {
	const content = contentDetected("请先选择需要缩写的内容");
	if (!content) return;
	from.value = aiEditor.innerEditor.view.state.selection.from;
	mode.value = ModeEnum.ABBREVIATE;
	clearSelectionContent();
	$props.abbreviate?.(content, ModeEnum.ABBREVIATE);
};

const handleContinueContent = () => {
	mode.value = ModeEnum.CONTINUE;
	const content = getLastSentenceBeforeCursor();
	if (!content) {
		message.error("还没有内容可以续写, 请先输入内容");
		return;
	}
	from.value = aiEditor.innerEditor.view.state.selection.from;
	$props.continue?.(content, ModeEnum.CONTINUE);
};
const handleRedoContent = (editor) => {
	editor.innerEditor.commands.redo();
};
const handleUndoContent = (editor) => {
	editor.innerEditor.commands.undo();
};

const toolbarFormat = (name: keyof typeof customToolbar) => {
	const custom = customToolbar[name];
	const handler = (event, editor) => {
		switch (name) {
			case "expand":
				return handleExpandContent();
			case "rewrite":
				return handleRewriteContent();
			case "abbreviate":
				return handleAbbreviateContent();
			case "redo":
				return handleRedoContent(editor);
			case "undo":
				return handleUndoContent(editor);
			case "continue":
			default:
				return handleContinueContent();
		}
	};

	return {
		...custom,
		onClick: handler,
	};
};

const handleSpecialRewriteContent = () => {
	const content = contentDetected("请先选择需要重写的内容");
	if (!content) return;
	mode.value = ModeEnum.REWRITE;
	clearSelectionContent();
	$props.rewrite?.(content, ModeEnum.REWRITE, window.$temptype || "正式得体");
};
const bubbleFormat = (name: keyof typeof customToolbar) => {
	const custom = customBubbleToolbar[name];
	const handler = (editor, event, event2, event3) => {
		switch (name) {
			case "expand":
				return handleExpandContent();
			case "rewrite":
				return handleSpecialRewriteContent();
			case "abbreviate":
				return handleAbbreviateContent();
			case "continue":
			default:
				return handleContinueContent();
		}
	};

	return {
		...custom,
		onClick: handler,
	};
};

const toolbarKeys = [
	"bold",
	"italic",
	"underline",
	"strike",
	toolbarFormat("expand"),
	toolbarFormat("rewrite"),
	toolbarFormat("abbreviate"),
	toolbarFormat("continue"),
	toolbarFormat("undo"),
	toolbarFormat("redo"),
	// "link",
	// "emoji",
	// "image",
	// "quote",
	// "|",
	// "undo",
	// "redo",
	// "|",
	// toolbarFormat("exportDocx"),
	// toolbarFormat("exportPdf"),
];
const bubbleItems = [
	"Bold",
	"Italic",
	"Underline",
	"Strike",
	bubbleFormat("expand"),
	bubbleFormat("rewrite"),
	bubbleFormat("abbreviate"),
	// bubbleFormat("continue"),
];

onMounted(() => {
	aiEditor = new AiEditor({
		element: $editor.value as Element,
		placeholder: $props.placeholder || "",
		content: $props.content || "",
		toolbarKeys,
		textSelectionBubbleMenu: {
			enable: true,
			items: bubbleItems,
		},
	});
	mkp.value = new markdownParser(aiEditor.innerEditor.schema);
});
onUnmounted(() => {
	aiEditor && aiEditor.destroy();
});
watch(
	() => $props.loading,
	(newVal) => {
		// if (newVal) {
		// 	aiEditor.setEditable(false);
		// } else {
		// 	aiEditor.setEditable(true);
		// }
	}
);
// 这里需要重构一下 不需要每次都清空整个编辑器
watch(
	() => $props.content,
	(newVal, oldVal) => {
		try {
			// 判断文本结尾是-或者数字.比如1. 2. 3.这样的情况下
			// 给文本结尾加上思考...
			/**
			 * 比如
			 * 1. 施工人员必须佩戴安全帽、安全带等个人防护用品。
			2. 设立警示标志，禁止无关人员进入施工现场。
			3.

			1. 基础施工
				- 按照设计要求开挖基础坑槽，确保尺寸和深度符合标准。
				-

			这2种情况就需要加上思考...
			 */
			const s = newVal as string;
			const formatContent =
				s.slice(-2).trim() === "-" || /^\d+\.$/.test(s.slice(-3).trim())
					? newVal + " 思考中..."
					: newVal;
			if (!aiEditor.isFocused()) {
				aiEditor.focus().clear().insert(formatContent);
			} else {
				aiEditor.clear().insert(formatContent);
			}
			// aiEditor.innerEditor.commands.scrollIntoView();
			const from = aiEditor.innerEditor.view.state.selection.from;
			const end = aiEditor.innerEditor.state.selection.to;
			const insertedText = aiEditor.innerEditor.state.doc.textBetween(
				from,
				end
			);
			const {
				state: { tr },
				view,
			} = aiEditor.innerEditor;
			const parseMarkdown = aiEditor.innerEditor.parseMarkdown(insertedText);
			view.dispatch(tr.replaceWith(from, end, parseMarkdown).scrollIntoView());
			nextTick(() => {
				const { view } = aiEditor.innerEditor;
				const end = view.state.doc.content.size;
				const tr = view.state.tr;
				tr.setSelection(TextSelection.create(tr.doc, end));
				tr.scrollIntoView();
				console.log("滚动到最底部");
				view.dispatch(tr);
			});
		} catch (error) {
			console.log("value: ", newVal);
		}
	}
);

watch(
	() => $props.extraContent,
	(newVal) => {
		try {
			aiEditor.insert(newVal as string);
			const end = aiEditor.innerEditor.state.selection.to;
			const insertedText = aiEditor.innerEditor.state.doc.textBetween(
				from.value,
				end
			);
			console.log("extraContent: ", insertedText);
			const {
				state: { tr },
				view,
			} = aiEditor.innerEditor;
			const parseMarkdown = aiEditor.innerEditor.parseMarkdown(insertedText);
			view.dispatch(
				tr.replaceWith(from.value, end, parseMarkdown).scrollIntoView()
			);

		} catch (error) {}
		// 确保内容插入后滚动到底部
		// nextTick(() => {
		// 	const { view } = aiEditor.innerEditor;
		// 	const end = view.state.doc.content.size;
		// 	const tr = view.state.tr;
		// 	tr.setSelection(TextSelection.create(tr.doc, end));
		// 	tr.scrollIntoView();
		// 	console.log("滚动到最底部");

		// 	view.dispatch(tr);
		// });
	}
);

const handlerExportPdf = async () => {
	const processor = unified().use(markdown).use(gfm);
	// .use(frontmatter).use(pdf, { output: "blob" });
	const doc = await processor.process(aiEditor.getMarkdown());
	const blob = await doc.result;
	saveAs(blob, "content.pdf");
};
</script>

<style lang="less">
.editos {
	ul,
	ol {
		list-style: revert;
	}

	.bytemd {
		background-color: transparent;
		height: 100%;
	}

	.aie-container,
	// .aie-container aie-header,
	.markdown-body {
		background-color: transparent;
	}

	aie-footer {
		display: none !important;
	}

	.aie-container {
		height: 100%;
		display: flex;
		flex-direction: column;
		border: none;
		background-color: #f9faff;
		overflow: hidden;
	}

	.aie-container aie-header {
		position: sticky;
		top: 0;
		z-index: 10;
		background: white;
		flex-grow: 0;
		flex-shrink: 0;
		border-bottom: 1px solid #f0f0f0;

		> div {
			padding-left: 25px;
		}
	}

	.aie-container {
		flex: 1;
		display: flex;
		flex-direction: column;
		min-height: 0;
		height: 100%;
		> div[aria-expanded="false"] {
			// height: 80vh;
			flex: 1;
			overflow-y: auto;
		}
		> div {
			&:where(:first-of-type) {
				background-color: #fff;
				flex: 1;
				// min-height: 80vh;
			}
		}
	}

	.aie-content {
		padding: 15px;
		padding-bottom: 50px;
		box-sizing: border-box;
		flex: 1;
		min-height: 0;
		overflow-y: auto;

		> * {
			max-width: 80%;
			margin-left: auto;
			margin-right: auto;
		}
		> p {
			font-size: 17px;
			line-height: 1.6;
		}
	}

	.tiptap .ProseMirror .aie-content {
		-ms-overflow-style: none;
		scrollbar-width: none;
	}

	.bubble {
		.bubble-list {
			box-shadow: 0px 1px 10px 0px #e1e9ff;
			display: none;
		}

		&:hover {
			.bubble-list {
				display: flex;
			}
		}
	}
}
</style>
