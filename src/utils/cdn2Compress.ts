export const cdn2Compress = (url: string, options: { quality?: number , width?: number, height?: number }) => {
	try {
		if(!url) return url
		if (!/(https?:)\/\/(cdn2\.weimob\.com|image-c-dev\.weimobwmc\.com||image-c\.weimobwmc\.com)/.test(url)) return url
		const { quality = 70, width = 0, height = 0 } = options
		let str = 'imageView2/2'
		if(width) str += `/w/${width}`
		if(height) str += `/h/${height}`
		if(quality) str += `/q/${Math.floor(quality)}!`
		return /\?/.test(url) ? url.replace(/\?/, `?${str}&`) : `${url}?${str}`
	} catch (error) {
		console.warn(url, error)
		return url
	}
}
