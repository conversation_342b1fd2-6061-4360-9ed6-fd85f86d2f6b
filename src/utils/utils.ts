import { NImage } from "naive-ui";
import { h } from "vue";
import * as pdfjsLib from "pdfjs-dist";
import { MarkdownParser } from "@tiptap/pm/markdown";
import { Schema } from "@tiptap/pm/model";
import MarkdownIt from "markdown-it";
import { RouteLocationNormalizedLoaded } from "vue-router";
import { OutlineData, OutlineNode, TreeNodes } from "@/views/ppt/types";

// Setting worker path to worker bundle.
export const searchTreeMenu = (keyword: string, list: any) => {
	if (!list) return;
	return list.filter((item: any) => {
		// 检查当前节点的名称是否包含关键词
		const itemName = item.name.toLowerCase();
		const searchTerm = keyword.toLowerCase();

		if (itemName.includes(searchTerm)) {
			return true;
		}

		// 如果当前节点有子节点,则递归调用搜索函数进行子节点的匹配
		if (item.Chatbots) {
			const matchedChildren = searchTreeMenu(keyword, item.Chatbots);
			if (matchedChildren.length > 0) {
				item.Chatbots = matchedChildren;
				return true;
			}
		}

		return false;
	});
};
export const formatChild = (list: any) => {
	let newList: any[] = [];
	for (let item of list) {
		if (item?.Createbots) {
			for (let mp of item?.Createbots) {
				const index = newList.findIndex((item) => item.id === mp.id);
				if (index == -1) {
					newList.push(mp);
				}
			}
		}
	}
	return newList;
};

export function renderIcon(image: any) {
	return () =>
		h(
			NImage,
			{ src: image, previewDisabled: true },
			{ default: () => h(image) }
		);
}

export const getText = (node: any) => {
	let text = "";
	node.descendants((node) => {
		if (node.text) {
			text += node.text;
		}
	});
	return text;
};

// // 设置 worker 路径（这是必需的）
// pdfjsLib.GlobalWorkerOptions.workerSrc = "pdf.worker.mjs";

Promise.all([
	// @ts-ignore: only accepted in commonJs or esNext
	import(/* webpackChunkName: "PDFJS" */ "pdfjs-dist/build/pdf.min.mjs"),
]).then(() => {
	// pdfjsLib.GlobalWorkerOptions.workerSrc = "assets/pdf.worker.min.mjs";
	// Alternatively, load from externalcdn
	pdfjsLib.GlobalWorkerOptions.workerSrc =
		"//unpkg.com/pdfjs-dist@4.6.82/build/pdf.worker.min.mjs";
});

export function readPdfContent(arrayBuffer: ArrayBuffer): Promise<string> {
	return new Promise((resolve, reject) => {
		pdfjsLib
			.getDocument(new Uint8Array(arrayBuffer))
			.promise.then((pdf: any) => {
				const numPages = pdf.numPages;
				console.log(`PDF loaded. It has ${numPages} page(s).`);

				let fullText = "";
				// 使用 Promise.all 同时处理所有页面
				const pagePromises: Promise<string>[] = [];
				for (let i = 1; i <= numPages; i++) {
					const t = getPageText(pdf, i);
					pagePromises.push(t);
				}

				Promise.all(pagePromises)
					.then((pageTexts) => {
						fullText = pageTexts.join(" ");
						console.log("Full PDF content:");
						console.log(fullText);
						resolve(fullText);
					})
					.catch(reject);
			})
			.catch(reject);
	});
}

async function getPageText(pdf: any, pageNumber: number) {
	return new Promise<string>((resolve, reject) => {
		pdf
			.getPage(pageNumber)
			.then((page: any) => {
				page
					.getTextContent()
					.then((textContent: any) => {
						const pageText = textContent.items
							.map((item: any) => item.str)
							.join(" ");
						console.log(`Page ${pageNumber} content:`);
						console.log(pageText);
						resolve(pageText);
					})
					.catch(reject);
			})
			.catch(reject);
	});
}

// 使用示例
async function processPdf(arrayBuffer) {
	try {
		const content = await readPdfContent(arrayBuffer);
		console.log("PDF content has been processed and printed to the console.");
		return content;
	} catch (error) {
		console.error("Error reading PDF:", error);
		throw error;
	}
}
function listIsTight(tokens: any, i: any) {
	while (++i < tokens.length)
		if (tokens[i].type != "list_item_open") return tokens[i].hidden;
	return false;
}
export class markdownParser {
	parser: MarkdownParser;
	constructor(schema: Schema) {
		this.parser = new MarkdownParser(
			schema,
			MarkdownIt("commonmark", { html: false }),
			{
				blockquote: { block: "blockquote" },
				paragraph: { block: "paragraph" },
				list_item: { block: "listItem" },
				list_item_open: { block: "listItem" },
				bullet_list: {
					block: "bulletList",
					getAttrs: (_, tokens, i) => ({ tight: listIsTight(tokens, i) }),
				},
				ordered_list: {
					block: "orderedList",
					getAttrs: (tok, tokens, i) => ({
						order: +(tok.attrGet("start") || 1),
						tight: listIsTight(tokens, i),
					}),
				},
				ordered_list_open: {
					block: "orderedList",
					getAttrs: (tok) => ({ level: +tok.tag.slice(1) }),
				},
				heading: {
					block: "heading",
					getAttrs: (tok) => ({ level: +tok.tag.slice(1) }),
				},
				code_block: {
					block: "codeBlock",
					getAttrs: (tok) => {
						return { language: tok.info || "" };
					},
					noCloseToken: true,
				},
				fence: {
					block: "codeBlock",
					getAttrs: (tok) => {
						return { language: tok.info || "" };
					},
					noCloseToken: true,
				},
				hr: { node: "horizontalRule" },
				image: {
					node: "image",
					getAttrs: (tok) => ({
						src: tok.attrGet("src"),
						title: tok.attrGet("title") || null,
						alt:
							(tok.children && tok.children[0] && tok.children[0].content) ||
							null,
					}),
				},
				hardbreak: { node: "hardBreak" },
				em: { mark: "em" },
				strong: { mark: "strong" },
				link: {
					mark: "link",
					getAttrs: (tok) => ({
						href: tok.attrGet("href"),
						title: tok.attrGet("title") || null,
					}),
				},
				code_inline: { mark: "code", noCloseToken: true },
			}
		);
	}
}

export const renderOptions = (opts: string[]) => {
	let options: any[] = [];
	opts.map((item) => {
		options.push({
			label: item,
			value: item,
		});
	});
	return options;
};

// 判断展示哪个充值页面
export const judgeRechargeTmp = (route: RouteLocationNormalizedLoaded) => {
  let categoryId = 1;
  switch (route?.name) {
		case "PPT":
		case "PPTGenerate":
			categoryId = 2;
			break;
		case "Paper":
			categoryId = 3;
			break;
		default:
			categoryId = 1;
			break;
	}
	// @ts-ignore
	window.$aiwork?.openRecharge?.({ type: "ai", categoryId });
};

export const readFileBuffer = (file: File): Promise<ArrayBuffer> => {
	return new Promise((resolve, reject) => {
		const reader = new FileReader();
		reader.onload = () => {
			if (reader.result) {
				resolve(reader.result as ArrayBuffer);
			} else {
				reject(new Error("Failed to read file buffer"));
			}
		};
		reader.onerror = () => {
			reject(new Error("Error reading file"));
		};
		reader.readAsArrayBuffer(file);
	});
};
export function transformOutline(nodes: TreeNodes[][]): OutlineData & { nodes: OutlineNode[] } {
	const titleNodes = nodes[0]
	const contentNodes = nodes[1]

	const title = titleNodes.find(item => item.type === 'title')?.title
	const subTitle = titleNodes.find(item => item.type === 'subTitle')?.title

	return {
		title,
		subTitle,
		nodes: contentNodes.map(item => ({
			type: item.type,
			title: item.title,
			chapterNum: item.chapterNum,
			paragraphNum: item.paragraphNum
		}))
	}
}
export const getRandomValues = (length: number) => crypto.getRandomValues(new Uint8Array(length));

export const generateId = (length: number = 21) => getRandomValues(length).reduce((id, value) => {
	value &= 63;
	if (value < 36) {
		id += value.toString(36);
	}
	else if (value < 62) {
		id += (value - 26).toString(36).toUpperCase();
	}
	else {
		id += value > 62 ? "-" : "_";
	}
	return id;
}, "");
export function transformNodes(props: OutlineData, nodes: OutlineNode[]): TreeNodes[][] {
	const titleNodes: TreeNodes[] = []
	const contentNodes: TreeNodes[] = []

	if (props.title) {
		titleNodes.push({
			id: generateId(),
			label: '标题',
			depth: 0,
			chapterNum: 1,
			type: 'title',
			title: props.title
		})
	}

	if (props.subTitle) {
		titleNodes.push({
			id: generateId(),
			label: '副标题',
			depth: 0,
			chapterNum: 1,
			type: 'subTitle',
			title: props.subTitle
		})
	}

	for (const node of nodes) {
		contentNodes.push({
			id: generateId(),
			type: node.type,
			title: node.title,
			chapterNum: node.chapterNum,
			paragraphNum: node.paragraphNum ? node.paragraphNum : undefined,
			label: node.type === 'chapter' ? '章节' : ''
		})
	}

	return [titleNodes, contentNodes]




	// const result: TreeNodes[] = [];
	// let currentChapter: TreeNodes | null = null;

	// if (props.title && props.subTitle) {
	//   result.push({
	//     id: generateId(),
	//     text: props.title,
	//     label: '标题',
	//     depth: 0,
	//     parent: ''
	//   })
	//   result.push({
	//     id: generateId(),
	//     text: props.subTitle,
	//     label: '副标题',
	//     depth: 0,
	//     parent: ''
	//   });
	// } else {
	//   result.push({
	//     id: generateId(),
	//     text: props.title as string,
	//     label: '标题',
	//     depth: 0,
	//     parent: ''
	//   });
	// }
	// for (const node of nodes) {
	//   const treeNode: TreeNodes = {
	//     id: generateId(),
	//     text: node.title,
	//     label: node.type === 'chapter' ? '章节' : '',
	//     depth: node.type === 'chapter' ? 2 : 3,
	//     parent: node.type === 'chapter' ? currentChapter?.id : ''
	//   };
	//   result.push(treeNode);
	// }

	// return result;
}
