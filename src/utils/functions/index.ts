import { riskControl } from '@/chatgpt'
import { useAuthStore } from '@/store'

export function getCurrentDate() {
  const date = new Date()
  const day = date.getDate()
  const month = date.getMonth() + 1
  const year = date.getFullYear()
  return `${year}-${month}-${day}`
}
export function captchaInit(captchaAppId: string, callback: any) {
  const captcha = new (window as any).TencentCaptcha(captchaAppId, function (res: any) {
    if (res && res.ticket) {
      callback &&
        callback({
          ticket: res.ticket,
          randstr: res.randstr,
        })
    } else {
      callback &&
        callback({
          errcode: '1051',
          message: '请完成拼图！',
          errmsg: '请完成拼图！',
        })
    }
  })
  captcha.show()
}

// 检车频控拼图
export const postRiskControl = async (type: 'apps'): Promise<{ ticket: string; randstr: string }> => {
  try {
    const isCaptcha: any = await riskControl(type)
    // 没有触发频控
    if (!isCaptcha.captcha) true

    // 触发频控
    const res = await new Promise((resolve, reject) => {
      captchaInit(useAuthStore().captchaAppId, async (captchaData: any) => {
        if (captchaData && captchaData.randstr) resolve(captchaData)
        else reject(captchaData)
      })
    })
    return res as { ticket: string; randstr: string }
  } catch (error) {
    throw error
  }
}

export function downloadIamge(imgSrc: string, fileName?: string) {
  const xhr = new XMLHttpRequest()
  xhr.open('GET', imgSrc)
  xhr.responseType = 'blob'
  xhr.send()
  xhr.onload = function () {
    const fileBlob = xhr.response
    const fileUrl = URL.createObjectURL(fileBlob)
    console.log(fileUrl)
    const ele = document.createElement('a')
    ele.setAttribute('href', fileUrl)
    ele.setAttribute('download', '')
    ele.click()
    ele.innerHTML = '下载'
    document.body.appendChild(ele)
  }
}
